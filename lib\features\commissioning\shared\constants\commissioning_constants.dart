/// Constants specific to commissioning operations
class CommissioningConstants {
  // Discovery timeouts
  static const int bluetoothScanTimeoutSeconds = 30;
  static const int wifiScanTimeoutSeconds = 15;
  static const int qrScanTimeoutSeconds = 60;
  static const int connectionTimeoutSeconds = 30;

  // Network configuration
  static const int networkTestTimeoutSeconds = 10;
  static const int ntpSyncTimeoutSeconds = 15;
  static const String defaultNtpServer = 'pool.ntp.org';
  static const int defaultMtu = 1500;

  // OCPP configuration
  static const int ocppConnectionTimeoutSeconds = 30;
  static const int ocppHeartbeatIntervalSeconds = 300; // 5 minutes
  static const int ocppReconnectDelaySeconds = 10;
  static const int maxOcppReconnectAttempts = 5;
  static const String defaultOcppVersion = '2.0.1';

  // Firmware management
  static const int firmwareDownloadTimeoutMinutes = 30;
  static const int firmwareInstallTimeoutMinutes = 15;
  static const int firmwareVerificationTimeoutSeconds = 60;
  static const String firmwareChecksumAlgorithm = 'SHA256';

  // Parameter configuration
  static const int parameterSetTimeoutSeconds = 10;
  static const int parameterGetTimeoutSeconds = 5;
  static const double minChargingCurrent = 6.0; // Amperes
  static const double maxChargingCurrent = 80.0; // Amperes
  static const double minChargingPower = 3.0; // kW
  static const double maxChargingPower = 350.0; // kW

  // Diagnostics
  static const int diagnosticsTimeoutSeconds = 30;
  static const int logRetrievalTimeoutSeconds = 60;
  static const int maxLogEntries = 1000;
  static const List<String> defaultLogLevels = ['ERROR', 'WARN', 'INFO', 'DEBUG'];

  // Compliance (Indian standards)
  static const String indianStandardIS17017_1 = 'IS 17017-1';
  static const String indianStandardIS17017_2 = 'IS 17017-2';
  static const double maxIndianGridVoltage = 440.0; // Volts
  static const double minIndianGridVoltage = 380.0; // Volts
  static const double indianGridFrequency = 50.0; // Hz

  // Workflow configuration
  static const int maxWorkflowSteps = 20;
  static const int workflowTimeoutHours = 4;
  static const int maxRetryAttempts = 3;
  static const int retryDelaySeconds = 5;

  // File and data limits
  static const int maxLogFileSize = 10 * 1024 * 1024; // 10 MB
  static const int maxReportFileSize = 50 * 1024 * 1024; // 50 MB
  static const int maxConfigurationSize = 1024 * 1024; // 1 MB
  static const List<String> supportedExportFormats = ['json', 'xml', 'pdf', 'csv'];

  // Security
  static const int certificateValidityDays = 365;
  static const int sessionTimeoutMinutes = 60;
  static const int maxFailedAttempts = 3;
  static const int lockoutDurationMinutes = 15;

  // UI configuration
  static const int progressUpdateIntervalMs = 500;
  static const int statusRefreshIntervalSeconds = 5;
  static const int alertDisplayDurationSeconds = 5;
  static const double minSignalStrength = -80.0; // dBm

  // Mock data configuration (for testing)
  static const bool useMockServices = true;
  static const int mockOperationDelayMs = 1000;
  static const double mockSuccessRate = 0.9; // 90% success rate for mock operations

  // Charger types and capabilities
  static const Map<String, List<String>> chargerTypeCapabilities = {
    'ac_type1': ['single_phase', 'basic_charging'],
    'ac_type2': ['single_phase', 'three_phase', 'smart_charging'],
    'dc_ccs': ['fast_charging', 'ultra_fast_charging', 'smart_charging'],
    'dc_chademo': ['fast_charging', 'bidirectional_charging'],
    'dc_combo': ['fast_charging', 'ultra_fast_charging', 'smart_charging'],
  };

  // Default parameter values
  static const Map<String, dynamic> defaultChargerParameters = {
    'max_current': 32.0,
    'max_power': 22.0,
    'voltage': 230.0,
    'phases': 1,
    'frequency': 50.0,
    'power_factor': 0.95,
    'heartbeat_interval': 300,
    'meter_values_sample_interval': 60,
    'clock_aligned_data_interval': 900,
    'connection_timeout': 30,
    'retry_attempts': 3,
    'reset_retries': 3,
    'unlock_connector_on_ev_side_disconnect': true,
    'local_authorize_offline': true,
    'local_pre_authorize': false,
    'allow_offline_tx_for_unknown_id': false,
    'authorization_cache_enabled': true,
    'stop_transaction_on_ev_side_disconnect': true,
    'stop_transaction_on_invalid_id': true,
  };

  // Error messages
  static const Map<String, String> errorMessages = {
    'discovery_timeout': 'Charger discovery timed out. Please check if the charger is powered on and in pairing mode.',
    'connection_failed': 'Failed to connect to charger. Please check the connection and try again.',
    'authentication_failed': 'Authentication failed. Please verify the credentials and try again.',
    'network_config_failed': 'Network configuration failed. Please check the Wi-Fi settings and try again.',
    'ocpp_connection_failed': 'OCPP connection failed. Please verify the CMS URL and credentials.',
    'firmware_update_failed': 'Firmware update failed. Please check the firmware file and try again.',
    'parameter_config_failed': 'Parameter configuration failed. Please check the parameter values and try again.',
    'diagnostics_failed': 'Diagnostics test failed. Please check the charger status and try again.',
    'compliance_check_failed': 'Compliance check failed. The charger does not meet the required standards.',
    'workflow_timeout': 'Commissioning workflow timed out. Please restart the process.',
  };

  // Success messages
  static const Map<String, String> successMessages = {
    'discovery_success': 'Charger discovered successfully.',
    'connection_success': 'Connected to charger successfully.',
    'authentication_success': 'Authentication completed successfully.',
    'network_config_success': 'Network configuration completed successfully.',
    'ocpp_connection_success': 'OCPP connection established successfully.',
    'firmware_update_success': 'Firmware update completed successfully.',
    'parameter_config_success': 'Parameter configuration completed successfully.',
    'diagnostics_success': 'Diagnostics test completed successfully.',
    'compliance_check_success': 'Compliance check passed successfully.',
    'workflow_complete': 'Commissioning completed successfully.',
  };

  // Validation patterns
  static const Map<String, String> validationPatterns = {
    'serial_number': r'^[A-Z0-9]{8,20}$',
    'charger_id': r'^[A-Za-z0-9_-]{4,50}$',
    'ssid': r'^.{1,32}$',
    'ip_address': r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$',
    'mac_address': r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$',
    'url': r'^https?:\/\/.+',
    'version': r'^\d+\.\d+(\.\d+)?$',
  };
}
