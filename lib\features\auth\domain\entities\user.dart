import 'package:equatable/equatable.dart';

/// User entity representing authenticated user
class User extends Equatable {
  const User({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
    required this.isActive,
    this.phoneNumber,
    this.profileImageUrl,
    this.lastLoginAt,
    this.createdAt,
    this.updatedAt,
    this.permissions = const [],
    this.organizationId,
    this.organizationName,
  });

  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final UserRole role;
  final bool isActive;
  final String? phoneNumber;
  final String? profileImageUrl;
  final DateTime? lastLoginAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<String> permissions;
  final String? organizationId;
  final String? organizationName;

  String get fullName => '$firstName $lastName';
  String get initials => '${firstName.isNotEmpty ? firstName[0] : ''}${lastName.isNotEmpty ? lastName[0] : ''}';

  bool hasPermission(String permission) => permissions.contains(permission);

  bool get canCommissionChargers => role == UserRole.admin || role == UserRole.technician;
  bool get canManageUsers => role == UserRole.admin;
  bool get canViewReports => true; // All users can view reports
  bool get canGenerateReports => role == UserRole.admin || role == UserRole.operator;

  @override
  List<Object?> get props => [
        id,
        email,
        firstName,
        lastName,
        role,
        isActive,
        phoneNumber,
        profileImageUrl,
        lastLoginAt,
        createdAt,
        updatedAt,
        permissions,
        organizationId,
        organizationName,
      ];
}

/// User roles in the system
enum UserRole {
  admin('Admin'),
  operator('Operator'),
  technician('Technician');

  const UserRole(this.displayName);

  final String displayName;

  static UserRole fromString(String value) {
    switch (value.toLowerCase()) {
      case 'admin':
        return UserRole.admin;
      case 'operator':
        return UserRole.operator;
      case 'technician':
        return UserRole.technician;
      default:
        throw ArgumentError('Invalid user role: $value');
    }
  }
}

/// Authentication tokens
class AuthTokens extends Equatable {
  const AuthTokens({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    this.tokenType = 'Bearer',
  });

  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final String tokenType;

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isExpiringSoon => DateTime.now().add(const Duration(minutes: 5)).isAfter(expiresAt);

  @override
  List<Object?> get props => [accessToken, refreshToken, expiresAt, tokenType];
}

/// Login credentials
class LoginCredentials extends Equatable {
  const LoginCredentials({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  final String email;
  final String password;
  final bool rememberMe;

  @override
  List<Object?> get props => [email, password, rememberMe];
}

/// Biometric authentication data
class BiometricAuthData extends Equatable {
  const BiometricAuthData({
    required this.isAvailable,
    required this.isEnabled,
    required this.supportedTypes,
  });

  final bool isAvailable;
  final bool isEnabled;
  final List<BiometricType> supportedTypes;

  @override
  List<Object?> get props => [isAvailable, isEnabled, supportedTypes];
}

/// Supported biometric types
enum BiometricType {
  fingerprint('Fingerprint'),
  face('Face ID'),
  iris('Iris'),
  voice('Voice');

  const BiometricType(this.displayName);

  final String displayName;
}
