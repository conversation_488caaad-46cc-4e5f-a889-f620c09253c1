import 'package:logger/logger.dart';

/// Application logger service
class AppLogger {
  AppLogger(this._logger);

  final Logger _logger;

  /// Log debug message
  void debug(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log info message
  void info(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning message
  void warning(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error message
  void error(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal error message
  void fatal(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log network request
  void logNetworkRequest(
    String method,
    String url,
    Map<String, dynamic>? data,
  ) {
    info('🌐 $method $url', data);
  }

  /// Log network response
  void logNetworkResponse(
    String method,
    String url,
    int statusCode,
    dynamic data,
  ) {
    if (statusCode >= 200 && statusCode < 300) {
      info('✅ $method $url - $statusCode', data);
    } else {
      error('❌ $method $url - $statusCode', data);
    }
  }

  /// Log user action
  void logUserAction(String action, Map<String, dynamic>? context) {
    info('👤 User Action: $action', context);
  }

  /// Log charger operation
  void logChargerOperation(
    String operation,
    String chargerId, [
    Map<String, dynamic>? details,
  ]) {
    info('🔌 Charger Operation: $operation for $chargerId', details);
  }

  /// Log commissioning step
  void logCommissioningStep(
    String step,
    String chargerId,
    bool success, [
    String? errorMessage,
  ]) {
    if (success) {
      info('✅ Commissioning: $step completed for $chargerId');
    } else {
      error('❌ Commissioning: $step failed for $chargerId', errorMessage);
    }
  }

  /// Log security event
  void logSecurityEvent(String event, Map<String, dynamic>? context) {
    warning('🔒 Security Event: $event', context);
  }

  /// Log performance metric
  void logPerformance(
    String operation,
    Duration duration, [
    Map<String, dynamic>? context,
  ]) {
    info(
      '⏱️ Performance: $operation took ${duration.inMilliseconds}ms',
      context,
    );
  }

  /// Log app lifecycle event
  void logAppLifecycle(String event) {
    info('📱 App Lifecycle: $event');
  }

  /// Log database operation
  void logDatabaseOperation(
    String operation,
    String table, [
    Map<String, dynamic>? context,
  ]) {
    debug('🗄️ Database: $operation on $table', context);
  }

  /// Log file operation
  void logFileOperation(
    String operation,
    String filePath,
    bool success, [
    String? errorMessage,
  ]) {
    if (success) {
      debug('📁 File: $operation successful for $filePath');
    } else {
      error('📁 File: $operation failed for $filePath', errorMessage);
    }
  }
}
