import '../../../../core/utils/typedef.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';

/// Mock implementation of AuthRepository for development
class AuthRepositoryImpl implements AuthRepository {
  @override
  FutureResult<AuthTokens> login(LoginCredentials credentials) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 2));

      // Mock validation - accept any email/password for now
      if (credentials.email.isNotEmpty && credentials.password.isNotEmpty) {
        final tokens = AuthTokens(
          accessToken:
              'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
          refreshToken:
              'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
          expiresAt: DateTime.now().add(const Duration(hours: 1)),
        );
        return Result.success(tokens);
      } else {
        return const Result.failure(
          AuthFailure(
            message: 'Invalid credentials',
            code: 'INVALID_CREDENTIALS',
          ),
        );
      }
    } catch (e) {
      return Result.failure(
        AuthFailure(message: 'Login failed: $e', code: 'LOGIN_ERROR'),
      );
    }
  }

  @override
  FutureResult<void> logout() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        AuthFailure(message: 'Logout failed: $e', code: 'LOGOUT_ERROR'),
      );
    }
  }

  @override
  FutureResult<AuthTokens> refreshTokens(String refreshToken) async {
    try {
      await Future.delayed(const Duration(seconds: 1));

      final tokens = AuthTokens(
        accessToken:
            'refreshed_access_token_${DateTime.now().millisecondsSinceEpoch}',
        refreshToken:
            'refreshed_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );
      return Result.success(tokens);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Token refresh failed: $e',
          code: 'TOKEN_REFRESH_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<User> getCurrentUser() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final user = User(
        id: 'mock_user_id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.technician,
        isActive: true,
        phoneNumber: '+1234567890',
        lastLoginAt: DateTime.now(),
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        permissions: ['commission_chargers', 'view_reports'],
        organizationId: 'org_123',
        organizationName: 'EV Solutions Inc.',
      );

      return Result.success(user);
    } catch (e) {
      return Result.failure(
        AuthFailure(message: 'Failed to get user: $e', code: 'GET_USER_ERROR'),
      );
    }
  }

  @override
  FutureResult<User> updateProfile(User user) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      return Result.success(user);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Profile update failed: $e',
          code: 'PROFILE_UPDATE_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<void> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Password change failed: $e',
          code: 'PASSWORD_CHANGE_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<void> requestPasswordReset(String email) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Password reset request failed: $e',
          code: 'PASSWORD_RESET_REQUEST_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<void> resetPassword(String token, String newPassword) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Password reset failed: $e',
          code: 'PASSWORD_RESET_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<bool> isAuthenticated() async {
    try {
      // For now, always return false to show login screen
      return const Result.success(false);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Authentication check failed: $e',
          code: 'AUTH_CHECK_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<AuthTokens?> getStoredTokens() async {
    try {
      // Mock implementation - return null for now
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        CacheFailure(
          message: 'Failed to get stored tokens: $e',
          code: 'GET_TOKENS_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<void> storeTokens(AuthTokens tokens) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        CacheFailure(
          message: 'Failed to store tokens: $e',
          code: 'STORE_TOKENS_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<void> clearAuthData() async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        CacheFailure(
          message: 'Failed to clear auth data: $e',
          code: 'CLEAR_AUTH_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<void> enableBiometricAuth() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        BiometricFailure(
          message: 'Failed to enable biometric auth: $e',
          code: 'ENABLE_BIOMETRIC_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<void> disableBiometricAuth() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        BiometricFailure(
          message: 'Failed to disable biometric auth: $e',
          code: 'DISABLE_BIOMETRIC_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<BiometricAuthData> getBiometricAuthData() async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));

      const biometricData = BiometricAuthData(
        isAvailable: true,
        isEnabled: false,
        supportedTypes: [BiometricType.fingerprint, BiometricType.face],
      );

      return const Result.success(biometricData);
    } catch (e) {
      return Result.failure(
        BiometricFailure(
          message: 'Failed to get biometric data: $e',
          code: 'GET_BIOMETRIC_DATA_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<bool> authenticateWithBiometrics() async {
    try {
      await Future.delayed(const Duration(seconds: 2));
      return const Result.success(true);
    } catch (e) {
      return Result.failure(
        BiometricFailure(
          message: 'Biometric authentication failed: $e',
          code: 'BIOMETRIC_AUTH_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<bool> verifySession() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return const Result.success(true);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Session verification failed: $e',
          code: 'SESSION_VERIFY_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<List<String>> getUserPermissions() async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      return const Result.success(['commission_chargers', 'view_reports']);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Failed to get permissions: $e',
          code: 'GET_PERMISSIONS_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<bool> hasPermission(String permission) async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      const permissions = ['commission_chargers', 'view_reports'];
      return Result.success(permissions.contains(permission));
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Permission check failed: $e',
          code: 'PERMISSION_CHECK_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<void> updateLastActivity() async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Failed to update activity: $e',
          code: 'UPDATE_ACTIVITY_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<bool> isSessionExpired() async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      return const Result.success(false);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Session check failed: $e',
          code: 'SESSION_CHECK_ERROR',
        ),
      );
    }
  }

  @override
  FutureResult<void> extendSession() async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        AuthFailure(
          message: 'Session extension failed: $e',
          code: 'SESSION_EXTEND_ERROR',
        ),
      );
    }
  }
}
