import 'package:freezed_annotation/freezed_annotation.dart';

part 'firmware_info.freezed.dart';
part 'firmware_info.g.dart';

/// Represents firmware update status
enum FirmwareStatus {
  @JsonValue('idle')
  idle,
  @JsonValue('downloading')
  downloading,
  @JsonValue('downloaded')
  downloaded,
  @JsonValue('installing')
  installing,
  @JsonValue('installed')
  installed,
  @JsonValue('failed')
  failed,
  @JsonValue('verification_failed')
  verificationFailed,
  @JsonValue('signature_verified')
  signatureVerified,
}

/// Represents firmware information
@freezed
class FirmwareInfo with _$FirmwareInfo {
  const factory FirmwareInfo({
    required String version,
    required String downloadUrl,
    required String checksum,
    required String checksumAlgorithm,
    required int fileSize,
    required DateTime releaseDate,
    String? description,
    String? releaseNotes,
    bool? isSecurityUpdate,
    bool? isCritical,
    List<String>? compatibleModels,
    String? minimumVersion,
    Map<String, dynamic>? metadata,
  }) = _FirmwareInfo;

  factory FirmwareInfo.fromJson(Map<String, dynamic> json) =>
      _$FirmwareInfoFromJson(json);
}

/// Represents firmware download progress
@freezed
class DownloadProgress with _$DownloadProgress {
  const factory DownloadProgress({
    required int bytesDownloaded,
    required int totalBytes,
    required double percentage,
    required double speed, // bytes per second
    DateTime? estimatedCompletion,
    String? currentPhase,
  }) = _DownloadProgress;

  factory DownloadProgress.fromJson(Map<String, dynamic> json) =>
      _$DownloadProgressFromJson(json);
}

/// Represents firmware installation progress
@freezed
class InstallationProgress with _$InstallationProgress {
  const factory InstallationProgress({
    required String phase,
    required double percentage,
    String? currentStep,
    String? message,
    DateTime? estimatedCompletion,
  }) = _InstallationProgress;

  factory InstallationProgress.fromJson(Map<String, dynamic> json) =>
      _$InstallationProgressFromJson(json);
}

/// Represents firmware update request
@freezed
class FirmwareUpdateRequest with _$FirmwareUpdateRequest {
  const factory FirmwareUpdateRequest({
    required String chargerId,
    required FirmwareInfo firmwareInfo,
    required DateTime requestedAt,
    DateTime? scheduledAt,
    bool? forceUpdate,
    int? retryAttempts,
    Map<String, dynamic>? options,
  }) = _FirmwareUpdateRequest;

  factory FirmwareUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$FirmwareUpdateRequestFromJson(json);
}

/// Represents firmware update result
@freezed
class FirmwareUpdateResult with _$FirmwareUpdateResult {
  const factory FirmwareUpdateResult({
    required String chargerId,
    required FirmwareStatus status,
    required DateTime completedAt,
    String? previousVersion,
    String? newVersion,
    String? errorMessage,
    String? errorCode,
    Duration? updateDuration,
    Map<String, dynamic>? diagnostics,
  }) = _FirmwareUpdateResult;

  factory FirmwareUpdateResult.fromJson(Map<String, dynamic> json) =>
      _$FirmwareUpdateResultFromJson(json);
}
