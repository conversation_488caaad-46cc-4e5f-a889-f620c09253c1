import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';

/// Real OCPP WebSocket client for EV charger communication
class OCPPWebSocketClient {
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  final StreamController<Map<String, dynamic>> _messageController =
      StreamController.broadcast();
  final StreamController<OCPPConnectionState> _stateController =
      StreamController.broadcast();

  String? _centralSystemUrl; // Stored for potential reconnection logic
  String? _chargePointId;
  String? _authKey; // Stored for potential reconnection logic
  int _messageId = 1;
  bool _isConnected = false;
  Timer? _heartbeatTimer;
  int _heartbeatInterval = 300; // seconds

  // Connection state
  OCPPConnectionState _state = OCPPConnectionState.disconnected;

  // Streams
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;
  Stream<OCPPConnectionState> get stateStream => _stateController.stream;
  bool get isConnected => _isConnected;
  OCPPConnectionState get state => _state;

  /// Connect to OCPP Central System
  Future<bool> connect({
    required String centralSystemUrl,
    required String chargePointId,
    String? authKey,
    int heartbeatInterval = 300,
  }) async {
    try {
      _centralSystemUrl = centralSystemUrl;
      _chargePointId = chargePointId;
      _authKey = authKey;
      _heartbeatInterval = heartbeatInterval;

      _updateState(OCPPConnectionState.connecting);

      // Create WebSocket connection
      final uri = Uri.parse('$centralSystemUrl/$chargePointId');

      // Add authentication headers if provided
      final headers = <String, String>{};
      if (authKey != null && authKey.isNotEmpty) {
        headers['Authorization'] = 'Bearer $authKey';
      }
      headers['Sec-WebSocket-Protocol'] = 'ocpp1.6';

      _channel = IOWebSocketChannel.connect(
        uri,
        headers: headers,
        pingInterval: Duration(seconds: _heartbeatInterval ~/ 2),
      );

      // Listen to messages
      _subscription = _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );

      _isConnected = true;
      _updateState(OCPPConnectionState.connected);

      // Send BootNotification
      await _sendBootNotification();

      // Start heartbeat timer
      _startHeartbeat();

      return true;
    } catch (e) {
      _updateState(OCPPConnectionState.error);
      _addMessage({
        'type': 'error',
        'message': 'Connection failed: $e',
        'timestamp': DateTime.now().toIso8601String(),
      });
      return false;
    }
  }

  /// Disconnect from Central System
  Future<void> disconnect() async {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;

    await _subscription?.cancel();
    await _channel?.sink.close();

    _isConnected = false;
    _updateState(OCPPConnectionState.disconnected);
  }

  /// Send OCPP message
  Future<void> sendMessage(String action, Map<String, dynamic> payload) async {
    if (!_isConnected || _channel == null) {
      throw Exception('Not connected to Central System');
    }

    final messageId = _getNextMessageId();
    final message = [2, messageId, action, payload];
    final jsonMessage = jsonEncode(message);

    _channel!.sink.add(jsonMessage);

    _addMessage({
      'type': 'sent',
      'action': action,
      'messageId': messageId,
      'payload': payload,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Send BootNotification message
  Future<void> _sendBootNotification() async {
    await sendMessage('BootNotification', {
      'chargePointVendor': 'EVTech Solutions',
      'chargePointModel': 'FastCharger Pro',
      'chargePointSerialNumber': _chargePointId ?? 'UNKNOWN',
      'firmwareVersion': '1.0.0',
      'iccid': '',
      'imsi': '',
      'meterSerialNumber': '',
      'meterType': '',
    });
  }

  /// Send Heartbeat message
  Future<void> _sendHeartbeat() async {
    await sendMessage('Heartbeat', {});
  }

  /// Send StatusNotification message
  Future<void> sendStatusNotification({
    required int connectorId,
    required String status,
    required String errorCode,
    String? info,
    DateTime? timestamp,
  }) async {
    await sendMessage('StatusNotification', {
      'connectorId': connectorId,
      'errorCode': errorCode,
      'status': status,
      'timestamp': (timestamp ?? DateTime.now()).toIso8601String(),
      if (info != null) 'info': info,
    });
  }

  /// Send Authorize message
  Future<void> sendAuthorize(String idTag) async {
    await sendMessage('Authorize', {'idTag': idTag});
  }

  /// Send StartTransaction message
  Future<void> sendStartTransaction({
    required int connectorId,
    required String idTag,
    required int meterStart,
    DateTime? timestamp,
    String? reservationId,
  }) async {
    await sendMessage('StartTransaction', {
      'connectorId': connectorId,
      'idTag': idTag,
      'meterStart': meterStart,
      'timestamp': (timestamp ?? DateTime.now()).toIso8601String(),
      if (reservationId != null) 'reservationId': reservationId,
    });
  }

  /// Send StopTransaction message
  Future<void> sendStopTransaction({
    required int transactionId,
    required int meterStop,
    DateTime? timestamp,
    String? reason,
    List<Map<String, dynamic>>? transactionData,
  }) async {
    await sendMessage('StopTransaction', {
      'transactionId': transactionId,
      'meterStop': meterStop,
      'timestamp': (timestamp ?? DateTime.now()).toIso8601String(),
      if (reason != null) 'reason': reason,
      if (transactionData != null) 'transactionData': transactionData,
    });
  }

  /// Handle incoming WebSocket messages
  void _handleMessage(dynamic data) {
    try {
      final message = jsonDecode(data as String) as List;
      final messageType = message[0] as int;

      switch (messageType) {
        case 2: // Call
          _handleCall(message);
          break;
        case 3: // CallResult
          _handleCallResult(message);
          break;
        case 4: // CallError
          _handleCallError(message);
          break;
      }
    } catch (e) {
      _addMessage({
        'type': 'error',
        'message': 'Failed to parse message: $e',
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Handle Call messages from Central System
  void _handleCall(List message) {
    final messageId = message[1] as String;
    final action = message[2] as String;
    final payload = message[3] as Map<String, dynamic>;

    _addMessage({
      'type': 'received',
      'action': action,
      'messageId': messageId,
      'payload': payload,
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Handle specific actions
    switch (action) {
      case 'RemoteStartTransaction':
        _handleRemoteStartTransaction(messageId, payload);
        break;
      case 'RemoteStopTransaction':
        _handleRemoteStopTransaction(messageId, payload);
        break;
      case 'ChangeConfiguration':
        _handleChangeConfiguration(messageId, payload);
        break;
      case 'GetConfiguration':
        _handleGetConfiguration(messageId, payload);
        break;
      case 'Reset':
        _handleReset(messageId, payload);
        break;
      default:
        _sendCallResult(messageId, {});
    }
  }

  /// Handle CallResult messages
  void _handleCallResult(List message) {
    final messageId = message[1] as String;
    final payload = message[2] as Map<String, dynamic>;

    _addMessage({
      'type': 'result',
      'messageId': messageId,
      'payload': payload,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Handle CallError messages
  void _handleCallError(List message) {
    final messageId = message[1] as String;
    final errorCode = message[2] as String;
    final errorDescription = message[3] as String;
    final errorDetails = message.length > 4 ? message[4] : {};

    _addMessage({
      'type': 'error',
      'messageId': messageId,
      'errorCode': errorCode,
      'errorDescription': errorDescription,
      'errorDetails': errorDetails,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Send CallResult response
  void _sendCallResult(String messageId, Map<String, dynamic> payload) {
    final message = [3, messageId, payload];
    final jsonMessage = jsonEncode(message);
    _channel?.sink.add(jsonMessage);
  }

  /// Handle RemoteStartTransaction
  void _handleRemoteStartTransaction(
    String messageId,
    Map<String, dynamic> payload,
  ) {
    // Simulate accepting the remote start
    _sendCallResult(messageId, {'status': 'Accepted'});

    // Send StatusNotification
    Future.delayed(const Duration(seconds: 1), () {
      sendStatusNotification(
        connectorId: payload['connectorId'] ?? 1,
        status: 'Preparing',
        errorCode: 'NoError',
      );
    });
  }

  /// Handle RemoteStopTransaction
  void _handleRemoteStopTransaction(
    String messageId,
    Map<String, dynamic> payload,
  ) {
    _sendCallResult(messageId, {'status': 'Accepted'});
  }

  /// Handle ChangeConfiguration
  void _handleChangeConfiguration(
    String messageId,
    Map<String, dynamic> payload,
  ) {
    _sendCallResult(messageId, {'status': 'Accepted'});
  }

  /// Handle GetConfiguration
  void _handleGetConfiguration(String messageId, Map<String, dynamic> payload) {
    _sendCallResult(messageId, {
      'configurationKey': [
        {
          'key': 'HeartbeatInterval',
          'readonly': false,
          'value': '$_heartbeatInterval',
        },
        {'key': 'MeterValueSampleInterval', 'readonly': false, 'value': '60'},
        {'key': 'NumberOfConnectors', 'readonly': true, 'value': '1'},
      ],
      'unknownKey': [],
    });
  }

  /// Handle Reset
  void _handleReset(String messageId, Map<String, dynamic> payload) {
    _sendCallResult(messageId, {'status': 'Accepted'});

    // Simulate reset by disconnecting and reconnecting
    Future.delayed(const Duration(seconds: 2), () {
      disconnect();
    });
  }

  /// Handle WebSocket errors
  void _handleError(error) {
    _updateState(OCPPConnectionState.error);
    _addMessage({
      'type': 'error',
      'message': 'WebSocket error: $error',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Handle WebSocket disconnection
  void _handleDisconnection() {
    _isConnected = false;
    _updateState(OCPPConnectionState.disconnected);
    _heartbeatTimer?.cancel();

    _addMessage({
      'type': 'info',
      'message': 'Disconnected from Central System',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Start heartbeat timer
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(
      Duration(seconds: _heartbeatInterval),
      (_) => _sendHeartbeat(),
    );
  }

  /// Update connection state
  void _updateState(OCPPConnectionState newState) {
    _state = newState;
    _stateController.add(newState);
  }

  /// Add message to stream
  void _addMessage(Map<String, dynamic> message) {
    _messageController.add(message);
  }

  /// Get next message ID
  String _getNextMessageId() {
    return (_messageId++).toString();
  }

  /// Dispose resources
  void dispose() {
    _heartbeatTimer?.cancel();
    _subscription?.cancel();
    _channel?.sink.close();
    _messageController.close();
    _stateController.close();
  }
}

/// OCPP connection states
enum OCPPConnectionState { disconnected, connecting, connected, error }
