import 'package:flutter/material.dart';

/// Page for establishing connection with a discovered charger
class ChargerConnectionPage extends StatefulWidget {
  static const String routeName = '/commissioning/discovery/connect';

  const ChargerConnectionPage({super.key});

  @override
  State<ChargerConnectionPage> createState() => _ChargerConnectionPageState();
}

class _ChargerConnectionPageState extends State<ChargerConnectionPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  Map<String, dynamic>? _chargerData;
  ConnectionStep _currentStep = ConnectionStep.initializing;
  String _statusMessage = 'Initializing connection...';
  bool _isConnected = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );
    _animationController.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_chargerData == null) {
      _chargerData =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      if (_chargerData != null) {
        _startConnectionProcess();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_chargerData == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Connect to Charger'),
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: Text('No charger data provided')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Connect to Charger'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isConnected)
            IconButton(icon: const Icon(Icons.check_circle), onPressed: null),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildChargerInfoCard(),
              const SizedBox(height: 24),
              _buildConnectionProgress(),
              const SizedBox(height: 24),
              _buildConnectionSteps(),
              if (_errorMessage != null) ...[
                const SizedBox(height: 24),
                _buildErrorCard(),
              ],
              const SizedBox(height: 24),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChargerInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.ev_station,
                    color: Theme.of(context).primaryColor,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _chargerData!['name'] ?? 'Unknown Charger',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Serial: ${_chargerData!['serial'] ?? 'Unknown'}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildChargerDetails(),
          ],
        ),
      ),
    );
  }

  Widget _buildChargerDetails() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildDetailRow('Model', _chargerData!['model'] ?? 'Unknown'),
          _buildDetailRow(
            'Manufacturer',
            _chargerData!['manufacturer'] ?? 'Unknown',
          ),
          _buildDetailRow(
            'Connection Type',
            _chargerData!['connectionType'] ?? 'Unknown',
          ),
          if (_chargerData!['macAddress'] != null)
            _buildDetailRow('MAC Address', _chargerData!['macAddress']),
          if (_chargerData!['ipAddress'] != null)
            _buildDetailRow('IP Address', _chargerData!['ipAddress']),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Widget _buildConnectionProgress() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Connection Status',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusIcon(),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getStepTitle(_currentStep),
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w500),
                      ),
                      Text(
                        _statusMessage,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: _getProgressValue(),
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _isConnected ? Colors.green : Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon() {
    if (_errorMessage != null) {
      return const Icon(Icons.error, color: Colors.red, size: 32);
    }

    if (_isConnected) {
      return const Icon(Icons.check_circle, color: Colors.green, size: 32);
    }

    return const SizedBox(
      width: 32,
      height: 32,
      child: CircularProgressIndicator(strokeWidth: 3),
    );
  }

  Widget _buildConnectionSteps() {
    final steps = ConnectionStep.values;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Connection Steps',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...steps.map((step) => _buildStepItem(step)),
          ],
        ),
      ),
    );
  }

  Widget _buildStepItem(ConnectionStep step) {
    final isCompleted = step.index < _currentStep.index;
    final isCurrent = step == _currentStep;
    final isPending = step.index > _currentStep.index;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCompleted
                  ? Colors.green
                  : isCurrent
                  ? Theme.of(context).primaryColor
                  : Colors.grey[300],
            ),
            child: Icon(
              isCompleted
                  ? Icons.check
                  : isCurrent
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              size: 16,
              color: isCompleted || isCurrent ? Colors.white : Colors.grey[600],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _getStepTitle(step),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                color: isPending ? Colors.grey[600] : null,
              ),
            ),
          ),
          if (isCurrent && _errorMessage == null)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
        ],
      ),
    );
  }

  Widget _buildErrorCard() {
    return Card(
      color: Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.error, color: Colors.red.shade600),
                const SizedBox(width: 8),
                Text(
                  'Connection Error',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.red.shade600),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    if (_isConnected) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _startCommissioning,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Start Commissioning'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _testConnection,
              icon: const Icon(Icons.speed),
              label: const Text('Test Connection'),
            ),
          ),
        ],
      );
    }

    if (_errorMessage != null) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _retryConnection,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry Connection'),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Back to Discovery'),
            ),
          ),
        ],
      );
    }

    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        onPressed: () => Navigator.of(context).pop(),
        child: const Text('Cancel'),
      ),
    );
  }

  Future<void> _startConnectionProcess() async {
    final steps = [
      ConnectionStep.initializing,
      ConnectionStep.authenticating,
      ConnectionStep.establishing,
      ConnectionStep.verifying,
      ConnectionStep.connected,
    ];

    for (final step in steps) {
      if (!mounted) return;

      setState(() {
        _currentStep = step;
        _statusMessage = _getStepMessage(step);
        _errorMessage = null;
      });

      try {
        await _executeConnectionStep(step);
        await Future.delayed(
          const Duration(seconds: 2),
        ); // Simulate processing time
      } catch (e) {
        setState(() {
          _errorMessage = e.toString();
        });
        return;
      }
    }

    setState(() {
      _isConnected = true;
      _statusMessage = 'Successfully connected to charger';
    });
  }

  Future<void> _executeConnectionStep(ConnectionStep step) async {
    switch (step) {
      case ConnectionStep.initializing:
        // Initialize connection parameters
        await Future.delayed(const Duration(seconds: 1));
        // Simulate parameter setup
        break;
      case ConnectionStep.authenticating:
        // Authenticate with charger
        await Future.delayed(const Duration(seconds: 2));
        // Simulate authentication process
        break;
      case ConnectionStep.establishing:
        // Establish communication channel
        await Future.delayed(const Duration(seconds: 2));
        // Simulate channel establishment
        break;
      case ConnectionStep.verifying:
        // Verify connection integrity
        await Future.delayed(const Duration(seconds: 1));
        // Simulate verification
        break;
      case ConnectionStep.connected:
        // Connection established
        await Future.delayed(const Duration(milliseconds: 500));
        break;
    }
  }

  String _getStepTitle(ConnectionStep step) {
    switch (step) {
      case ConnectionStep.initializing:
        return 'Initializing';
      case ConnectionStep.authenticating:
        return 'Authenticating';
      case ConnectionStep.establishing:
        return 'Establishing Connection';
      case ConnectionStep.verifying:
        return 'Verifying';
      case ConnectionStep.connected:
        return 'Connected';
    }
  }

  String _getStepMessage(ConnectionStep step) {
    switch (step) {
      case ConnectionStep.initializing:
        return 'Preparing connection parameters...';
      case ConnectionStep.authenticating:
        return 'Authenticating with charger...';
      case ConnectionStep.establishing:
        return 'Establishing communication channel...';
      case ConnectionStep.verifying:
        return 'Verifying connection integrity...';
      case ConnectionStep.connected:
        return 'Connection established successfully';
    }
  }

  double _getProgressValue() {
    return (_currentStep.index + 1) / ConnectionStep.values.length;
  }

  void _retryConnection() {
    setState(() {
      _currentStep = ConnectionStep.initializing;
      _errorMessage = null;
      _isConnected = false;
    });
    _startConnectionProcess();
  }

  void _testConnection() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Connection test completed successfully')),
    );
  }

  void _startCommissioning() {
    Navigator.pushNamed(
      context,
      '/commissioning/options',
      arguments: {'chargerData': _chargerData, 'connectionEstablished': true},
    );
  }
}

enum ConnectionStep {
  initializing,
  authenticating,
  establishing,
  verifying,
  connected,
}
