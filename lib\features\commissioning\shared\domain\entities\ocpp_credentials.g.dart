// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ocpp_credentials.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OCPPCredentialsImpl _$$OCPPCredentialsImplFromJson(
        Map<String, dynamic> json) =>
    _$OCPPCredentialsImpl(
      chargePointId: json['chargePointId'] as String,
      cmsUrl: json['cmsUrl'] as String,
      version: $enumDecode(_$OCPPVersionEnumMap, json['version']),
      authType: $enumDecode(_$OCPPAuthTypeEnumMap, json['authType']),
      username: json['username'] as String?,
      password: json['password'] as String?,
      certificatePath: json['certificatePath'] as String?,
      privateKeyPath: json['privateKeyPath'] as String?,
      caCertificatePath: json['caCertificatePath'] as String?,
      authToken: json['authToken'] as String?,
      headers: (json['headers'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      heartbeatInterval: (json['heartbeatInterval'] as num?)?.toInt(),
      connectionTimeout: (json['connectionTimeout'] as num?)?.toInt(),
      useSSL: json['useSSL'] as bool?,
    );

Map<String, dynamic> _$$OCPPCredentialsImplToJson(
        _$OCPPCredentialsImpl instance) =>
    <String, dynamic>{
      'chargePointId': instance.chargePointId,
      'cmsUrl': instance.cmsUrl,
      'version': _$OCPPVersionEnumMap[instance.version]!,
      'authType': _$OCPPAuthTypeEnumMap[instance.authType]!,
      'username': instance.username,
      'password': instance.password,
      'certificatePath': instance.certificatePath,
      'privateKeyPath': instance.privateKeyPath,
      'caCertificatePath': instance.caCertificatePath,
      'authToken': instance.authToken,
      'headers': instance.headers,
      'heartbeatInterval': instance.heartbeatInterval,
      'connectionTimeout': instance.connectionTimeout,
      'useSSL': instance.useSSL,
    };

const _$OCPPVersionEnumMap = {
  OCPPVersion.v16: '1.6',
  OCPPVersion.v20: '2.0',
  OCPPVersion.v201: '2.0.1',
};

const _$OCPPAuthTypeEnumMap = {
  OCPPAuthType.basic: 'basic',
  OCPPAuthType.certificate: 'certificate',
  OCPPAuthType.token: 'token',
};

_$OCPPMessageImpl _$$OCPPMessageImplFromJson(Map<String, dynamic> json) =>
    _$OCPPMessageImpl(
      messageType: $enumDecode(_$OCPPMessageTypeEnumMap, json['messageType']),
      messageId: json['messageId'] as String,
      action: json['action'] as String,
      payload: json['payload'] as Map<String, dynamic>,
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
      errorCode: json['errorCode'] as String?,
      errorDescription: json['errorDescription'] as String?,
    );

Map<String, dynamic> _$$OCPPMessageImplToJson(_$OCPPMessageImpl instance) =>
    <String, dynamic>{
      'messageType': _$OCPPMessageTypeEnumMap[instance.messageType]!,
      'messageId': instance.messageId,
      'action': instance.action,
      'payload': instance.payload,
      'timestamp': instance.timestamp?.toIso8601String(),
      'errorCode': instance.errorCode,
      'errorDescription': instance.errorDescription,
    };

const _$OCPPMessageTypeEnumMap = {
  OCPPMessageType.call: 'call',
  OCPPMessageType.callResult: 'callresult',
  OCPPMessageType.callError: 'callerror',
};

_$OCPPConnectionStatusImpl _$$OCPPConnectionStatusImplFromJson(
        Map<String, dynamic> json) =>
    _$OCPPConnectionStatusImpl(
      isConnected: json['isConnected'] as bool,
      isRegistered: json['isRegistered'] as bool,
      connectedAt: json['connectedAt'] == null
          ? null
          : DateTime.parse(json['connectedAt'] as String),
      lastHeartbeat: json['lastHeartbeat'] == null
          ? null
          : DateTime.parse(json['lastHeartbeat'] as String),
      sessionId: json['sessionId'] as String?,
      errorMessage: json['errorMessage'] as String?,
      reconnectAttempts: (json['reconnectAttempts'] as num?)?.toInt(),
      connectionInfo: json['connectionInfo'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$OCPPConnectionStatusImplToJson(
        _$OCPPConnectionStatusImpl instance) =>
    <String, dynamic>{
      'isConnected': instance.isConnected,
      'isRegistered': instance.isRegistered,
      'connectedAt': instance.connectedAt?.toIso8601String(),
      'lastHeartbeat': instance.lastHeartbeat?.toIso8601String(),
      'sessionId': instance.sessionId,
      'errorMessage': instance.errorMessage,
      'reconnectAttempts': instance.reconnectAttempts,
      'connectionInfo': instance.connectionInfo,
    };

_$ChargingTransactionImpl _$$ChargingTransactionImplFromJson(
        Map<String, dynamic> json) =>
    _$ChargingTransactionImpl(
      transactionId: json['transactionId'] as String,
      connectorId: json['connectorId'] as String,
      idTag: json['idTag'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      startMeterValue: (json['startMeterValue'] as num).toDouble(),
      stopTime: json['stopTime'] == null
          ? null
          : DateTime.parse(json['stopTime'] as String),
      stopMeterValue: (json['stopMeterValue'] as num?)?.toDouble(),
      stopReason: json['stopReason'] as String?,
      energyDelivered: (json['energyDelivered'] as num?)?.toDouble(),
      cost: (json['cost'] as num?)?.toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$ChargingTransactionImplToJson(
        _$ChargingTransactionImpl instance) =>
    <String, dynamic>{
      'transactionId': instance.transactionId,
      'connectorId': instance.connectorId,
      'idTag': instance.idTag,
      'startTime': instance.startTime.toIso8601String(),
      'startMeterValue': instance.startMeterValue,
      'stopTime': instance.stopTime?.toIso8601String(),
      'stopMeterValue': instance.stopMeterValue,
      'stopReason': instance.stopReason,
      'energyDelivered': instance.energyDelivered,
      'cost': instance.cost,
      'metadata': instance.metadata,
    };
