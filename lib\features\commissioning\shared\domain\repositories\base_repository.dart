import 'package:dartz/dartz.dart';
import '../../../../../core/error/failures.dart';

/// Base repository interface with common CRUD operations
abstract class BaseRepository<T, ID> {
  /// Create a new entity
  Future<Either<Failure, T>> create(T entity);

  /// Get entity by ID
  Future<Either<Failure, T>> getById(ID id);

  /// Get all entities with optional filtering
  Future<Either<Failure, List<T>>> getAll({
    Map<String, dynamic>? filters,
    int? limit,
    int? offset,
  });

  /// Update an existing entity
  Future<Either<Failure, T>> update(T entity);

  /// Delete entity by ID
  Future<Either<Failure, bool>> delete(ID id);

  /// Check if entity exists
  Future<Either<Failure, bool>> exists(ID id);

  /// Get count of entities with optional filtering
  Future<Either<Failure, int>> count({Map<String, dynamic>? filters});
}

/// Repository interface for entities that support caching
abstract class CacheableRepository<T, ID> extends BaseRepository<T, ID> {
  /// Clear cache for specific entity
  Future<void> clearCache(ID id);

  /// Clear all cache
  Future<void> clearAllCache();

  /// Refresh cache for specific entity
  Future<Either<Failure, T>> refreshCache(ID id);

  /// Get entity from cache only
  Future<Either<Failure, T?>> getFromCache(ID id);

  /// Check if entity is cached
  Future<bool> isCached(ID id);
}

/// Repository interface for entities that support offline operations
abstract class OfflineRepository<T, ID> extends CacheableRepository<T, ID> {
  /// Sync pending changes with remote
  Future<Either<Failure, List<T>>> syncPendingChanges();

  /// Get pending changes count
  Future<int> getPendingChangesCount();

  /// Mark entity for offline sync
  Future<void> markForSync(ID id);

  /// Check if entity has pending changes
  Future<bool> hasPendingChanges(ID id);

  /// Get all entities with pending changes
  Future<List<T>> getPendingEntities();
}

/// Repository interface for real-time data
abstract class RealtimeRepository<T, ID> extends BaseRepository<T, ID> {
  /// Subscribe to real-time updates for specific entity
  Stream<T> subscribeToEntity(ID id);

  /// Subscribe to real-time updates for all entities
  Stream<List<T>> subscribeToAll({Map<String, dynamic>? filters});

  /// Unsubscribe from real-time updates
  Future<void> unsubscribe();

  /// Check if subscribed to real-time updates
  bool get isSubscribed;
}
