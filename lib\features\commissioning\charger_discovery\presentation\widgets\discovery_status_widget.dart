import 'package:flutter/material.dart';

/// Widget displaying the current discovery status
class DiscoveryStatusWidget extends StatelessWidget {
  final bool isScanning;
  final int discoveredCount;
  final String? errorMessage;
  final VoidCallback? onRetry;

  const DiscoveryStatusWidget({
    super.key,
    required this.isScanning,
    required this.discoveredCount,
    this.errorMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (errorMessage != null) {
      return _buildErrorStatus(context);
    }

    if (isScanning) {
      return _buildScanningStatus(context);
    }

    return _buildResultStatus(context);
  }

  Widget _buildScanningStatus(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.blue.shade50,
      child: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Scanning for chargers...',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Colors.blue.shade700,
                      ),
                ),
                Text(
                  'Found $discoveredCount charger${discoveredCount != 1 ? 's' : ''}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.blue.shade600,
                      ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.bluetooth_searching,
            color: Colors.blue.shade600,
          ),
        ],
      ),
    );
  }

  Widget _buildResultStatus(BuildContext context) {
    if (discoveredCount == 0) {
      return Container(
        padding: const EdgeInsets.all(16),
        color: Colors.orange.shade50,
        child: Row(
          children: [
            Icon(
              Icons.search_off,
              color: Colors.orange.shade600,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'No chargers found',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Colors.orange.shade700,
                        ),
                  ),
                  Text(
                    'Make sure chargers are powered on and in pairing mode',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.orange.shade600,
                        ),
                  ),
                ],
              ),
            ),
            if (onRetry != null)
              IconButton(
                onPressed: onRetry,
                icon: Icon(
                  Icons.refresh,
                  color: Colors.orange.shade600,
                ),
              ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.green.shade50,
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green.shade600,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Found $discoveredCount charger${discoveredCount != 1 ? 's' : ''}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Colors.green.shade700,
                      ),
                ),
                Text(
                  'Select a charger to start commissioning',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.green.shade600,
                      ),
                ),
              ],
            ),
          ),
          if (onRetry != null)
            IconButton(
              onPressed: onRetry,
              icon: Icon(
                Icons.refresh,
                color: Colors.green.shade600,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildErrorStatus(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.red.shade50,
      child: Row(
        children: [
          Icon(
            Icons.error,
            color: Colors.red.shade600,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Discovery Error',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Colors.red.shade700,
                      ),
                ),
                Text(
                  errorMessage!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.red.shade600,
                      ),
                ),
              ],
            ),
          ),
          if (onRetry != null)
            IconButton(
              onPressed: onRetry,
              icon: Icon(
                Icons.refresh,
                color: Colors.red.shade600,
              ),
            ),
        ],
      ),
    );
  }
}

/// Widget for displaying discovery filters and options
class DiscoveryFiltersWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onFiltersChanged;

  const DiscoveryFiltersWidget({
    super.key,
    required this.onFiltersChanged,
  });

  @override
  State<DiscoveryFiltersWidget> createState() => _DiscoveryFiltersWidgetState();
}

class _DiscoveryFiltersWidgetState extends State<DiscoveryFiltersWidget> {
  bool _showOnlyKnownManufacturers = false;
  bool _showOnlyStrongSignal = false;
  String _selectedConnectionType = 'all';

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: ExpansionTile(
        title: const Text('Discovery Filters'),
        leading: const Icon(Icons.filter_list),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Known Manufacturers Only'),
                  subtitle: const Text('Show only recognized charger brands'),
                  value: _showOnlyKnownManufacturers,
                  onChanged: (value) {
                    setState(() {
                      _showOnlyKnownManufacturers = value;
                    });
                    _updateFilters();
                  },
                ),
                SwitchListTile(
                  title: const Text('Strong Signal Only'),
                  subtitle: const Text('Show only chargers with good signal strength'),
                  value: _showOnlyStrongSignal,
                  onChanged: (value) {
                    setState(() {
                      _showOnlyStrongSignal = value;
                    });
                    _updateFilters();
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Connection Type',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedConnectionType,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All Types')),
                    DropdownMenuItem(value: 'bluetooth', child: Text('Bluetooth')),
                    DropdownMenuItem(value: 'wifi', child: Text('Wi-Fi')),
                    DropdownMenuItem(value: 'ethernet', child: Text('Ethernet')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedConnectionType = value!;
                    });
                    _updateFilters();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _updateFilters() {
    widget.onFiltersChanged({
      'knownManufacturersOnly': _showOnlyKnownManufacturers,
      'strongSignalOnly': _showOnlyStrongSignal,
      'connectionType': _selectedConnectionType,
    });
  }
}
