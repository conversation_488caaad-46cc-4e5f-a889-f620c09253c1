import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/auth_bloc.dart';

/// Biometric login button widget
class BiometricLoginButton extends StatelessWidget {
  const BiometricLoginButton({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        // Don't show if loading or if biometric data indicates it's not available
        if (state.isLoading || 
            (state.biometricData != null && !state.biometricData!.isAvailable)) {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            // Divider with "OR"
            Row(
              children: [
                const Expanded(child: Divider()),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    'OR',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Expanded(child: Divider()),
              ],
            ),
            const SizedBox(height: 24),

            // Biometric Login Button
            OutlinedButton.icon(
              onPressed: state.isLoading ? null : _handleBiometricLogin,
              icon: const Icon(Icons.fingerprint),
              label: const Text('Sign in with Biometrics'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                side: BorderSide(
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _handleBiometricLogin() {
    // TODO: Implement biometric login
    // For now, just show a placeholder message
    // context.read<AuthBloc>().add(const AuthBiometricLoginRequested());
  }
}
