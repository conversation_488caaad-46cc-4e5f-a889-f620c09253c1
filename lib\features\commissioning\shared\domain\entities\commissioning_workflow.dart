import 'package:freezed_annotation/freezed_annotation.dart';
import 'charger_info.dart';
import 'network_config.dart';
import 'ocpp_credentials.dart';
import 'firmware_info.dart';

part 'commissioning_workflow.freezed.dart';
part 'commissioning_workflow.g.dart';

/// Represents the current step in the commissioning workflow
enum CommissioningStep {
  @JsonValue('discovery')
  discovery,
  @JsonValue('connection')
  connection,
  @JsonValue('authentication')
  authentication,
  @JsonValue('network_config')
  networkConfig,
  @JsonValue('ocpp_setup')
  ocppSetup,
  @JsonValue('firmware_update')
  firmwareUpdate,
  @JsonValue('parameter_config')
  parameterConfig,
  @JsonValue('diagnostics')
  diagnostics,
  @JsonValue('compliance_check')
  complianceCheck,
  @JsonValue('finalization')
  finalization,
  @JsonValue('completed')
  completed,
}

/// Represents the status of a commissioning step
enum StepStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('skipped')
  skipped,
  @JsonValue('cancelled')
  cancelled,
}

/// Represents a single step in the commissioning workflow
@freezed
class WorkflowStep with _$WorkflowStep {
  const factory WorkflowStep({
    required CommissioningStep step,
    required String name,
    required String description,
    required StepStatus status,
    DateTime? startedAt,
    DateTime? completedAt,
    String? errorMessage,
    Map<String, dynamic>? stepData,
    List<String>? prerequisites,
    bool? isOptional,
    int? estimatedDurationMinutes,
  }) = _WorkflowStep;

  factory WorkflowStep.fromJson(Map<String, dynamic> json) =>
      _$WorkflowStepFromJson(json);
}

/// Represents the complete commissioning workflow
@freezed
class CommissioningWorkflow with _$CommissioningWorkflow {
  const factory CommissioningWorkflow({
    required String id,
    required String chargerId,
    required String technicianId,
    required List<WorkflowStep> steps,
    required CommissioningStep currentStep,
    required DateTime startedAt,
    DateTime? completedAt,
    String? notes,
    Map<String, dynamic>? configuration,
    List<String>? attachments,
    bool? isCompleted,
    double? progressPercentage,
  }) = _CommissioningWorkflow;

  factory CommissioningWorkflow.fromJson(Map<String, dynamic> json) =>
      _$CommissioningWorkflowFromJson(json);
}

/// Represents commissioning configuration template
@freezed
class CommissioningTemplate with _$CommissioningTemplate {
  const factory CommissioningTemplate({
    required String id,
    required String name,
    required String description,
    required List<CommissioningStep> steps,
    required Map<String, dynamic> defaultParameters,
    String? chargerModel,
    String? version,
    bool? isDefault,
    DateTime? createdAt,
    String? createdBy,
  }) = _CommissioningTemplate;

  factory CommissioningTemplate.fromJson(Map<String, dynamic> json) =>
      _$CommissioningTemplateFromJson(json);
}

/// Represents commissioning session data
@freezed
class CommissioningSession with _$CommissioningSession {
  const factory CommissioningSession({
    required String sessionId,
    required String chargerId,
    required ChargerConnection connection,
    ChargerInfo? chargerInfo,
    NetworkConfig? networkConfig,
    OCPPCredentials? ocppCredentials,
    FirmwareInfo? firmwareInfo,
    Map<String, dynamic>? parameters,
    Map<String, dynamic>? diagnostics,
    DateTime? sessionStarted,
    DateTime? lastActivity,
    bool? isActive,
  }) = _CommissioningSession;

  factory CommissioningSession.fromJson(Map<String, dynamic> json) =>
      _$CommissioningSessionFromJson(json);
}

/// Represents commissioning progress update
@freezed
class CommissioningProgress with _$CommissioningProgress {
  const factory CommissioningProgress({
    required String workflowId,
    required CommissioningStep currentStep,
    required double progressPercentage,
    required String statusMessage,
    StepStatus? stepStatus,
    String? errorMessage,
    DateTime? timestamp,
    Map<String, dynamic>? stepData,
  }) = _CommissioningProgress;

  factory CommissioningProgress.fromJson(Map<String, dynamic> json) =>
      _$CommissioningProgressFromJson(json);
}

/// Represents commissioning result
@freezed
class CommissioningResult with _$CommissioningResult {
  const factory CommissioningResult({
    required String workflowId,
    required String chargerId,
    required bool isSuccessful,
    required DateTime completedAt,
    required Duration totalDuration,
    List<WorkflowStep>? completedSteps,
    List<WorkflowStep>? failedSteps,
    Map<String, dynamic>? finalConfiguration,
    Map<String, dynamic>? diagnosticsReport,
    List<String>? complianceCertificates,
    String? summary,
    List<String>? recommendations,
  }) = _CommissioningResult;

  factory CommissioningResult.fromJson(Map<String, dynamic> json) =>
      _$CommissioningResultFromJson(json);
}
