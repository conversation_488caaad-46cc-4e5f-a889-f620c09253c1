// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'commissioning_workflow.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkflowStep _$WorkflowStepFromJson(Map<String, dynamic> json) {
  return _WorkflowStep.fromJson(json);
}

/// @nodoc
mixin _$WorkflowStep {
  CommissioningStep get step => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  StepStatus get status => throw _privateConstructorUsedError;
  DateTime? get startedAt => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, dynamic>? get stepData => throw _privateConstructorUsedError;
  List<String>? get prerequisites => throw _privateConstructorUsedError;
  bool? get isOptional => throw _privateConstructorUsedError;
  int? get estimatedDurationMinutes => throw _privateConstructorUsedError;

  /// Serializes this WorkflowStep to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorkflowStep
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorkflowStepCopyWith<WorkflowStep> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkflowStepCopyWith<$Res> {
  factory $WorkflowStepCopyWith(
          WorkflowStep value, $Res Function(WorkflowStep) then) =
      _$WorkflowStepCopyWithImpl<$Res, WorkflowStep>;
  @useResult
  $Res call(
      {CommissioningStep step,
      String name,
      String description,
      StepStatus status,
      DateTime? startedAt,
      DateTime? completedAt,
      String? errorMessage,
      Map<String, dynamic>? stepData,
      List<String>? prerequisites,
      bool? isOptional,
      int? estimatedDurationMinutes});
}

/// @nodoc
class _$WorkflowStepCopyWithImpl<$Res, $Val extends WorkflowStep>
    implements $WorkflowStepCopyWith<$Res> {
  _$WorkflowStepCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkflowStep
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? step = null,
    Object? name = null,
    Object? description = null,
    Object? status = null,
    Object? startedAt = freezed,
    Object? completedAt = freezed,
    Object? errorMessage = freezed,
    Object? stepData = freezed,
    Object? prerequisites = freezed,
    Object? isOptional = freezed,
    Object? estimatedDurationMinutes = freezed,
  }) {
    return _then(_value.copyWith(
      step: null == step
          ? _value.step
          : step // ignore: cast_nullable_to_non_nullable
              as CommissioningStep,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as StepStatus,
      startedAt: freezed == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      stepData: freezed == stepData
          ? _value.stepData
          : stepData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      prerequisites: freezed == prerequisites
          ? _value.prerequisites
          : prerequisites // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      isOptional: freezed == isOptional
          ? _value.isOptional
          : isOptional // ignore: cast_nullable_to_non_nullable
              as bool?,
      estimatedDurationMinutes: freezed == estimatedDurationMinutes
          ? _value.estimatedDurationMinutes
          : estimatedDurationMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkflowStepImplCopyWith<$Res>
    implements $WorkflowStepCopyWith<$Res> {
  factory _$$WorkflowStepImplCopyWith(
          _$WorkflowStepImpl value, $Res Function(_$WorkflowStepImpl) then) =
      __$$WorkflowStepImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CommissioningStep step,
      String name,
      String description,
      StepStatus status,
      DateTime? startedAt,
      DateTime? completedAt,
      String? errorMessage,
      Map<String, dynamic>? stepData,
      List<String>? prerequisites,
      bool? isOptional,
      int? estimatedDurationMinutes});
}

/// @nodoc
class __$$WorkflowStepImplCopyWithImpl<$Res>
    extends _$WorkflowStepCopyWithImpl<$Res, _$WorkflowStepImpl>
    implements _$$WorkflowStepImplCopyWith<$Res> {
  __$$WorkflowStepImplCopyWithImpl(
      _$WorkflowStepImpl _value, $Res Function(_$WorkflowStepImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkflowStep
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? step = null,
    Object? name = null,
    Object? description = null,
    Object? status = null,
    Object? startedAt = freezed,
    Object? completedAt = freezed,
    Object? errorMessage = freezed,
    Object? stepData = freezed,
    Object? prerequisites = freezed,
    Object? isOptional = freezed,
    Object? estimatedDurationMinutes = freezed,
  }) {
    return _then(_$WorkflowStepImpl(
      step: null == step
          ? _value.step
          : step // ignore: cast_nullable_to_non_nullable
              as CommissioningStep,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as StepStatus,
      startedAt: freezed == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      stepData: freezed == stepData
          ? _value._stepData
          : stepData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      prerequisites: freezed == prerequisites
          ? _value._prerequisites
          : prerequisites // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      isOptional: freezed == isOptional
          ? _value.isOptional
          : isOptional // ignore: cast_nullable_to_non_nullable
              as bool?,
      estimatedDurationMinutes: freezed == estimatedDurationMinutes
          ? _value.estimatedDurationMinutes
          : estimatedDurationMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkflowStepImpl implements _WorkflowStep {
  const _$WorkflowStepImpl(
      {required this.step,
      required this.name,
      required this.description,
      required this.status,
      this.startedAt,
      this.completedAt,
      this.errorMessage,
      final Map<String, dynamic>? stepData,
      final List<String>? prerequisites,
      this.isOptional,
      this.estimatedDurationMinutes})
      : _stepData = stepData,
        _prerequisites = prerequisites;

  factory _$WorkflowStepImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkflowStepImplFromJson(json);

  @override
  final CommissioningStep step;
  @override
  final String name;
  @override
  final String description;
  @override
  final StepStatus status;
  @override
  final DateTime? startedAt;
  @override
  final DateTime? completedAt;
  @override
  final String? errorMessage;
  final Map<String, dynamic>? _stepData;
  @override
  Map<String, dynamic>? get stepData {
    final value = _stepData;
    if (value == null) return null;
    if (_stepData is EqualUnmodifiableMapView) return _stepData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _prerequisites;
  @override
  List<String>? get prerequisites {
    final value = _prerequisites;
    if (value == null) return null;
    if (_prerequisites is EqualUnmodifiableListView) return _prerequisites;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? isOptional;
  @override
  final int? estimatedDurationMinutes;

  @override
  String toString() {
    return 'WorkflowStep(step: $step, name: $name, description: $description, status: $status, startedAt: $startedAt, completedAt: $completedAt, errorMessage: $errorMessage, stepData: $stepData, prerequisites: $prerequisites, isOptional: $isOptional, estimatedDurationMinutes: $estimatedDurationMinutes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkflowStepImpl &&
            (identical(other.step, step) || other.step == step) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startedAt, startedAt) ||
                other.startedAt == startedAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(other._stepData, _stepData) &&
            const DeepCollectionEquality()
                .equals(other._prerequisites, _prerequisites) &&
            (identical(other.isOptional, isOptional) ||
                other.isOptional == isOptional) &&
            (identical(
                    other.estimatedDurationMinutes, estimatedDurationMinutes) ||
                other.estimatedDurationMinutes == estimatedDurationMinutes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      step,
      name,
      description,
      status,
      startedAt,
      completedAt,
      errorMessage,
      const DeepCollectionEquality().hash(_stepData),
      const DeepCollectionEquality().hash(_prerequisites),
      isOptional,
      estimatedDurationMinutes);

  /// Create a copy of WorkflowStep
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkflowStepImplCopyWith<_$WorkflowStepImpl> get copyWith =>
      __$$WorkflowStepImplCopyWithImpl<_$WorkflowStepImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkflowStepImplToJson(
      this,
    );
  }
}

abstract class _WorkflowStep implements WorkflowStep {
  const factory _WorkflowStep(
      {required final CommissioningStep step,
      required final String name,
      required final String description,
      required final StepStatus status,
      final DateTime? startedAt,
      final DateTime? completedAt,
      final String? errorMessage,
      final Map<String, dynamic>? stepData,
      final List<String>? prerequisites,
      final bool? isOptional,
      final int? estimatedDurationMinutes}) = _$WorkflowStepImpl;

  factory _WorkflowStep.fromJson(Map<String, dynamic> json) =
      _$WorkflowStepImpl.fromJson;

  @override
  CommissioningStep get step;
  @override
  String get name;
  @override
  String get description;
  @override
  StepStatus get status;
  @override
  DateTime? get startedAt;
  @override
  DateTime? get completedAt;
  @override
  String? get errorMessage;
  @override
  Map<String, dynamic>? get stepData;
  @override
  List<String>? get prerequisites;
  @override
  bool? get isOptional;
  @override
  int? get estimatedDurationMinutes;

  /// Create a copy of WorkflowStep
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorkflowStepImplCopyWith<_$WorkflowStepImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CommissioningWorkflow _$CommissioningWorkflowFromJson(
    Map<String, dynamic> json) {
  return _CommissioningWorkflow.fromJson(json);
}

/// @nodoc
mixin _$CommissioningWorkflow {
  String get id => throw _privateConstructorUsedError;
  String get chargerId => throw _privateConstructorUsedError;
  String get technicianId => throw _privateConstructorUsedError;
  List<WorkflowStep> get steps => throw _privateConstructorUsedError;
  CommissioningStep get currentStep => throw _privateConstructorUsedError;
  DateTime get startedAt => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  Map<String, dynamic>? get configuration => throw _privateConstructorUsedError;
  List<String>? get attachments => throw _privateConstructorUsedError;
  bool? get isCompleted => throw _privateConstructorUsedError;
  double? get progressPercentage => throw _privateConstructorUsedError;

  /// Serializes this CommissioningWorkflow to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CommissioningWorkflow
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CommissioningWorkflowCopyWith<CommissioningWorkflow> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommissioningWorkflowCopyWith<$Res> {
  factory $CommissioningWorkflowCopyWith(CommissioningWorkflow value,
          $Res Function(CommissioningWorkflow) then) =
      _$CommissioningWorkflowCopyWithImpl<$Res, CommissioningWorkflow>;
  @useResult
  $Res call(
      {String id,
      String chargerId,
      String technicianId,
      List<WorkflowStep> steps,
      CommissioningStep currentStep,
      DateTime startedAt,
      DateTime? completedAt,
      String? notes,
      Map<String, dynamic>? configuration,
      List<String>? attachments,
      bool? isCompleted,
      double? progressPercentage});
}

/// @nodoc
class _$CommissioningWorkflowCopyWithImpl<$Res,
        $Val extends CommissioningWorkflow>
    implements $CommissioningWorkflowCopyWith<$Res> {
  _$CommissioningWorkflowCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommissioningWorkflow
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? chargerId = null,
    Object? technicianId = null,
    Object? steps = null,
    Object? currentStep = null,
    Object? startedAt = null,
    Object? completedAt = freezed,
    Object? notes = freezed,
    Object? configuration = freezed,
    Object? attachments = freezed,
    Object? isCompleted = freezed,
    Object? progressPercentage = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      technicianId: null == technicianId
          ? _value.technicianId
          : technicianId // ignore: cast_nullable_to_non_nullable
              as String,
      steps: null == steps
          ? _value.steps
          : steps // ignore: cast_nullable_to_non_nullable
              as List<WorkflowStep>,
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as CommissioningStep,
      startedAt: null == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      configuration: freezed == configuration
          ? _value.configuration
          : configuration // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      attachments: freezed == attachments
          ? _value.attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      isCompleted: freezed == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      progressPercentage: freezed == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CommissioningWorkflowImplCopyWith<$Res>
    implements $CommissioningWorkflowCopyWith<$Res> {
  factory _$$CommissioningWorkflowImplCopyWith(
          _$CommissioningWorkflowImpl value,
          $Res Function(_$CommissioningWorkflowImpl) then) =
      __$$CommissioningWorkflowImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String chargerId,
      String technicianId,
      List<WorkflowStep> steps,
      CommissioningStep currentStep,
      DateTime startedAt,
      DateTime? completedAt,
      String? notes,
      Map<String, dynamic>? configuration,
      List<String>? attachments,
      bool? isCompleted,
      double? progressPercentage});
}

/// @nodoc
class __$$CommissioningWorkflowImplCopyWithImpl<$Res>
    extends _$CommissioningWorkflowCopyWithImpl<$Res,
        _$CommissioningWorkflowImpl>
    implements _$$CommissioningWorkflowImplCopyWith<$Res> {
  __$$CommissioningWorkflowImplCopyWithImpl(_$CommissioningWorkflowImpl _value,
      $Res Function(_$CommissioningWorkflowImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningWorkflow
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? chargerId = null,
    Object? technicianId = null,
    Object? steps = null,
    Object? currentStep = null,
    Object? startedAt = null,
    Object? completedAt = freezed,
    Object? notes = freezed,
    Object? configuration = freezed,
    Object? attachments = freezed,
    Object? isCompleted = freezed,
    Object? progressPercentage = freezed,
  }) {
    return _then(_$CommissioningWorkflowImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      technicianId: null == technicianId
          ? _value.technicianId
          : technicianId // ignore: cast_nullable_to_non_nullable
              as String,
      steps: null == steps
          ? _value._steps
          : steps // ignore: cast_nullable_to_non_nullable
              as List<WorkflowStep>,
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as CommissioningStep,
      startedAt: null == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      configuration: freezed == configuration
          ? _value._configuration
          : configuration // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      attachments: freezed == attachments
          ? _value._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      isCompleted: freezed == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      progressPercentage: freezed == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommissioningWorkflowImpl implements _CommissioningWorkflow {
  const _$CommissioningWorkflowImpl(
      {required this.id,
      required this.chargerId,
      required this.technicianId,
      required final List<WorkflowStep> steps,
      required this.currentStep,
      required this.startedAt,
      this.completedAt,
      this.notes,
      final Map<String, dynamic>? configuration,
      final List<String>? attachments,
      this.isCompleted,
      this.progressPercentage})
      : _steps = steps,
        _configuration = configuration,
        _attachments = attachments;

  factory _$CommissioningWorkflowImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommissioningWorkflowImplFromJson(json);

  @override
  final String id;
  @override
  final String chargerId;
  @override
  final String technicianId;
  final List<WorkflowStep> _steps;
  @override
  List<WorkflowStep> get steps {
    if (_steps is EqualUnmodifiableListView) return _steps;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_steps);
  }

  @override
  final CommissioningStep currentStep;
  @override
  final DateTime startedAt;
  @override
  final DateTime? completedAt;
  @override
  final String? notes;
  final Map<String, dynamic>? _configuration;
  @override
  Map<String, dynamic>? get configuration {
    final value = _configuration;
    if (value == null) return null;
    if (_configuration is EqualUnmodifiableMapView) return _configuration;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _attachments;
  @override
  List<String>? get attachments {
    final value = _attachments;
    if (value == null) return null;
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? isCompleted;
  @override
  final double? progressPercentage;

  @override
  String toString() {
    return 'CommissioningWorkflow(id: $id, chargerId: $chargerId, technicianId: $technicianId, steps: $steps, currentStep: $currentStep, startedAt: $startedAt, completedAt: $completedAt, notes: $notes, configuration: $configuration, attachments: $attachments, isCompleted: $isCompleted, progressPercentage: $progressPercentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommissioningWorkflowImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.chargerId, chargerId) ||
                other.chargerId == chargerId) &&
            (identical(other.technicianId, technicianId) ||
                other.technicianId == technicianId) &&
            const DeepCollectionEquality().equals(other._steps, _steps) &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.startedAt, startedAt) ||
                other.startedAt == startedAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            const DeepCollectionEquality()
                .equals(other._configuration, _configuration) &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.progressPercentage, progressPercentage) ||
                other.progressPercentage == progressPercentage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      chargerId,
      technicianId,
      const DeepCollectionEquality().hash(_steps),
      currentStep,
      startedAt,
      completedAt,
      notes,
      const DeepCollectionEquality().hash(_configuration),
      const DeepCollectionEquality().hash(_attachments),
      isCompleted,
      progressPercentage);

  /// Create a copy of CommissioningWorkflow
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommissioningWorkflowImplCopyWith<_$CommissioningWorkflowImpl>
      get copyWith => __$$CommissioningWorkflowImplCopyWithImpl<
          _$CommissioningWorkflowImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommissioningWorkflowImplToJson(
      this,
    );
  }
}

abstract class _CommissioningWorkflow implements CommissioningWorkflow {
  const factory _CommissioningWorkflow(
      {required final String id,
      required final String chargerId,
      required final String technicianId,
      required final List<WorkflowStep> steps,
      required final CommissioningStep currentStep,
      required final DateTime startedAt,
      final DateTime? completedAt,
      final String? notes,
      final Map<String, dynamic>? configuration,
      final List<String>? attachments,
      final bool? isCompleted,
      final double? progressPercentage}) = _$CommissioningWorkflowImpl;

  factory _CommissioningWorkflow.fromJson(Map<String, dynamic> json) =
      _$CommissioningWorkflowImpl.fromJson;

  @override
  String get id;
  @override
  String get chargerId;
  @override
  String get technicianId;
  @override
  List<WorkflowStep> get steps;
  @override
  CommissioningStep get currentStep;
  @override
  DateTime get startedAt;
  @override
  DateTime? get completedAt;
  @override
  String? get notes;
  @override
  Map<String, dynamic>? get configuration;
  @override
  List<String>? get attachments;
  @override
  bool? get isCompleted;
  @override
  double? get progressPercentage;

  /// Create a copy of CommissioningWorkflow
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommissioningWorkflowImplCopyWith<_$CommissioningWorkflowImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CommissioningTemplate _$CommissioningTemplateFromJson(
    Map<String, dynamic> json) {
  return _CommissioningTemplate.fromJson(json);
}

/// @nodoc
mixin _$CommissioningTemplate {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  List<CommissioningStep> get steps => throw _privateConstructorUsedError;
  Map<String, dynamic> get defaultParameters =>
      throw _privateConstructorUsedError;
  String? get chargerModel => throw _privateConstructorUsedError;
  String? get version => throw _privateConstructorUsedError;
  bool? get isDefault => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;

  /// Serializes this CommissioningTemplate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CommissioningTemplate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CommissioningTemplateCopyWith<CommissioningTemplate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommissioningTemplateCopyWith<$Res> {
  factory $CommissioningTemplateCopyWith(CommissioningTemplate value,
          $Res Function(CommissioningTemplate) then) =
      _$CommissioningTemplateCopyWithImpl<$Res, CommissioningTemplate>;
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      List<CommissioningStep> steps,
      Map<String, dynamic> defaultParameters,
      String? chargerModel,
      String? version,
      bool? isDefault,
      DateTime? createdAt,
      String? createdBy});
}

/// @nodoc
class _$CommissioningTemplateCopyWithImpl<$Res,
        $Val extends CommissioningTemplate>
    implements $CommissioningTemplateCopyWith<$Res> {
  _$CommissioningTemplateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommissioningTemplate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? steps = null,
    Object? defaultParameters = null,
    Object? chargerModel = freezed,
    Object? version = freezed,
    Object? isDefault = freezed,
    Object? createdAt = freezed,
    Object? createdBy = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      steps: null == steps
          ? _value.steps
          : steps // ignore: cast_nullable_to_non_nullable
              as List<CommissioningStep>,
      defaultParameters: null == defaultParameters
          ? _value.defaultParameters
          : defaultParameters // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      chargerModel: freezed == chargerModel
          ? _value.chargerModel
          : chargerModel // ignore: cast_nullable_to_non_nullable
              as String?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
      isDefault: freezed == isDefault
          ? _value.isDefault
          : isDefault // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CommissioningTemplateImplCopyWith<$Res>
    implements $CommissioningTemplateCopyWith<$Res> {
  factory _$$CommissioningTemplateImplCopyWith(
          _$CommissioningTemplateImpl value,
          $Res Function(_$CommissioningTemplateImpl) then) =
      __$$CommissioningTemplateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      List<CommissioningStep> steps,
      Map<String, dynamic> defaultParameters,
      String? chargerModel,
      String? version,
      bool? isDefault,
      DateTime? createdAt,
      String? createdBy});
}

/// @nodoc
class __$$CommissioningTemplateImplCopyWithImpl<$Res>
    extends _$CommissioningTemplateCopyWithImpl<$Res,
        _$CommissioningTemplateImpl>
    implements _$$CommissioningTemplateImplCopyWith<$Res> {
  __$$CommissioningTemplateImplCopyWithImpl(_$CommissioningTemplateImpl _value,
      $Res Function(_$CommissioningTemplateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningTemplate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? steps = null,
    Object? defaultParameters = null,
    Object? chargerModel = freezed,
    Object? version = freezed,
    Object? isDefault = freezed,
    Object? createdAt = freezed,
    Object? createdBy = freezed,
  }) {
    return _then(_$CommissioningTemplateImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      steps: null == steps
          ? _value._steps
          : steps // ignore: cast_nullable_to_non_nullable
              as List<CommissioningStep>,
      defaultParameters: null == defaultParameters
          ? _value._defaultParameters
          : defaultParameters // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      chargerModel: freezed == chargerModel
          ? _value.chargerModel
          : chargerModel // ignore: cast_nullable_to_non_nullable
              as String?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
      isDefault: freezed == isDefault
          ? _value.isDefault
          : isDefault // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommissioningTemplateImpl implements _CommissioningTemplate {
  const _$CommissioningTemplateImpl(
      {required this.id,
      required this.name,
      required this.description,
      required final List<CommissioningStep> steps,
      required final Map<String, dynamic> defaultParameters,
      this.chargerModel,
      this.version,
      this.isDefault,
      this.createdAt,
      this.createdBy})
      : _steps = steps,
        _defaultParameters = defaultParameters;

  factory _$CommissioningTemplateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommissioningTemplateImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  final List<CommissioningStep> _steps;
  @override
  List<CommissioningStep> get steps {
    if (_steps is EqualUnmodifiableListView) return _steps;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_steps);
  }

  final Map<String, dynamic> _defaultParameters;
  @override
  Map<String, dynamic> get defaultParameters {
    if (_defaultParameters is EqualUnmodifiableMapView)
      return _defaultParameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_defaultParameters);
  }

  @override
  final String? chargerModel;
  @override
  final String? version;
  @override
  final bool? isDefault;
  @override
  final DateTime? createdAt;
  @override
  final String? createdBy;

  @override
  String toString() {
    return 'CommissioningTemplate(id: $id, name: $name, description: $description, steps: $steps, defaultParameters: $defaultParameters, chargerModel: $chargerModel, version: $version, isDefault: $isDefault, createdAt: $createdAt, createdBy: $createdBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommissioningTemplateImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._steps, _steps) &&
            const DeepCollectionEquality()
                .equals(other._defaultParameters, _defaultParameters) &&
            (identical(other.chargerModel, chargerModel) ||
                other.chargerModel == chargerModel) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.isDefault, isDefault) ||
                other.isDefault == isDefault) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      const DeepCollectionEquality().hash(_steps),
      const DeepCollectionEquality().hash(_defaultParameters),
      chargerModel,
      version,
      isDefault,
      createdAt,
      createdBy);

  /// Create a copy of CommissioningTemplate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommissioningTemplateImplCopyWith<_$CommissioningTemplateImpl>
      get copyWith => __$$CommissioningTemplateImplCopyWithImpl<
          _$CommissioningTemplateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommissioningTemplateImplToJson(
      this,
    );
  }
}

abstract class _CommissioningTemplate implements CommissioningTemplate {
  const factory _CommissioningTemplate(
      {required final String id,
      required final String name,
      required final String description,
      required final List<CommissioningStep> steps,
      required final Map<String, dynamic> defaultParameters,
      final String? chargerModel,
      final String? version,
      final bool? isDefault,
      final DateTime? createdAt,
      final String? createdBy}) = _$CommissioningTemplateImpl;

  factory _CommissioningTemplate.fromJson(Map<String, dynamic> json) =
      _$CommissioningTemplateImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  List<CommissioningStep> get steps;
  @override
  Map<String, dynamic> get defaultParameters;
  @override
  String? get chargerModel;
  @override
  String? get version;
  @override
  bool? get isDefault;
  @override
  DateTime? get createdAt;
  @override
  String? get createdBy;

  /// Create a copy of CommissioningTemplate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommissioningTemplateImplCopyWith<_$CommissioningTemplateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CommissioningSession _$CommissioningSessionFromJson(Map<String, dynamic> json) {
  return _CommissioningSession.fromJson(json);
}

/// @nodoc
mixin _$CommissioningSession {
  String get sessionId => throw _privateConstructorUsedError;
  String get chargerId => throw _privateConstructorUsedError;
  ChargerConnection get connection => throw _privateConstructorUsedError;
  ChargerInfo? get chargerInfo => throw _privateConstructorUsedError;
  NetworkConfig? get networkConfig => throw _privateConstructorUsedError;
  OCPPCredentials? get ocppCredentials => throw _privateConstructorUsedError;
  FirmwareInfo? get firmwareInfo => throw _privateConstructorUsedError;
  Map<String, dynamic>? get parameters => throw _privateConstructorUsedError;
  Map<String, dynamic>? get diagnostics => throw _privateConstructorUsedError;
  DateTime? get sessionStarted => throw _privateConstructorUsedError;
  DateTime? get lastActivity => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;

  /// Serializes this CommissioningSession to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CommissioningSessionCopyWith<CommissioningSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommissioningSessionCopyWith<$Res> {
  factory $CommissioningSessionCopyWith(CommissioningSession value,
          $Res Function(CommissioningSession) then) =
      _$CommissioningSessionCopyWithImpl<$Res, CommissioningSession>;
  @useResult
  $Res call(
      {String sessionId,
      String chargerId,
      ChargerConnection connection,
      ChargerInfo? chargerInfo,
      NetworkConfig? networkConfig,
      OCPPCredentials? ocppCredentials,
      FirmwareInfo? firmwareInfo,
      Map<String, dynamic>? parameters,
      Map<String, dynamic>? diagnostics,
      DateTime? sessionStarted,
      DateTime? lastActivity,
      bool? isActive});

  $ChargerConnectionCopyWith<$Res> get connection;
  $ChargerInfoCopyWith<$Res>? get chargerInfo;
  $NetworkConfigCopyWith<$Res>? get networkConfig;
  $OCPPCredentialsCopyWith<$Res>? get ocppCredentials;
  $FirmwareInfoCopyWith<$Res>? get firmwareInfo;
}

/// @nodoc
class _$CommissioningSessionCopyWithImpl<$Res,
        $Val extends CommissioningSession>
    implements $CommissioningSessionCopyWith<$Res> {
  _$CommissioningSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? chargerId = null,
    Object? connection = null,
    Object? chargerInfo = freezed,
    Object? networkConfig = freezed,
    Object? ocppCredentials = freezed,
    Object? firmwareInfo = freezed,
    Object? parameters = freezed,
    Object? diagnostics = freezed,
    Object? sessionStarted = freezed,
    Object? lastActivity = freezed,
    Object? isActive = freezed,
  }) {
    return _then(_value.copyWith(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      connection: null == connection
          ? _value.connection
          : connection // ignore: cast_nullable_to_non_nullable
              as ChargerConnection,
      chargerInfo: freezed == chargerInfo
          ? _value.chargerInfo
          : chargerInfo // ignore: cast_nullable_to_non_nullable
              as ChargerInfo?,
      networkConfig: freezed == networkConfig
          ? _value.networkConfig
          : networkConfig // ignore: cast_nullable_to_non_nullable
              as NetworkConfig?,
      ocppCredentials: freezed == ocppCredentials
          ? _value.ocppCredentials
          : ocppCredentials // ignore: cast_nullable_to_non_nullable
              as OCPPCredentials?,
      firmwareInfo: freezed == firmwareInfo
          ? _value.firmwareInfo
          : firmwareInfo // ignore: cast_nullable_to_non_nullable
              as FirmwareInfo?,
      parameters: freezed == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      diagnostics: freezed == diagnostics
          ? _value.diagnostics
          : diagnostics // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      sessionStarted: freezed == sessionStarted
          ? _value.sessionStarted
          : sessionStarted // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastActivity: freezed == lastActivity
          ? _value.lastActivity
          : lastActivity // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChargerConnectionCopyWith<$Res> get connection {
    return $ChargerConnectionCopyWith<$Res>(_value.connection, (value) {
      return _then(_value.copyWith(connection: value) as $Val);
    });
  }

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChargerInfoCopyWith<$Res>? get chargerInfo {
    if (_value.chargerInfo == null) {
      return null;
    }

    return $ChargerInfoCopyWith<$Res>(_value.chargerInfo!, (value) {
      return _then(_value.copyWith(chargerInfo: value) as $Val);
    });
  }

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NetworkConfigCopyWith<$Res>? get networkConfig {
    if (_value.networkConfig == null) {
      return null;
    }

    return $NetworkConfigCopyWith<$Res>(_value.networkConfig!, (value) {
      return _then(_value.copyWith(networkConfig: value) as $Val);
    });
  }

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OCPPCredentialsCopyWith<$Res>? get ocppCredentials {
    if (_value.ocppCredentials == null) {
      return null;
    }

    return $OCPPCredentialsCopyWith<$Res>(_value.ocppCredentials!, (value) {
      return _then(_value.copyWith(ocppCredentials: value) as $Val);
    });
  }

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FirmwareInfoCopyWith<$Res>? get firmwareInfo {
    if (_value.firmwareInfo == null) {
      return null;
    }

    return $FirmwareInfoCopyWith<$Res>(_value.firmwareInfo!, (value) {
      return _then(_value.copyWith(firmwareInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CommissioningSessionImplCopyWith<$Res>
    implements $CommissioningSessionCopyWith<$Res> {
  factory _$$CommissioningSessionImplCopyWith(_$CommissioningSessionImpl value,
          $Res Function(_$CommissioningSessionImpl) then) =
      __$$CommissioningSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String sessionId,
      String chargerId,
      ChargerConnection connection,
      ChargerInfo? chargerInfo,
      NetworkConfig? networkConfig,
      OCPPCredentials? ocppCredentials,
      FirmwareInfo? firmwareInfo,
      Map<String, dynamic>? parameters,
      Map<String, dynamic>? diagnostics,
      DateTime? sessionStarted,
      DateTime? lastActivity,
      bool? isActive});

  @override
  $ChargerConnectionCopyWith<$Res> get connection;
  @override
  $ChargerInfoCopyWith<$Res>? get chargerInfo;
  @override
  $NetworkConfigCopyWith<$Res>? get networkConfig;
  @override
  $OCPPCredentialsCopyWith<$Res>? get ocppCredentials;
  @override
  $FirmwareInfoCopyWith<$Res>? get firmwareInfo;
}

/// @nodoc
class __$$CommissioningSessionImplCopyWithImpl<$Res>
    extends _$CommissioningSessionCopyWithImpl<$Res, _$CommissioningSessionImpl>
    implements _$$CommissioningSessionImplCopyWith<$Res> {
  __$$CommissioningSessionImplCopyWithImpl(_$CommissioningSessionImpl _value,
      $Res Function(_$CommissioningSessionImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? chargerId = null,
    Object? connection = null,
    Object? chargerInfo = freezed,
    Object? networkConfig = freezed,
    Object? ocppCredentials = freezed,
    Object? firmwareInfo = freezed,
    Object? parameters = freezed,
    Object? diagnostics = freezed,
    Object? sessionStarted = freezed,
    Object? lastActivity = freezed,
    Object? isActive = freezed,
  }) {
    return _then(_$CommissioningSessionImpl(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      connection: null == connection
          ? _value.connection
          : connection // ignore: cast_nullable_to_non_nullable
              as ChargerConnection,
      chargerInfo: freezed == chargerInfo
          ? _value.chargerInfo
          : chargerInfo // ignore: cast_nullable_to_non_nullable
              as ChargerInfo?,
      networkConfig: freezed == networkConfig
          ? _value.networkConfig
          : networkConfig // ignore: cast_nullable_to_non_nullable
              as NetworkConfig?,
      ocppCredentials: freezed == ocppCredentials
          ? _value.ocppCredentials
          : ocppCredentials // ignore: cast_nullable_to_non_nullable
              as OCPPCredentials?,
      firmwareInfo: freezed == firmwareInfo
          ? _value.firmwareInfo
          : firmwareInfo // ignore: cast_nullable_to_non_nullable
              as FirmwareInfo?,
      parameters: freezed == parameters
          ? _value._parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      diagnostics: freezed == diagnostics
          ? _value._diagnostics
          : diagnostics // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      sessionStarted: freezed == sessionStarted
          ? _value.sessionStarted
          : sessionStarted // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastActivity: freezed == lastActivity
          ? _value.lastActivity
          : lastActivity // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommissioningSessionImpl implements _CommissioningSession {
  const _$CommissioningSessionImpl(
      {required this.sessionId,
      required this.chargerId,
      required this.connection,
      this.chargerInfo,
      this.networkConfig,
      this.ocppCredentials,
      this.firmwareInfo,
      final Map<String, dynamic>? parameters,
      final Map<String, dynamic>? diagnostics,
      this.sessionStarted,
      this.lastActivity,
      this.isActive})
      : _parameters = parameters,
        _diagnostics = diagnostics;

  factory _$CommissioningSessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommissioningSessionImplFromJson(json);

  @override
  final String sessionId;
  @override
  final String chargerId;
  @override
  final ChargerConnection connection;
  @override
  final ChargerInfo? chargerInfo;
  @override
  final NetworkConfig? networkConfig;
  @override
  final OCPPCredentials? ocppCredentials;
  @override
  final FirmwareInfo? firmwareInfo;
  final Map<String, dynamic>? _parameters;
  @override
  Map<String, dynamic>? get parameters {
    final value = _parameters;
    if (value == null) return null;
    if (_parameters is EqualUnmodifiableMapView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, dynamic>? _diagnostics;
  @override
  Map<String, dynamic>? get diagnostics {
    final value = _diagnostics;
    if (value == null) return null;
    if (_diagnostics is EqualUnmodifiableMapView) return _diagnostics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? sessionStarted;
  @override
  final DateTime? lastActivity;
  @override
  final bool? isActive;

  @override
  String toString() {
    return 'CommissioningSession(sessionId: $sessionId, chargerId: $chargerId, connection: $connection, chargerInfo: $chargerInfo, networkConfig: $networkConfig, ocppCredentials: $ocppCredentials, firmwareInfo: $firmwareInfo, parameters: $parameters, diagnostics: $diagnostics, sessionStarted: $sessionStarted, lastActivity: $lastActivity, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommissioningSessionImpl &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.chargerId, chargerId) ||
                other.chargerId == chargerId) &&
            (identical(other.connection, connection) ||
                other.connection == connection) &&
            (identical(other.chargerInfo, chargerInfo) ||
                other.chargerInfo == chargerInfo) &&
            (identical(other.networkConfig, networkConfig) ||
                other.networkConfig == networkConfig) &&
            (identical(other.ocppCredentials, ocppCredentials) ||
                other.ocppCredentials == ocppCredentials) &&
            (identical(other.firmwareInfo, firmwareInfo) ||
                other.firmwareInfo == firmwareInfo) &&
            const DeepCollectionEquality()
                .equals(other._parameters, _parameters) &&
            const DeepCollectionEquality()
                .equals(other._diagnostics, _diagnostics) &&
            (identical(other.sessionStarted, sessionStarted) ||
                other.sessionStarted == sessionStarted) &&
            (identical(other.lastActivity, lastActivity) ||
                other.lastActivity == lastActivity) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionId,
      chargerId,
      connection,
      chargerInfo,
      networkConfig,
      ocppCredentials,
      firmwareInfo,
      const DeepCollectionEquality().hash(_parameters),
      const DeepCollectionEquality().hash(_diagnostics),
      sessionStarted,
      lastActivity,
      isActive);

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommissioningSessionImplCopyWith<_$CommissioningSessionImpl>
      get copyWith =>
          __$$CommissioningSessionImplCopyWithImpl<_$CommissioningSessionImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommissioningSessionImplToJson(
      this,
    );
  }
}

abstract class _CommissioningSession implements CommissioningSession {
  const factory _CommissioningSession(
      {required final String sessionId,
      required final String chargerId,
      required final ChargerConnection connection,
      final ChargerInfo? chargerInfo,
      final NetworkConfig? networkConfig,
      final OCPPCredentials? ocppCredentials,
      final FirmwareInfo? firmwareInfo,
      final Map<String, dynamic>? parameters,
      final Map<String, dynamic>? diagnostics,
      final DateTime? sessionStarted,
      final DateTime? lastActivity,
      final bool? isActive}) = _$CommissioningSessionImpl;

  factory _CommissioningSession.fromJson(Map<String, dynamic> json) =
      _$CommissioningSessionImpl.fromJson;

  @override
  String get sessionId;
  @override
  String get chargerId;
  @override
  ChargerConnection get connection;
  @override
  ChargerInfo? get chargerInfo;
  @override
  NetworkConfig? get networkConfig;
  @override
  OCPPCredentials? get ocppCredentials;
  @override
  FirmwareInfo? get firmwareInfo;
  @override
  Map<String, dynamic>? get parameters;
  @override
  Map<String, dynamic>? get diagnostics;
  @override
  DateTime? get sessionStarted;
  @override
  DateTime? get lastActivity;
  @override
  bool? get isActive;

  /// Create a copy of CommissioningSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommissioningSessionImplCopyWith<_$CommissioningSessionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CommissioningProgress _$CommissioningProgressFromJson(
    Map<String, dynamic> json) {
  return _CommissioningProgress.fromJson(json);
}

/// @nodoc
mixin _$CommissioningProgress {
  String get workflowId => throw _privateConstructorUsedError;
  CommissioningStep get currentStep => throw _privateConstructorUsedError;
  double get progressPercentage => throw _privateConstructorUsedError;
  String get statusMessage => throw _privateConstructorUsedError;
  StepStatus? get stepStatus => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  DateTime? get timestamp => throw _privateConstructorUsedError;
  Map<String, dynamic>? get stepData => throw _privateConstructorUsedError;

  /// Serializes this CommissioningProgress to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CommissioningProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CommissioningProgressCopyWith<CommissioningProgress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommissioningProgressCopyWith<$Res> {
  factory $CommissioningProgressCopyWith(CommissioningProgress value,
          $Res Function(CommissioningProgress) then) =
      _$CommissioningProgressCopyWithImpl<$Res, CommissioningProgress>;
  @useResult
  $Res call(
      {String workflowId,
      CommissioningStep currentStep,
      double progressPercentage,
      String statusMessage,
      StepStatus? stepStatus,
      String? errorMessage,
      DateTime? timestamp,
      Map<String, dynamic>? stepData});
}

/// @nodoc
class _$CommissioningProgressCopyWithImpl<$Res,
        $Val extends CommissioningProgress>
    implements $CommissioningProgressCopyWith<$Res> {
  _$CommissioningProgressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommissioningProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workflowId = null,
    Object? currentStep = null,
    Object? progressPercentage = null,
    Object? statusMessage = null,
    Object? stepStatus = freezed,
    Object? errorMessage = freezed,
    Object? timestamp = freezed,
    Object? stepData = freezed,
  }) {
    return _then(_value.copyWith(
      workflowId: null == workflowId
          ? _value.workflowId
          : workflowId // ignore: cast_nullable_to_non_nullable
              as String,
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as CommissioningStep,
      progressPercentage: null == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      statusMessage: null == statusMessage
          ? _value.statusMessage
          : statusMessage // ignore: cast_nullable_to_non_nullable
              as String,
      stepStatus: freezed == stepStatus
          ? _value.stepStatus
          : stepStatus // ignore: cast_nullable_to_non_nullable
              as StepStatus?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      stepData: freezed == stepData
          ? _value.stepData
          : stepData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CommissioningProgressImplCopyWith<$Res>
    implements $CommissioningProgressCopyWith<$Res> {
  factory _$$CommissioningProgressImplCopyWith(
          _$CommissioningProgressImpl value,
          $Res Function(_$CommissioningProgressImpl) then) =
      __$$CommissioningProgressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String workflowId,
      CommissioningStep currentStep,
      double progressPercentage,
      String statusMessage,
      StepStatus? stepStatus,
      String? errorMessage,
      DateTime? timestamp,
      Map<String, dynamic>? stepData});
}

/// @nodoc
class __$$CommissioningProgressImplCopyWithImpl<$Res>
    extends _$CommissioningProgressCopyWithImpl<$Res,
        _$CommissioningProgressImpl>
    implements _$$CommissioningProgressImplCopyWith<$Res> {
  __$$CommissioningProgressImplCopyWithImpl(_$CommissioningProgressImpl _value,
      $Res Function(_$CommissioningProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workflowId = null,
    Object? currentStep = null,
    Object? progressPercentage = null,
    Object? statusMessage = null,
    Object? stepStatus = freezed,
    Object? errorMessage = freezed,
    Object? timestamp = freezed,
    Object? stepData = freezed,
  }) {
    return _then(_$CommissioningProgressImpl(
      workflowId: null == workflowId
          ? _value.workflowId
          : workflowId // ignore: cast_nullable_to_non_nullable
              as String,
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as CommissioningStep,
      progressPercentage: null == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      statusMessage: null == statusMessage
          ? _value.statusMessage
          : statusMessage // ignore: cast_nullable_to_non_nullable
              as String,
      stepStatus: freezed == stepStatus
          ? _value.stepStatus
          : stepStatus // ignore: cast_nullable_to_non_nullable
              as StepStatus?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      stepData: freezed == stepData
          ? _value._stepData
          : stepData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommissioningProgressImpl implements _CommissioningProgress {
  const _$CommissioningProgressImpl(
      {required this.workflowId,
      required this.currentStep,
      required this.progressPercentage,
      required this.statusMessage,
      this.stepStatus,
      this.errorMessage,
      this.timestamp,
      final Map<String, dynamic>? stepData})
      : _stepData = stepData;

  factory _$CommissioningProgressImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommissioningProgressImplFromJson(json);

  @override
  final String workflowId;
  @override
  final CommissioningStep currentStep;
  @override
  final double progressPercentage;
  @override
  final String statusMessage;
  @override
  final StepStatus? stepStatus;
  @override
  final String? errorMessage;
  @override
  final DateTime? timestamp;
  final Map<String, dynamic>? _stepData;
  @override
  Map<String, dynamic>? get stepData {
    final value = _stepData;
    if (value == null) return null;
    if (_stepData is EqualUnmodifiableMapView) return _stepData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningProgress(workflowId: $workflowId, currentStep: $currentStep, progressPercentage: $progressPercentage, statusMessage: $statusMessage, stepStatus: $stepStatus, errorMessage: $errorMessage, timestamp: $timestamp, stepData: $stepData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommissioningProgressImpl &&
            (identical(other.workflowId, workflowId) ||
                other.workflowId == workflowId) &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.progressPercentage, progressPercentage) ||
                other.progressPercentage == progressPercentage) &&
            (identical(other.statusMessage, statusMessage) ||
                other.statusMessage == statusMessage) &&
            (identical(other.stepStatus, stepStatus) ||
                other.stepStatus == stepStatus) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            const DeepCollectionEquality().equals(other._stepData, _stepData));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      workflowId,
      currentStep,
      progressPercentage,
      statusMessage,
      stepStatus,
      errorMessage,
      timestamp,
      const DeepCollectionEquality().hash(_stepData));

  /// Create a copy of CommissioningProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommissioningProgressImplCopyWith<_$CommissioningProgressImpl>
      get copyWith => __$$CommissioningProgressImplCopyWithImpl<
          _$CommissioningProgressImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommissioningProgressImplToJson(
      this,
    );
  }
}

abstract class _CommissioningProgress implements CommissioningProgress {
  const factory _CommissioningProgress(
      {required final String workflowId,
      required final CommissioningStep currentStep,
      required final double progressPercentage,
      required final String statusMessage,
      final StepStatus? stepStatus,
      final String? errorMessage,
      final DateTime? timestamp,
      final Map<String, dynamic>? stepData}) = _$CommissioningProgressImpl;

  factory _CommissioningProgress.fromJson(Map<String, dynamic> json) =
      _$CommissioningProgressImpl.fromJson;

  @override
  String get workflowId;
  @override
  CommissioningStep get currentStep;
  @override
  double get progressPercentage;
  @override
  String get statusMessage;
  @override
  StepStatus? get stepStatus;
  @override
  String? get errorMessage;
  @override
  DateTime? get timestamp;
  @override
  Map<String, dynamic>? get stepData;

  /// Create a copy of CommissioningProgress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommissioningProgressImplCopyWith<_$CommissioningProgressImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CommissioningResult _$CommissioningResultFromJson(Map<String, dynamic> json) {
  return _CommissioningResult.fromJson(json);
}

/// @nodoc
mixin _$CommissioningResult {
  String get workflowId => throw _privateConstructorUsedError;
  String get chargerId => throw _privateConstructorUsedError;
  bool get isSuccessful => throw _privateConstructorUsedError;
  DateTime get completedAt => throw _privateConstructorUsedError;
  Duration get totalDuration => throw _privateConstructorUsedError;
  List<WorkflowStep>? get completedSteps => throw _privateConstructorUsedError;
  List<WorkflowStep>? get failedSteps => throw _privateConstructorUsedError;
  Map<String, dynamic>? get finalConfiguration =>
      throw _privateConstructorUsedError;
  Map<String, dynamic>? get diagnosticsReport =>
      throw _privateConstructorUsedError;
  List<String>? get complianceCertificates =>
      throw _privateConstructorUsedError;
  String? get summary => throw _privateConstructorUsedError;
  List<String>? get recommendations => throw _privateConstructorUsedError;

  /// Serializes this CommissioningResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CommissioningResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CommissioningResultCopyWith<CommissioningResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommissioningResultCopyWith<$Res> {
  factory $CommissioningResultCopyWith(
          CommissioningResult value, $Res Function(CommissioningResult) then) =
      _$CommissioningResultCopyWithImpl<$Res, CommissioningResult>;
  @useResult
  $Res call(
      {String workflowId,
      String chargerId,
      bool isSuccessful,
      DateTime completedAt,
      Duration totalDuration,
      List<WorkflowStep>? completedSteps,
      List<WorkflowStep>? failedSteps,
      Map<String, dynamic>? finalConfiguration,
      Map<String, dynamic>? diagnosticsReport,
      List<String>? complianceCertificates,
      String? summary,
      List<String>? recommendations});
}

/// @nodoc
class _$CommissioningResultCopyWithImpl<$Res, $Val extends CommissioningResult>
    implements $CommissioningResultCopyWith<$Res> {
  _$CommissioningResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommissioningResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workflowId = null,
    Object? chargerId = null,
    Object? isSuccessful = null,
    Object? completedAt = null,
    Object? totalDuration = null,
    Object? completedSteps = freezed,
    Object? failedSteps = freezed,
    Object? finalConfiguration = freezed,
    Object? diagnosticsReport = freezed,
    Object? complianceCertificates = freezed,
    Object? summary = freezed,
    Object? recommendations = freezed,
  }) {
    return _then(_value.copyWith(
      workflowId: null == workflowId
          ? _value.workflowId
          : workflowId // ignore: cast_nullable_to_non_nullable
              as String,
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      isSuccessful: null == isSuccessful
          ? _value.isSuccessful
          : isSuccessful // ignore: cast_nullable_to_non_nullable
              as bool,
      completedAt: null == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      totalDuration: null == totalDuration
          ? _value.totalDuration
          : totalDuration // ignore: cast_nullable_to_non_nullable
              as Duration,
      completedSteps: freezed == completedSteps
          ? _value.completedSteps
          : completedSteps // ignore: cast_nullable_to_non_nullable
              as List<WorkflowStep>?,
      failedSteps: freezed == failedSteps
          ? _value.failedSteps
          : failedSteps // ignore: cast_nullable_to_non_nullable
              as List<WorkflowStep>?,
      finalConfiguration: freezed == finalConfiguration
          ? _value.finalConfiguration
          : finalConfiguration // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      diagnosticsReport: freezed == diagnosticsReport
          ? _value.diagnosticsReport
          : diagnosticsReport // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      complianceCertificates: freezed == complianceCertificates
          ? _value.complianceCertificates
          : complianceCertificates // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      summary: freezed == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendations: freezed == recommendations
          ? _value.recommendations
          : recommendations // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CommissioningResultImplCopyWith<$Res>
    implements $CommissioningResultCopyWith<$Res> {
  factory _$$CommissioningResultImplCopyWith(_$CommissioningResultImpl value,
          $Res Function(_$CommissioningResultImpl) then) =
      __$$CommissioningResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String workflowId,
      String chargerId,
      bool isSuccessful,
      DateTime completedAt,
      Duration totalDuration,
      List<WorkflowStep>? completedSteps,
      List<WorkflowStep>? failedSteps,
      Map<String, dynamic>? finalConfiguration,
      Map<String, dynamic>? diagnosticsReport,
      List<String>? complianceCertificates,
      String? summary,
      List<String>? recommendations});
}

/// @nodoc
class __$$CommissioningResultImplCopyWithImpl<$Res>
    extends _$CommissioningResultCopyWithImpl<$Res, _$CommissioningResultImpl>
    implements _$$CommissioningResultImplCopyWith<$Res> {
  __$$CommissioningResultImplCopyWithImpl(_$CommissioningResultImpl _value,
      $Res Function(_$CommissioningResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workflowId = null,
    Object? chargerId = null,
    Object? isSuccessful = null,
    Object? completedAt = null,
    Object? totalDuration = null,
    Object? completedSteps = freezed,
    Object? failedSteps = freezed,
    Object? finalConfiguration = freezed,
    Object? diagnosticsReport = freezed,
    Object? complianceCertificates = freezed,
    Object? summary = freezed,
    Object? recommendations = freezed,
  }) {
    return _then(_$CommissioningResultImpl(
      workflowId: null == workflowId
          ? _value.workflowId
          : workflowId // ignore: cast_nullable_to_non_nullable
              as String,
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      isSuccessful: null == isSuccessful
          ? _value.isSuccessful
          : isSuccessful // ignore: cast_nullable_to_non_nullable
              as bool,
      completedAt: null == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      totalDuration: null == totalDuration
          ? _value.totalDuration
          : totalDuration // ignore: cast_nullable_to_non_nullable
              as Duration,
      completedSteps: freezed == completedSteps
          ? _value._completedSteps
          : completedSteps // ignore: cast_nullable_to_non_nullable
              as List<WorkflowStep>?,
      failedSteps: freezed == failedSteps
          ? _value._failedSteps
          : failedSteps // ignore: cast_nullable_to_non_nullable
              as List<WorkflowStep>?,
      finalConfiguration: freezed == finalConfiguration
          ? _value._finalConfiguration
          : finalConfiguration // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      diagnosticsReport: freezed == diagnosticsReport
          ? _value._diagnosticsReport
          : diagnosticsReport // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      complianceCertificates: freezed == complianceCertificates
          ? _value._complianceCertificates
          : complianceCertificates // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      summary: freezed == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendations: freezed == recommendations
          ? _value._recommendations
          : recommendations // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommissioningResultImpl implements _CommissioningResult {
  const _$CommissioningResultImpl(
      {required this.workflowId,
      required this.chargerId,
      required this.isSuccessful,
      required this.completedAt,
      required this.totalDuration,
      final List<WorkflowStep>? completedSteps,
      final List<WorkflowStep>? failedSteps,
      final Map<String, dynamic>? finalConfiguration,
      final Map<String, dynamic>? diagnosticsReport,
      final List<String>? complianceCertificates,
      this.summary,
      final List<String>? recommendations})
      : _completedSteps = completedSteps,
        _failedSteps = failedSteps,
        _finalConfiguration = finalConfiguration,
        _diagnosticsReport = diagnosticsReport,
        _complianceCertificates = complianceCertificates,
        _recommendations = recommendations;

  factory _$CommissioningResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommissioningResultImplFromJson(json);

  @override
  final String workflowId;
  @override
  final String chargerId;
  @override
  final bool isSuccessful;
  @override
  final DateTime completedAt;
  @override
  final Duration totalDuration;
  final List<WorkflowStep>? _completedSteps;
  @override
  List<WorkflowStep>? get completedSteps {
    final value = _completedSteps;
    if (value == null) return null;
    if (_completedSteps is EqualUnmodifiableListView) return _completedSteps;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<WorkflowStep>? _failedSteps;
  @override
  List<WorkflowStep>? get failedSteps {
    final value = _failedSteps;
    if (value == null) return null;
    if (_failedSteps is EqualUnmodifiableListView) return _failedSteps;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final Map<String, dynamic>? _finalConfiguration;
  @override
  Map<String, dynamic>? get finalConfiguration {
    final value = _finalConfiguration;
    if (value == null) return null;
    if (_finalConfiguration is EqualUnmodifiableMapView)
      return _finalConfiguration;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, dynamic>? _diagnosticsReport;
  @override
  Map<String, dynamic>? get diagnosticsReport {
    final value = _diagnosticsReport;
    if (value == null) return null;
    if (_diagnosticsReport is EqualUnmodifiableMapView)
      return _diagnosticsReport;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _complianceCertificates;
  @override
  List<String>? get complianceCertificates {
    final value = _complianceCertificates;
    if (value == null) return null;
    if (_complianceCertificates is EqualUnmodifiableListView)
      return _complianceCertificates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? summary;
  final List<String>? _recommendations;
  @override
  List<String>? get recommendations {
    final value = _recommendations;
    if (value == null) return null;
    if (_recommendations is EqualUnmodifiableListView) return _recommendations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CommissioningResult(workflowId: $workflowId, chargerId: $chargerId, isSuccessful: $isSuccessful, completedAt: $completedAt, totalDuration: $totalDuration, completedSteps: $completedSteps, failedSteps: $failedSteps, finalConfiguration: $finalConfiguration, diagnosticsReport: $diagnosticsReport, complianceCertificates: $complianceCertificates, summary: $summary, recommendations: $recommendations)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommissioningResultImpl &&
            (identical(other.workflowId, workflowId) ||
                other.workflowId == workflowId) &&
            (identical(other.chargerId, chargerId) ||
                other.chargerId == chargerId) &&
            (identical(other.isSuccessful, isSuccessful) ||
                other.isSuccessful == isSuccessful) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.totalDuration, totalDuration) ||
                other.totalDuration == totalDuration) &&
            const DeepCollectionEquality()
                .equals(other._completedSteps, _completedSteps) &&
            const DeepCollectionEquality()
                .equals(other._failedSteps, _failedSteps) &&
            const DeepCollectionEquality()
                .equals(other._finalConfiguration, _finalConfiguration) &&
            const DeepCollectionEquality()
                .equals(other._diagnosticsReport, _diagnosticsReport) &&
            const DeepCollectionEquality().equals(
                other._complianceCertificates, _complianceCertificates) &&
            (identical(other.summary, summary) || other.summary == summary) &&
            const DeepCollectionEquality()
                .equals(other._recommendations, _recommendations));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      workflowId,
      chargerId,
      isSuccessful,
      completedAt,
      totalDuration,
      const DeepCollectionEquality().hash(_completedSteps),
      const DeepCollectionEquality().hash(_failedSteps),
      const DeepCollectionEquality().hash(_finalConfiguration),
      const DeepCollectionEquality().hash(_diagnosticsReport),
      const DeepCollectionEquality().hash(_complianceCertificates),
      summary,
      const DeepCollectionEquality().hash(_recommendations));

  /// Create a copy of CommissioningResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommissioningResultImplCopyWith<_$CommissioningResultImpl> get copyWith =>
      __$$CommissioningResultImplCopyWithImpl<_$CommissioningResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommissioningResultImplToJson(
      this,
    );
  }
}

abstract class _CommissioningResult implements CommissioningResult {
  const factory _CommissioningResult(
      {required final String workflowId,
      required final String chargerId,
      required final bool isSuccessful,
      required final DateTime completedAt,
      required final Duration totalDuration,
      final List<WorkflowStep>? completedSteps,
      final List<WorkflowStep>? failedSteps,
      final Map<String, dynamic>? finalConfiguration,
      final Map<String, dynamic>? diagnosticsReport,
      final List<String>? complianceCertificates,
      final String? summary,
      final List<String>? recommendations}) = _$CommissioningResultImpl;

  factory _CommissioningResult.fromJson(Map<String, dynamic> json) =
      _$CommissioningResultImpl.fromJson;

  @override
  String get workflowId;
  @override
  String get chargerId;
  @override
  bool get isSuccessful;
  @override
  DateTime get completedAt;
  @override
  Duration get totalDuration;
  @override
  List<WorkflowStep>? get completedSteps;
  @override
  List<WorkflowStep>? get failedSteps;
  @override
  Map<String, dynamic>? get finalConfiguration;
  @override
  Map<String, dynamic>? get diagnosticsReport;
  @override
  List<String>? get complianceCertificates;
  @override
  String? get summary;
  @override
  List<String>? get recommendations;

  /// Create a copy of CommissioningResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommissioningResultImplCopyWith<_$CommissioningResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
