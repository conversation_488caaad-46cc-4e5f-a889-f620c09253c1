// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'commissioning_errors.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CommissioningFailure {
  String get message => throw _privateConstructorUsedError;
  Map<String, dynamic>? get details => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CommissioningFailureCopyWith<CommissioningFailure> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommissioningFailureCopyWith<$Res> {
  factory $CommissioningFailureCopyWith(CommissioningFailure value,
          $Res Function(CommissioningFailure) then) =
      _$CommissioningFailureCopyWithImpl<$Res, CommissioningFailure>;
  @useResult
  $Res call({String message, Map<String, dynamic>? details});
}

/// @nodoc
class _$CommissioningFailureCopyWithImpl<$Res,
        $Val extends CommissioningFailure>
    implements $CommissioningFailureCopyWith<$Res> {
  _$CommissioningFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? details = freezed,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChargerDiscoveryFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$ChargerDiscoveryFailureImplCopyWith(
          _$ChargerDiscoveryFailureImpl value,
          $Res Function(_$ChargerDiscoveryFailureImpl) then) =
      __$$ChargerDiscoveryFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? errorCode, Map<String, dynamic>? details});
}

/// @nodoc
class __$$ChargerDiscoveryFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res,
        _$ChargerDiscoveryFailureImpl>
    implements _$$ChargerDiscoveryFailureImplCopyWith<$Res> {
  __$$ChargerDiscoveryFailureImplCopyWithImpl(
      _$ChargerDiscoveryFailureImpl _value,
      $Res Function(_$ChargerDiscoveryFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? details = freezed,
  }) {
    return _then(_$ChargerDiscoveryFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$ChargerDiscoveryFailureImpl implements ChargerDiscoveryFailure {
  const _$ChargerDiscoveryFailureImpl(
      {required this.message,
      this.errorCode,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.chargerDiscovery(message: $message, errorCode: $errorCode, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChargerDiscoveryFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChargerDiscoveryFailureImplCopyWith<_$ChargerDiscoveryFailureImpl>
      get copyWith => __$$ChargerDiscoveryFailureImplCopyWithImpl<
          _$ChargerDiscoveryFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return chargerDiscovery(message, errorCode, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return chargerDiscovery?.call(message, errorCode, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (chargerDiscovery != null) {
      return chargerDiscovery(message, errorCode, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return chargerDiscovery(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return chargerDiscovery?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (chargerDiscovery != null) {
      return chargerDiscovery(this);
    }
    return orElse();
  }
}

abstract class ChargerDiscoveryFailure implements CommissioningFailure {
  const factory ChargerDiscoveryFailure(
      {required final String message,
      final String? errorCode,
      final Map<String, dynamic>? details}) = _$ChargerDiscoveryFailureImpl;

  @override
  String get message;
  String? get errorCode;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChargerDiscoveryFailureImplCopyWith<_$ChargerDiscoveryFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChargerConnectionFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$ChargerConnectionFailureImplCopyWith(
          _$ChargerConnectionFailureImpl value,
          $Res Function(_$ChargerConnectionFailureImpl) then) =
      __$$ChargerConnectionFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      String? chargerId,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$ChargerConnectionFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res,
        _$ChargerConnectionFailureImpl>
    implements _$$ChargerConnectionFailureImplCopyWith<$Res> {
  __$$ChargerConnectionFailureImplCopyWithImpl(
      _$ChargerConnectionFailureImpl _value,
      $Res Function(_$ChargerConnectionFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? chargerId = freezed,
    Object? details = freezed,
  }) {
    return _then(_$ChargerConnectionFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      chargerId: freezed == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$ChargerConnectionFailureImpl implements ChargerConnectionFailure {
  const _$ChargerConnectionFailureImpl(
      {required this.message,
      this.errorCode,
      this.chargerId,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final String? chargerId;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.chargerConnection(message: $message, errorCode: $errorCode, chargerId: $chargerId, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChargerConnectionFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.chargerId, chargerId) ||
                other.chargerId == chargerId) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode, chargerId,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChargerConnectionFailureImplCopyWith<_$ChargerConnectionFailureImpl>
      get copyWith => __$$ChargerConnectionFailureImplCopyWithImpl<
          _$ChargerConnectionFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return chargerConnection(message, errorCode, chargerId, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return chargerConnection?.call(message, errorCode, chargerId, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (chargerConnection != null) {
      return chargerConnection(message, errorCode, chargerId, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return chargerConnection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return chargerConnection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (chargerConnection != null) {
      return chargerConnection(this);
    }
    return orElse();
  }
}

abstract class ChargerConnectionFailure implements CommissioningFailure {
  const factory ChargerConnectionFailure(
      {required final String message,
      final String? errorCode,
      final String? chargerId,
      final Map<String, dynamic>? details}) = _$ChargerConnectionFailureImpl;

  @override
  String get message;
  String? get errorCode;
  String? get chargerId;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChargerConnectionFailureImplCopyWith<_$ChargerConnectionFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NetworkConfigurationFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$NetworkConfigurationFailureImplCopyWith(
          _$NetworkConfigurationFailureImpl value,
          $Res Function(_$NetworkConfigurationFailureImpl) then) =
      __$$NetworkConfigurationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      String? networkName,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$NetworkConfigurationFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res,
        _$NetworkConfigurationFailureImpl>
    implements _$$NetworkConfigurationFailureImplCopyWith<$Res> {
  __$$NetworkConfigurationFailureImplCopyWithImpl(
      _$NetworkConfigurationFailureImpl _value,
      $Res Function(_$NetworkConfigurationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? networkName = freezed,
    Object? details = freezed,
  }) {
    return _then(_$NetworkConfigurationFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      networkName: freezed == networkName
          ? _value.networkName
          : networkName // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$NetworkConfigurationFailureImpl implements NetworkConfigurationFailure {
  const _$NetworkConfigurationFailureImpl(
      {required this.message,
      this.errorCode,
      this.networkName,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final String? networkName;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.networkConfiguration(message: $message, errorCode: $errorCode, networkName: $networkName, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkConfigurationFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.networkName, networkName) ||
                other.networkName == networkName) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode, networkName,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkConfigurationFailureImplCopyWith<_$NetworkConfigurationFailureImpl>
      get copyWith => __$$NetworkConfigurationFailureImplCopyWithImpl<
          _$NetworkConfigurationFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return networkConfiguration(message, errorCode, networkName, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return networkConfiguration?.call(message, errorCode, networkName, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (networkConfiguration != null) {
      return networkConfiguration(message, errorCode, networkName, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return networkConfiguration(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return networkConfiguration?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (networkConfiguration != null) {
      return networkConfiguration(this);
    }
    return orElse();
  }
}

abstract class NetworkConfigurationFailure implements CommissioningFailure {
  const factory NetworkConfigurationFailure(
      {required final String message,
      final String? errorCode,
      final String? networkName,
      final Map<String, dynamic>? details}) = _$NetworkConfigurationFailureImpl;

  @override
  String get message;
  String? get errorCode;
  String? get networkName;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkConfigurationFailureImplCopyWith<_$NetworkConfigurationFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OCPPConnectionFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$OCPPConnectionFailureImplCopyWith(
          _$OCPPConnectionFailureImpl value,
          $Res Function(_$OCPPConnectionFailureImpl) then) =
      __$$OCPPConnectionFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      String? cmsUrl,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$OCPPConnectionFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res,
        _$OCPPConnectionFailureImpl>
    implements _$$OCPPConnectionFailureImplCopyWith<$Res> {
  __$$OCPPConnectionFailureImplCopyWithImpl(_$OCPPConnectionFailureImpl _value,
      $Res Function(_$OCPPConnectionFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? cmsUrl = freezed,
    Object? details = freezed,
  }) {
    return _then(_$OCPPConnectionFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      cmsUrl: freezed == cmsUrl
          ? _value.cmsUrl
          : cmsUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$OCPPConnectionFailureImpl implements OCPPConnectionFailure {
  const _$OCPPConnectionFailureImpl(
      {required this.message,
      this.errorCode,
      this.cmsUrl,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final String? cmsUrl;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.ocppConnection(message: $message, errorCode: $errorCode, cmsUrl: $cmsUrl, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OCPPConnectionFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.cmsUrl, cmsUrl) || other.cmsUrl == cmsUrl) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode, cmsUrl,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OCPPConnectionFailureImplCopyWith<_$OCPPConnectionFailureImpl>
      get copyWith => __$$OCPPConnectionFailureImplCopyWithImpl<
          _$OCPPConnectionFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return ocppConnection(message, errorCode, cmsUrl, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return ocppConnection?.call(message, errorCode, cmsUrl, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (ocppConnection != null) {
      return ocppConnection(message, errorCode, cmsUrl, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return ocppConnection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return ocppConnection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (ocppConnection != null) {
      return ocppConnection(this);
    }
    return orElse();
  }
}

abstract class OCPPConnectionFailure implements CommissioningFailure {
  const factory OCPPConnectionFailure(
      {required final String message,
      final String? errorCode,
      final String? cmsUrl,
      final Map<String, dynamic>? details}) = _$OCPPConnectionFailureImpl;

  @override
  String get message;
  String? get errorCode;
  String? get cmsUrl;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OCPPConnectionFailureImplCopyWith<_$OCPPConnectionFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FirmwareUpdateFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$FirmwareUpdateFailureImplCopyWith(
          _$FirmwareUpdateFailureImpl value,
          $Res Function(_$FirmwareUpdateFailureImpl) then) =
      __$$FirmwareUpdateFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      String? firmwareVersion,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$FirmwareUpdateFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res,
        _$FirmwareUpdateFailureImpl>
    implements _$$FirmwareUpdateFailureImplCopyWith<$Res> {
  __$$FirmwareUpdateFailureImplCopyWithImpl(_$FirmwareUpdateFailureImpl _value,
      $Res Function(_$FirmwareUpdateFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? firmwareVersion = freezed,
    Object? details = freezed,
  }) {
    return _then(_$FirmwareUpdateFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      firmwareVersion: freezed == firmwareVersion
          ? _value.firmwareVersion
          : firmwareVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$FirmwareUpdateFailureImpl implements FirmwareUpdateFailure {
  const _$FirmwareUpdateFailureImpl(
      {required this.message,
      this.errorCode,
      this.firmwareVersion,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final String? firmwareVersion;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.firmwareUpdate(message: $message, errorCode: $errorCode, firmwareVersion: $firmwareVersion, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FirmwareUpdateFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.firmwareVersion, firmwareVersion) ||
                other.firmwareVersion == firmwareVersion) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode,
      firmwareVersion, const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FirmwareUpdateFailureImplCopyWith<_$FirmwareUpdateFailureImpl>
      get copyWith => __$$FirmwareUpdateFailureImplCopyWithImpl<
          _$FirmwareUpdateFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return firmwareUpdate(message, errorCode, firmwareVersion, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return firmwareUpdate?.call(message, errorCode, firmwareVersion, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (firmwareUpdate != null) {
      return firmwareUpdate(message, errorCode, firmwareVersion, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return firmwareUpdate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return firmwareUpdate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (firmwareUpdate != null) {
      return firmwareUpdate(this);
    }
    return orElse();
  }
}

abstract class FirmwareUpdateFailure implements CommissioningFailure {
  const factory FirmwareUpdateFailure(
      {required final String message,
      final String? errorCode,
      final String? firmwareVersion,
      final Map<String, dynamic>? details}) = _$FirmwareUpdateFailureImpl;

  @override
  String get message;
  String? get errorCode;
  String? get firmwareVersion;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FirmwareUpdateFailureImplCopyWith<_$FirmwareUpdateFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ParameterConfigurationFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$ParameterConfigurationFailureImplCopyWith(
          _$ParameterConfigurationFailureImpl value,
          $Res Function(_$ParameterConfigurationFailureImpl) then) =
      __$$ParameterConfigurationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      String? parameterName,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$ParameterConfigurationFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res,
        _$ParameterConfigurationFailureImpl>
    implements _$$ParameterConfigurationFailureImplCopyWith<$Res> {
  __$$ParameterConfigurationFailureImplCopyWithImpl(
      _$ParameterConfigurationFailureImpl _value,
      $Res Function(_$ParameterConfigurationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? parameterName = freezed,
    Object? details = freezed,
  }) {
    return _then(_$ParameterConfigurationFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      parameterName: freezed == parameterName
          ? _value.parameterName
          : parameterName // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$ParameterConfigurationFailureImpl
    implements ParameterConfigurationFailure {
  const _$ParameterConfigurationFailureImpl(
      {required this.message,
      this.errorCode,
      this.parameterName,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final String? parameterName;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.parameterConfiguration(message: $message, errorCode: $errorCode, parameterName: $parameterName, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ParameterConfigurationFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.parameterName, parameterName) ||
                other.parameterName == parameterName) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode,
      parameterName, const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ParameterConfigurationFailureImplCopyWith<
          _$ParameterConfigurationFailureImpl>
      get copyWith => __$$ParameterConfigurationFailureImplCopyWithImpl<
          _$ParameterConfigurationFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return parameterConfiguration(message, errorCode, parameterName, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return parameterConfiguration?.call(
        message, errorCode, parameterName, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (parameterConfiguration != null) {
      return parameterConfiguration(message, errorCode, parameterName, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return parameterConfiguration(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return parameterConfiguration?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (parameterConfiguration != null) {
      return parameterConfiguration(this);
    }
    return orElse();
  }
}

abstract class ParameterConfigurationFailure implements CommissioningFailure {
  const factory ParameterConfigurationFailure(
          {required final String message,
          final String? errorCode,
          final String? parameterName,
          final Map<String, dynamic>? details}) =
      _$ParameterConfigurationFailureImpl;

  @override
  String get message;
  String? get errorCode;
  String? get parameterName;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ParameterConfigurationFailureImplCopyWith<
          _$ParameterConfigurationFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DiagnosticsFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$DiagnosticsFailureImplCopyWith(_$DiagnosticsFailureImpl value,
          $Res Function(_$DiagnosticsFailureImpl) then) =
      __$$DiagnosticsFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      String? diagnosticType,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$DiagnosticsFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res, _$DiagnosticsFailureImpl>
    implements _$$DiagnosticsFailureImplCopyWith<$Res> {
  __$$DiagnosticsFailureImplCopyWithImpl(_$DiagnosticsFailureImpl _value,
      $Res Function(_$DiagnosticsFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? diagnosticType = freezed,
    Object? details = freezed,
  }) {
    return _then(_$DiagnosticsFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      diagnosticType: freezed == diagnosticType
          ? _value.diagnosticType
          : diagnosticType // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$DiagnosticsFailureImpl implements DiagnosticsFailure {
  const _$DiagnosticsFailureImpl(
      {required this.message,
      this.errorCode,
      this.diagnosticType,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final String? diagnosticType;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.diagnostics(message: $message, errorCode: $errorCode, diagnosticType: $diagnosticType, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiagnosticsFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.diagnosticType, diagnosticType) ||
                other.diagnosticType == diagnosticType) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode,
      diagnosticType, const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DiagnosticsFailureImplCopyWith<_$DiagnosticsFailureImpl> get copyWith =>
      __$$DiagnosticsFailureImplCopyWithImpl<_$DiagnosticsFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return diagnostics(message, errorCode, diagnosticType, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return diagnostics?.call(message, errorCode, diagnosticType, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (diagnostics != null) {
      return diagnostics(message, errorCode, diagnosticType, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return diagnostics(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return diagnostics?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (diagnostics != null) {
      return diagnostics(this);
    }
    return orElse();
  }
}

abstract class DiagnosticsFailure implements CommissioningFailure {
  const factory DiagnosticsFailure(
      {required final String message,
      final String? errorCode,
      final String? diagnosticType,
      final Map<String, dynamic>? details}) = _$DiagnosticsFailureImpl;

  @override
  String get message;
  String? get errorCode;
  String? get diagnosticType;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DiagnosticsFailureImplCopyWith<_$DiagnosticsFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ComplianceFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$ComplianceFailureImplCopyWith(_$ComplianceFailureImpl value,
          $Res Function(_$ComplianceFailureImpl) then) =
      __$$ComplianceFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      String? standard,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$ComplianceFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res, _$ComplianceFailureImpl>
    implements _$$ComplianceFailureImplCopyWith<$Res> {
  __$$ComplianceFailureImplCopyWithImpl(_$ComplianceFailureImpl _value,
      $Res Function(_$ComplianceFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? standard = freezed,
    Object? details = freezed,
  }) {
    return _then(_$ComplianceFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      standard: freezed == standard
          ? _value.standard
          : standard // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$ComplianceFailureImpl implements ComplianceFailure {
  const _$ComplianceFailureImpl(
      {required this.message,
      this.errorCode,
      this.standard,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final String? standard;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.compliance(message: $message, errorCode: $errorCode, standard: $standard, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComplianceFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.standard, standard) ||
                other.standard == standard) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode, standard,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComplianceFailureImplCopyWith<_$ComplianceFailureImpl> get copyWith =>
      __$$ComplianceFailureImplCopyWithImpl<_$ComplianceFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return compliance(message, errorCode, standard, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return compliance?.call(message, errorCode, standard, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (compliance != null) {
      return compliance(message, errorCode, standard, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return compliance(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return compliance?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (compliance != null) {
      return compliance(this);
    }
    return orElse();
  }
}

abstract class ComplianceFailure implements CommissioningFailure {
  const factory ComplianceFailure(
      {required final String message,
      final String? errorCode,
      final String? standard,
      final Map<String, dynamic>? details}) = _$ComplianceFailureImpl;

  @override
  String get message;
  String? get errorCode;
  String? get standard;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComplianceFailureImplCopyWith<_$ComplianceFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AuthenticationFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$AuthenticationFailureImplCopyWith(
          _$AuthenticationFailureImpl value,
          $Res Function(_$AuthenticationFailureImpl) then) =
      __$$AuthenticationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      String? authMethod,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$AuthenticationFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res,
        _$AuthenticationFailureImpl>
    implements _$$AuthenticationFailureImplCopyWith<$Res> {
  __$$AuthenticationFailureImplCopyWithImpl(_$AuthenticationFailureImpl _value,
      $Res Function(_$AuthenticationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? authMethod = freezed,
    Object? details = freezed,
  }) {
    return _then(_$AuthenticationFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      authMethod: freezed == authMethod
          ? _value.authMethod
          : authMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$AuthenticationFailureImpl implements AuthenticationFailure {
  const _$AuthenticationFailureImpl(
      {required this.message,
      this.errorCode,
      this.authMethod,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final String? authMethod;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.authentication(message: $message, errorCode: $errorCode, authMethod: $authMethod, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthenticationFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.authMethod, authMethod) ||
                other.authMethod == authMethod) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode, authMethod,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthenticationFailureImplCopyWith<_$AuthenticationFailureImpl>
      get copyWith => __$$AuthenticationFailureImplCopyWithImpl<
          _$AuthenticationFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return authentication(message, errorCode, authMethod, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return authentication?.call(message, errorCode, authMethod, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(message, errorCode, authMethod, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return authentication(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return authentication?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(this);
    }
    return orElse();
  }
}

abstract class AuthenticationFailure implements CommissioningFailure {
  const factory AuthenticationFailure(
      {required final String message,
      final String? errorCode,
      final String? authMethod,
      final Map<String, dynamic>? details}) = _$AuthenticationFailureImpl;

  @override
  String get message;
  String? get errorCode;
  String? get authMethod;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthenticationFailureImplCopyWith<_$AuthenticationFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TimeoutFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$TimeoutFailureImplCopyWith(_$TimeoutFailureImpl value,
          $Res Function(_$TimeoutFailureImpl) then) =
      __$$TimeoutFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      Duration timeout,
      String? operation,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$TimeoutFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res, _$TimeoutFailureImpl>
    implements _$$TimeoutFailureImplCopyWith<$Res> {
  __$$TimeoutFailureImplCopyWithImpl(
      _$TimeoutFailureImpl _value, $Res Function(_$TimeoutFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? timeout = null,
    Object? operation = freezed,
    Object? details = freezed,
  }) {
    return _then(_$TimeoutFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      timeout: null == timeout
          ? _value.timeout
          : timeout // ignore: cast_nullable_to_non_nullable
              as Duration,
      operation: freezed == operation
          ? _value.operation
          : operation // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$TimeoutFailureImpl implements TimeoutFailure {
  const _$TimeoutFailureImpl(
      {required this.message,
      required this.timeout,
      this.operation,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final Duration timeout;
  @override
  final String? operation;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.timeout(message: $message, timeout: $timeout, operation: $operation, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimeoutFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.timeout, timeout) || other.timeout == timeout) &&
            (identical(other.operation, operation) ||
                other.operation == operation) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, timeout, operation,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimeoutFailureImplCopyWith<_$TimeoutFailureImpl> get copyWith =>
      __$$TimeoutFailureImplCopyWithImpl<_$TimeoutFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return timeout(message, this.timeout, operation, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return timeout?.call(message, this.timeout, operation, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (timeout != null) {
      return timeout(message, this.timeout, operation, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return timeout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return timeout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (timeout != null) {
      return timeout(this);
    }
    return orElse();
  }
}

abstract class TimeoutFailure implements CommissioningFailure {
  const factory TimeoutFailure(
      {required final String message,
      required final Duration timeout,
      final String? operation,
      final Map<String, dynamic>? details}) = _$TimeoutFailureImpl;

  @override
  String get message;
  Duration get timeout;
  String? get operation;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimeoutFailureImplCopyWith<_$TimeoutFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ValidationFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$ValidationFailureImplCopyWith(_$ValidationFailureImpl value,
          $Res Function(_$ValidationFailureImpl) then) =
      __$$ValidationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String field,
      String? expectedFormat,
      String? actualValue,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$ValidationFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res, _$ValidationFailureImpl>
    implements _$$ValidationFailureImplCopyWith<$Res> {
  __$$ValidationFailureImplCopyWithImpl(_$ValidationFailureImpl _value,
      $Res Function(_$ValidationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? field = null,
    Object? expectedFormat = freezed,
    Object? actualValue = freezed,
    Object? details = freezed,
  }) {
    return _then(_$ValidationFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      field: null == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String,
      expectedFormat: freezed == expectedFormat
          ? _value.expectedFormat
          : expectedFormat // ignore: cast_nullable_to_non_nullable
              as String?,
      actualValue: freezed == actualValue
          ? _value.actualValue
          : actualValue // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$ValidationFailureImpl implements ValidationFailure {
  const _$ValidationFailureImpl(
      {required this.message,
      required this.field,
      this.expectedFormat,
      this.actualValue,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String field;
  @override
  final String? expectedFormat;
  @override
  final String? actualValue;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.validation(message: $message, field: $field, expectedFormat: $expectedFormat, actualValue: $actualValue, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidationFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.field, field) || other.field == field) &&
            (identical(other.expectedFormat, expectedFormat) ||
                other.expectedFormat == expectedFormat) &&
            (identical(other.actualValue, actualValue) ||
                other.actualValue == actualValue) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, field, expectedFormat,
      actualValue, const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValidationFailureImplCopyWith<_$ValidationFailureImpl> get copyWith =>
      __$$ValidationFailureImplCopyWithImpl<_$ValidationFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return validation(message, field, expectedFormat, actualValue, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return validation?.call(
        message, field, expectedFormat, actualValue, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(message, field, expectedFormat, actualValue, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return validation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return validation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(this);
    }
    return orElse();
  }
}

abstract class ValidationFailure implements CommissioningFailure {
  const factory ValidationFailure(
      {required final String message,
      required final String field,
      final String? expectedFormat,
      final String? actualValue,
      final Map<String, dynamic>? details}) = _$ValidationFailureImpl;

  @override
  String get message;
  String get field;
  String? get expectedFormat;
  String? get actualValue;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValidationFailureImplCopyWith<_$ValidationFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PermissionFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$PermissionFailureImplCopyWith(_$PermissionFailureImpl value,
          $Res Function(_$PermissionFailureImpl) then) =
      __$$PermissionFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String permission,
      String? requiredLevel,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$PermissionFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res, _$PermissionFailureImpl>
    implements _$$PermissionFailureImplCopyWith<$Res> {
  __$$PermissionFailureImplCopyWithImpl(_$PermissionFailureImpl _value,
      $Res Function(_$PermissionFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? permission = null,
    Object? requiredLevel = freezed,
    Object? details = freezed,
  }) {
    return _then(_$PermissionFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      permission: null == permission
          ? _value.permission
          : permission // ignore: cast_nullable_to_non_nullable
              as String,
      requiredLevel: freezed == requiredLevel
          ? _value.requiredLevel
          : requiredLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$PermissionFailureImpl implements PermissionFailure {
  const _$PermissionFailureImpl(
      {required this.message,
      required this.permission,
      this.requiredLevel,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String permission;
  @override
  final String? requiredLevel;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.permission(message: $message, permission: $permission, requiredLevel: $requiredLevel, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PermissionFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.permission, permission) ||
                other.permission == permission) &&
            (identical(other.requiredLevel, requiredLevel) ||
                other.requiredLevel == requiredLevel) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, permission,
      requiredLevel, const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PermissionFailureImplCopyWith<_$PermissionFailureImpl> get copyWith =>
      __$$PermissionFailureImplCopyWithImpl<_$PermissionFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return permission(message, this.permission, requiredLevel, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return permission?.call(message, this.permission, requiredLevel, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(message, this.permission, requiredLevel, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return permission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return permission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(this);
    }
    return orElse();
  }
}

abstract class PermissionFailure implements CommissioningFailure {
  const factory PermissionFailure(
      {required final String message,
      required final String permission,
      final String? requiredLevel,
      final Map<String, dynamic>? details}) = _$PermissionFailureImpl;

  @override
  String get message;
  String get permission;
  String? get requiredLevel;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PermissionFailureImplCopyWith<_$PermissionFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StorageFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$StorageFailureImplCopyWith(_$StorageFailureImpl value,
          $Res Function(_$StorageFailureImpl) then) =
      __$$StorageFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      String? storageType,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$StorageFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res, _$StorageFailureImpl>
    implements _$$StorageFailureImplCopyWith<$Res> {
  __$$StorageFailureImplCopyWithImpl(
      _$StorageFailureImpl _value, $Res Function(_$StorageFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? storageType = freezed,
    Object? details = freezed,
  }) {
    return _then(_$StorageFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      storageType: freezed == storageType
          ? _value.storageType
          : storageType // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$StorageFailureImpl implements StorageFailure {
  const _$StorageFailureImpl(
      {required this.message,
      this.errorCode,
      this.storageType,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final String? storageType;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.storage(message: $message, errorCode: $errorCode, storageType: $storageType, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StorageFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.storageType, storageType) ||
                other.storageType == storageType) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode, storageType,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StorageFailureImplCopyWith<_$StorageFailureImpl> get copyWith =>
      __$$StorageFailureImplCopyWithImpl<_$StorageFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return storage(message, errorCode, storageType, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return storage?.call(message, errorCode, storageType, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (storage != null) {
      return storage(message, errorCode, storageType, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return storage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return storage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (storage != null) {
      return storage(this);
    }
    return orElse();
  }
}

abstract class StorageFailure implements CommissioningFailure {
  const factory StorageFailure(
      {required final String message,
      final String? errorCode,
      final String? storageType,
      final Map<String, dynamic>? details}) = _$StorageFailureImpl;

  @override
  String get message;
  String? get errorCode;
  String? get storageType;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StorageFailureImplCopyWith<_$StorageFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnknownCommissioningFailureImplCopyWith<$Res>
    implements $CommissioningFailureCopyWith<$Res> {
  factory _$$UnknownCommissioningFailureImplCopyWith(
          _$UnknownCommissioningFailureImpl value,
          $Res Function(_$UnknownCommissioningFailureImpl) then) =
      __$$UnknownCommissioningFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? errorCode,
      Exception? originalException,
      Map<String, dynamic>? details});
}

/// @nodoc
class __$$UnknownCommissioningFailureImplCopyWithImpl<$Res>
    extends _$CommissioningFailureCopyWithImpl<$Res,
        _$UnknownCommissioningFailureImpl>
    implements _$$UnknownCommissioningFailureImplCopyWith<$Res> {
  __$$UnknownCommissioningFailureImplCopyWithImpl(
      _$UnknownCommissioningFailureImpl _value,
      $Res Function(_$UnknownCommissioningFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? errorCode = freezed,
    Object? originalException = freezed,
    Object? details = freezed,
  }) {
    return _then(_$UnknownCommissioningFailureImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      originalException: freezed == originalException
          ? _value.originalException
          : originalException // ignore: cast_nullable_to_non_nullable
              as Exception?,
      details: freezed == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$UnknownCommissioningFailureImpl implements UnknownCommissioningFailure {
  const _$UnknownCommissioningFailureImpl(
      {required this.message,
      this.errorCode,
      this.originalException,
      final Map<String, dynamic>? details})
      : _details = details;

  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final Exception? originalException;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissioningFailure.unknown(message: $message, errorCode: $errorCode, originalException: $originalException, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnknownCommissioningFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.originalException, originalException) ||
                other.originalException == originalException) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, errorCode,
      originalException, const DeepCollectionEquality().hash(_details));

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnknownCommissioningFailureImplCopyWith<_$UnknownCommissioningFailureImpl>
      get copyWith => __$$UnknownCommissioningFailureImplCopyWithImpl<
          _$UnknownCommissioningFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)
        chargerDiscovery,
    required TResult Function(String message, String? errorCode,
            String? chargerId, Map<String, dynamic>? details)
        chargerConnection,
    required TResult Function(String message, String? errorCode,
            String? networkName, Map<String, dynamic>? details)
        networkConfiguration,
    required TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)
        ocppConnection,
    required TResult Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)
        firmwareUpdate,
    required TResult Function(String message, String? errorCode,
            String? parameterName, Map<String, dynamic>? details)
        parameterConfiguration,
    required TResult Function(String message, String? errorCode,
            String? diagnosticType, Map<String, dynamic>? details)
        diagnostics,
    required TResult Function(String message, String? errorCode,
            String? standard, Map<String, dynamic>? details)
        compliance,
    required TResult Function(String message, String? errorCode,
            String? authMethod, Map<String, dynamic>? details)
        authentication,
    required TResult Function(String message, Duration timeout,
            String? operation, Map<String, dynamic>? details)
        timeout,
    required TResult Function(
            String message,
            String field,
            String? expectedFormat,
            String? actualValue,
            Map<String, dynamic>? details)
        validation,
    required TResult Function(String message, String permission,
            String? requiredLevel, Map<String, dynamic>? details)
        permission,
    required TResult Function(String message, String? errorCode,
            String? storageType, Map<String, dynamic>? details)
        storage,
    required TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)
        unknown,
  }) {
    return unknown(message, errorCode, originalException, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult? Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult? Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult? Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult? Function(String message, String? errorCode,
            String? firmwareVersion, Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult? Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult? Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult? Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult? Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult? Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult? Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult? Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult? Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult? Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
  }) {
    return unknown?.call(message, errorCode, originalException, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String message, String? errorCode, Map<String, dynamic>? details)?
        chargerDiscovery,
    TResult Function(String message, String? errorCode, String? chargerId,
            Map<String, dynamic>? details)?
        chargerConnection,
    TResult Function(String message, String? errorCode, String? networkName,
            Map<String, dynamic>? details)?
        networkConfiguration,
    TResult Function(String message, String? errorCode, String? cmsUrl,
            Map<String, dynamic>? details)?
        ocppConnection,
    TResult Function(String message, String? errorCode, String? firmwareVersion,
            Map<String, dynamic>? details)?
        firmwareUpdate,
    TResult Function(String message, String? errorCode, String? parameterName,
            Map<String, dynamic>? details)?
        parameterConfiguration,
    TResult Function(String message, String? errorCode, String? diagnosticType,
            Map<String, dynamic>? details)?
        diagnostics,
    TResult Function(String message, String? errorCode, String? standard,
            Map<String, dynamic>? details)?
        compliance,
    TResult Function(String message, String? errorCode, String? authMethod,
            Map<String, dynamic>? details)?
        authentication,
    TResult Function(String message, Duration timeout, String? operation,
            Map<String, dynamic>? details)?
        timeout,
    TResult Function(String message, String field, String? expectedFormat,
            String? actualValue, Map<String, dynamic>? details)?
        validation,
    TResult Function(String message, String permission, String? requiredLevel,
            Map<String, dynamic>? details)?
        permission,
    TResult Function(String message, String? errorCode, String? storageType,
            Map<String, dynamic>? details)?
        storage,
    TResult Function(String message, String? errorCode,
            Exception? originalException, Map<String, dynamic>? details)?
        unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(message, errorCode, originalException, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ChargerDiscoveryFailure value) chargerDiscovery,
    required TResult Function(ChargerConnectionFailure value) chargerConnection,
    required TResult Function(NetworkConfigurationFailure value)
        networkConfiguration,
    required TResult Function(OCPPConnectionFailure value) ocppConnection,
    required TResult Function(FirmwareUpdateFailure value) firmwareUpdate,
    required TResult Function(ParameterConfigurationFailure value)
        parameterConfiguration,
    required TResult Function(DiagnosticsFailure value) diagnostics,
    required TResult Function(ComplianceFailure value) compliance,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(TimeoutFailure value) timeout,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(StorageFailure value) storage,
    required TResult Function(UnknownCommissioningFailure value) unknown,
  }) {
    return unknown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult? Function(ChargerConnectionFailure value)? chargerConnection,
    TResult? Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult? Function(OCPPConnectionFailure value)? ocppConnection,
    TResult? Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult? Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult? Function(DiagnosticsFailure value)? diagnostics,
    TResult? Function(ComplianceFailure value)? compliance,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(TimeoutFailure value)? timeout,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(StorageFailure value)? storage,
    TResult? Function(UnknownCommissioningFailure value)? unknown,
  }) {
    return unknown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ChargerDiscoveryFailure value)? chargerDiscovery,
    TResult Function(ChargerConnectionFailure value)? chargerConnection,
    TResult Function(NetworkConfigurationFailure value)? networkConfiguration,
    TResult Function(OCPPConnectionFailure value)? ocppConnection,
    TResult Function(FirmwareUpdateFailure value)? firmwareUpdate,
    TResult Function(ParameterConfigurationFailure value)?
        parameterConfiguration,
    TResult Function(DiagnosticsFailure value)? diagnostics,
    TResult Function(ComplianceFailure value)? compliance,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(TimeoutFailure value)? timeout,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(StorageFailure value)? storage,
    TResult Function(UnknownCommissioningFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(this);
    }
    return orElse();
  }
}

abstract class UnknownCommissioningFailure implements CommissioningFailure {
  const factory UnknownCommissioningFailure(
      {required final String message,
      final String? errorCode,
      final Exception? originalException,
      final Map<String, dynamic>? details}) = _$UnknownCommissioningFailureImpl;

  @override
  String get message;
  String? get errorCode;
  Exception? get originalException;
  @override
  Map<String, dynamic>? get details;

  /// Create a copy of CommissioningFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnknownCommissioningFailureImplCopyWith<_$UnknownCommissioningFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}
