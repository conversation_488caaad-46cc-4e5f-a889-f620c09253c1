name: ev_commissioning_app
description: "Comprehensive Flutter mobile application for EV charger commissioning"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # UI & Material Design
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5

  # Dependency Injection
  get_it: ^8.0.0
  injectable: ^2.4.4

  # Networking & HTTP (Minimal for mock testing)
  connectivity_plus: ^6.0.5
  json_annotation: ^4.9.0
  dio: ^5.4.0
  network_info_plus: ^4.1.0
  wifi_scan: ^0.4.1
  permission_handler: ^11.1.0
  file_picker: ^6.1.1

  # Security & Authentication
  flutter_secure_storage: ^9.2.2
  local_auth: ^2.3.0
  crypto: ^3.0.5

  # Database & Storage
  sqflite: ^2.3.3+1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.4

  # Network Discovery & Scanning
  network_info_plus: ^6.1.4
  wifi_scan: ^0.4.1

  # WebSocket for OCPP
  web_socket_channel: ^3.0.1

  # HTTP client for REST APIs
  dio: ^5.7.0

  # File Handling & Documents
  pdf: ^3.11.1
  path: ^1.9.0
  file_picker: ^8.1.2
  image_picker: ^1.1.2

  # Utilities
  intl: ^0.19.0
  uuid: ^4.5.1
  logger: ^2.4.0
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

  # Charts & Visualization
  fl_chart: ^0.69.0

  # QR Code & Barcode (using more compatible packages)
  qr_flutter: ^4.1.0
  mobile_scanner: ^5.0.1

  # Bluetooth connectivity (using flutter_blue_plus for better compatibility)
  flutter_blue_plus: ^1.32.12

  # Freezed for immutable data classes
  freezed_annotation: ^2.4.4

  # Functional programming
  dartz: ^0.10.1

  # Biometric Authentication
  local_auth_android: ^1.0.41
  local_auth_darwin: ^1.4.1

  # Background Tasks (removed workmanager due to compatibility issues - will add back later)
  # workmanager: ^0.5.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting & Code Quality
  flutter_lints: ^5.0.0
  very_good_analysis: ^6.0.0

  # Code Generation (Minimal for mock testing)
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1
  freezed: ^2.5.7

  # Testing
  mockito: ^5.4.4
  bloc_test: ^9.1.7
  network_image_mock: ^2.1.1
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/config/
