import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import 'package:uuid/uuid.dart';

/// OCPP 1.6J service for EV charger commissioning
/// Implements Indian standards IS-17017 Parts 21-23 and DC-001/AC-001 compliance
class OCPPService {
  static const String _ocppSubProtocol = 'ocpp1.6';

  WebSocketChannel? _channel;
  bool _isConnected = false;
  
  final Map<String, Completer<Map<String, dynamic>>> _pendingRequests = {};
  final StreamController<Map<String, dynamic>> _messageController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  final Uuid _uuid = const Uuid();
  
  /// Stream of incoming OCPP messages
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;
  
  /// Check if connected to Central System
  bool get isConnected => _isConnected;
  
  /// Initialize OCPP connection
  Future<bool> connect({
    required String centralSystemUrl,
    required String chargePointId,
    Map<String, String>? headers,
  }) async {
    try {
      
      // Create WebSocket connection with OCPP subprotocol
      final uri = Uri.parse('$centralSystemUrl/$chargePointId');
      _channel = IOWebSocketChannel.connect(
        uri,
        protocols: [_ocppSubProtocol],
        headers: headers,
      );
      
      // Listen to incoming messages
      _channel!.stream.listen(
        _handleIncomingMessage,
        onError: _handleConnectionError,
        onDone: _handleConnectionClosed,
      );
      
      _isConnected = true;
      
      // Send initial BootNotification
      await _sendBootNotification();
      
      return true;
    } catch (e) {
      _isConnected = false;
      return false;
    }
  }
  
  /// Disconnect from Central System
  Future<void> disconnect() async {
    _isConnected = false;
    await _channel?.sink.close();
    _channel = null;
  }
  
  /// Send BootNotification message (required for commissioning)
  Future<Map<String, dynamic>> _sendBootNotification({
    String? chargePointModel,
    String? chargePointSerialNumber,
    String? chargePointVendor,
    String? firmwareVersion,
  }) async {
    final payload = {
      'chargePointModel': chargePointModel ?? 'EV Charger',
      'chargePointSerialNumber': chargePointSerialNumber ?? 'UNKNOWN',
      'chargePointVendor': chargePointVendor ?? 'Generic',
      'firmwareVersion': firmwareVersion ?? '1.0.0',
    };
    
    return await _sendRequest('BootNotification', payload);
  }
  
  /// Send ChangeConfiguration message for commissioning parameters
  Future<Map<String, dynamic>> changeConfiguration({
    required String key,
    required String value,
  }) async {
    final payload = {
      'key': key,
      'value': value,
    };
    
    return await _sendRequest('ChangeConfiguration', payload);
  }
  
  /// Configure network settings via OCPP
  Future<List<Map<String, dynamic>>> configureNetwork({
    String? wifiSSID,
    String? wifiPassword,
    String? gsmAPN,
    String? ethernetMode, // 'DHCP' or 'Static'
    String? staticIP,
    String? subnetMask,
    String? gateway,
    String? dns,
  }) async {
    final results = <Map<String, dynamic>>[];
    
    if (wifiSSID != null) {
      results.add(await changeConfiguration(
        key: 'RemoteService.OCSP.Wifi.SSID',
        value: wifiSSID,
      ));
    }
    
    if (wifiPassword != null) {
      results.add(await changeConfiguration(
        key: 'RemoteService.OCSP.Wifi.Password',
        value: wifiPassword,
      ));
    }
    
    if (gsmAPN != null) {
      results.add(await changeConfiguration(
        key: 'RemoteService.OCSP.GSM.APN',
        value: gsmAPN,
      ));
    }
    
    if (ethernetMode != null) {
      results.add(await changeConfiguration(
        key: 'RemoteService.OCSP.LAN.Mode',
        value: ethernetMode,
      ));
    }
    
    if (staticIP != null) {
      results.add(await changeConfiguration(
        key: 'RemoteService.OCSP.LAN.StaticIP',
        value: staticIP,
      ));
    }
    
    if (subnetMask != null) {
      results.add(await changeConfiguration(
        key: 'RemoteService.OCSP.LAN.SubnetMask',
        value: subnetMask,
      ));
    }
    
    if (gateway != null) {
      results.add(await changeConfiguration(
        key: 'RemoteService.OCSP.LAN.Gateway',
        value: gateway,
      ));
    }
    
    if (dns != null) {
      results.add(await changeConfiguration(
        key: 'RemoteService.OCSP.LAN.DNS',
        value: dns,
      ));
    }
    
    return results;
  }
  
  /// Configure charger settings via OCPP
  Future<List<Map<String, dynamic>>> configureCharger({
    double? maxPower,
    String? controlMode, // 'Standard' or 'Dynamic'
    String? chargerType, // 'SingleGun' or 'DualGun'
    bool? acType2Enabled,
    int? gun1MaxCurrent,
    int? gun2MaxCurrent,
    String? authenticationType, // 'UserAuthentication' or 'PlugAndCharge'
    int? sessionTimeout,
  }) async {
    final results = <Map<String, dynamic>>[];
    
    if (maxPower != null) {
      results.add(await changeConfiguration(
        key: 'ChargePoint.MaxPower',
        value: maxPower.toString(),
      ));
    }
    
    if (controlMode != null) {
      results.add(await changeConfiguration(
        key: 'ChargePoint.ControlMode',
        value: controlMode,
      ));
    }
    
    if (chargerType != null) {
      results.add(await changeConfiguration(
        key: 'ChargePoint.Type',
        value: chargerType,
      ));
    }
    
    if (acType2Enabled != null) {
      results.add(await changeConfiguration(
        key: 'Connector.AcType2.Enabled',
        value: acType2Enabled.toString(),
      ));
    }
    
    if (gun1MaxCurrent != null) {
      results.add(await changeConfiguration(
        key: 'Connector.1.MaxCurrent',
        value: gun1MaxCurrent.toString(),
      ));
    }
    
    if (gun2MaxCurrent != null) {
      results.add(await changeConfiguration(
        key: 'Connector.2.MaxCurrent',
        value: gun2MaxCurrent.toString(),
      ));
    }
    
    if (authenticationType != null) {
      results.add(await changeConfiguration(
        key: 'Security.AuthenticationType',
        value: authenticationType,
      ));
    }
    
    if (sessionTimeout != null) {
      results.add(await changeConfiguration(
        key: 'Transaction.IdleTimeout',
        value: sessionTimeout.toString(),
      ));
    }
    
    return results;
  }
  
  /// Configure charging point settings via OCPP
  Future<List<Map<String, dynamic>>> configureChargingPoint({
    String? model,
    String? serialNumber,
    String? vendor,
    String? websocketUrl,
    String? authToken,
    double? electricityPrice,
    int? rfidTagLength,
    String? timeZone,
  }) async {
    final results = <Map<String, dynamic>>[];
    
    // Send updated BootNotification with device info
    if (model != null || serialNumber != null || vendor != null) {
      results.add(await _sendBootNotification(
        chargePointModel: model,
        chargePointSerialNumber: serialNumber,
        chargePointVendor: vendor,
      ));
    }
    
    if (websocketUrl != null) {
      results.add(await changeConfiguration(
        key: 'CentralSystemURI',
        value: websocketUrl,
      ));
    }
    
    if (authToken != null) {
      results.add(await changeConfiguration(
        key: 'Security.AuthorizationToken',
        value: authToken,
      ));
    }
    
    if (rfidTagLength != null) {
      results.add(await changeConfiguration(
        key: 'Security.RFID.TagLength',
        value: rfidTagLength.toString(),
      ));
    }
    
    if (timeZone != null) {
      results.add(await changeConfiguration(
        key: 'LocalTimeZone',
        value: timeZone,
      ));
    }
    
    // Use DataTransfer for custom parameters like electricity price
    if (electricityPrice != null) {
      results.add(await _sendDataTransfer(
        vendorId: 'EV_COMMISSIONING_APP',
        messageId: 'ELECTRICITY_PRICE',
        data: {'price': electricityPrice, 'currency': 'INR'},
      ));
    }

    return results;
  }

  /// Send DataTransfer message for custom parameters
  Future<Map<String, dynamic>> _sendDataTransfer({
    required String vendorId,
    String? messageId,
    Map<String, dynamic>? data,
  }) async {
    final payload = {
      'vendorId': vendorId,
      if (messageId != null) 'messageId': messageId,
      if (data != null) 'data': jsonEncode(data),
    };

    return await _sendRequest('DataTransfer', payload);
  }

  /// Send UpdateFirmware message for OTA updates
  Future<Map<String, dynamic>> updateFirmware({
    required String location,
    required DateTime retrieveDate,
    int? retries,
    int? retryInterval,
  }) async {
    final payload = {
      'location': location,
      'retrieveDate': retrieveDate.toIso8601String(),
      if (retries != null) 'retries': retries,
      if (retryInterval != null) 'retryInterval': retryInterval,
    };

    return await _sendRequest('UpdateFirmware', payload);
  }

  /// Send CertificateSigned message for certificate management
  Future<Map<String, dynamic>> installCertificate({
    required String certificateChain,
    required String certificateType, // 'ChargingStationRootCertificate', 'ManufacturerRootCertificate'
  }) async {
    final payload = {
      'certificateChain': certificateChain,
      'certificateType': certificateType,
    };

    return await _sendRequest('CertificateSigned', payload);
  }

  /// Send DeleteCertificate message
  Future<Map<String, dynamic>> deleteCertificate({
    required String certificateHashData,
  }) async {
    final payload = {
      'certificateHashData': certificateHashData,
    };

    return await _sendRequest('DeleteCertificate', payload);
  }

  /// Send GetInstalledCertificateIds message
  Future<Map<String, dynamic>> getInstalledCertificates({
    List<String>? certificateTypes,
  }) async {
    final payload = {
      if (certificateTypes != null) 'certificateType': certificateTypes,
    };

    return await _sendRequest('GetInstalledCertificateIds', payload);
  }

  /// Send generic OCPP request
  Future<Map<String, dynamic>> _sendRequest(String action, Map<String, dynamic> payload) async {
    if (!_isConnected || _channel == null) {
      throw Exception('Not connected to Central System');
    }

    final messageId = _uuid.v4();
    final message = [2, messageId, action, payload];

    final completer = Completer<Map<String, dynamic>>();
    _pendingRequests[messageId] = completer;

    // Send message
    _channel!.sink.add(jsonEncode(message));

    // Wait for response with timeout
    return completer.future.timeout(
      const Duration(seconds: 30),
      onTimeout: () {
        _pendingRequests.remove(messageId);
        throw TimeoutException('OCPP request timeout', const Duration(seconds: 30));
      },
    );
  }

  /// Handle incoming WebSocket messages
  void _handleIncomingMessage(dynamic message) {
    try {
      final List<dynamic> parsedMessage = jsonDecode(message);
      final messageType = parsedMessage[0] as int;

      switch (messageType) {
        case 3: // CALLRESULT
          _handleCallResult(parsedMessage);
          break;
        case 4: // CALLERROR
          _handleCallError(parsedMessage);
          break;
        case 2: // CALL (incoming request from Central System)
          _handleIncomingCall(parsedMessage);
          break;
      }
    } catch (e) {
      // Log error in production app - replace with proper logging framework
      debugPrint('Error parsing OCPP message: $e');
    }
  }

  /// Handle CALLRESULT messages
  void _handleCallResult(List<dynamic> message) {
    final messageId = message[1] as String;
    final payload = message[2] as Map<String, dynamic>;

    final completer = _pendingRequests.remove(messageId);
    completer?.complete(payload);
  }

  /// Handle CALLERROR messages
  void _handleCallError(List<dynamic> message) {
    final messageId = message[1] as String;
    final errorCode = message[2] as String;
    final errorDescription = message[3] as String;

    final completer = _pendingRequests.remove(messageId);
    completer?.completeError(Exception('OCPP Error: $errorCode - $errorDescription'));
  }

  /// Handle incoming CALL messages from Central System
  void _handleIncomingCall(List<dynamic> message) {
    final messageId = message[1] as String;
    final action = message[2] as String;
    final payload = message[3] as Map<String, dynamic>;

    // Broadcast incoming message
    _messageController.add({
      'messageId': messageId,
      'action': action,
      'payload': payload,
    });

    // Send default response for now
    _sendCallResult(messageId, {});
  }

  /// Send CALLRESULT response
  void _sendCallResult(String messageId, Map<String, dynamic> payload) {
    if (_channel != null) {
      final message = [3, messageId, payload];
      _channel!.sink.add(jsonEncode(message));
    }
  }

  /// Handle connection errors
  void _handleConnectionError(error) {
    _isConnected = false;
    debugPrint('OCPP connection error: $error');
  }

  /// Handle connection closed
  void _handleConnectionClosed() {
    _isConnected = false;
    debugPrint('OCPP connection closed');
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _messageController.close();
    _pendingRequests.clear();
  }
}
