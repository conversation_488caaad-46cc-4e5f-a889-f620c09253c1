import 'package:freezed_annotation/freezed_annotation.dart';

part 'commissioning_errors.freezed.dart';

/// Commissioning-specific failure types
@freezed
class CommissioningFailure with _$CommissioningFailure {
  const factory CommissioningFailure.chargerDiscovery({
    required String message,
    String? errorCode,
    Map<String, dynamic>? details,
  }) = ChargerDiscoveryFailure;

  const factory CommissioningFailure.chargerConnection({
    required String message,
    String? errorCode,
    String? chargerId,
    Map<String, dynamic>? details,
  }) = ChargerConnectionFailure;

  const factory CommissioningFailure.networkConfiguration({
    required String message,
    String? errorCode,
    String? networkName,
    Map<String, dynamic>? details,
  }) = NetworkConfigurationFailure;

  const factory CommissioningFailure.ocppConnection({
    required String message,
    String? errorCode,
    String? cmsUrl,
    Map<String, dynamic>? details,
  }) = OCPPConnectionFailure;

  const factory CommissioningFailure.firmwareUpdate({
    required String message,
    String? errorCode,
    String? firmwareVersion,
    Map<String, dynamic>? details,
  }) = FirmwareUpdateFailure;

  const factory CommissioningFailure.parameterConfiguration({
    required String message,
    String? errorCode,
    String? parameterName,
    Map<String, dynamic>? details,
  }) = ParameterConfigurationFailure;

  const factory CommissioningFailure.diagnostics({
    required String message,
    String? errorCode,
    String? diagnosticType,
    Map<String, dynamic>? details,
  }) = DiagnosticsFailure;

  const factory CommissioningFailure.compliance({
    required String message,
    String? errorCode,
    String? standard,
    Map<String, dynamic>? details,
  }) = ComplianceFailure;

  const factory CommissioningFailure.authentication({
    required String message,
    String? errorCode,
    String? authMethod,
    Map<String, dynamic>? details,
  }) = AuthenticationFailure;

  const factory CommissioningFailure.timeout({
    required String message,
    required Duration timeout,
    String? operation,
    Map<String, dynamic>? details,
  }) = TimeoutFailure;

  const factory CommissioningFailure.validation({
    required String message,
    required String field,
    String? expectedFormat,
    String? actualValue,
    Map<String, dynamic>? details,
  }) = ValidationFailure;

  const factory CommissioningFailure.permission({
    required String message,
    required String permission,
    String? requiredLevel,
    Map<String, dynamic>? details,
  }) = PermissionFailure;

  const factory CommissioningFailure.storage({
    required String message,
    String? errorCode,
    String? storageType,
    Map<String, dynamic>? details,
  }) = StorageFailure;

  const factory CommissioningFailure.unknown({
    required String message,
    String? errorCode,
    Exception? originalException,
    Map<String, dynamic>? details,
  }) = UnknownCommissioningFailure;
}

/// Common error codes used throughout commissioning
class CommissioningErrorCodes {
  // Discovery errors
  static const String discoveryTimeout = 'DISCOVERY_TIMEOUT';
  static const String noChargersFound = 'NO_CHARGERS_FOUND';
  static const String bluetoothDisabled = 'BLUETOOTH_DISABLED';
  static const String wifiDisabled = 'WIFI_DISABLED';
  static const String locationPermissionDenied = 'LOCATION_PERMISSION_DENIED';

  // Connection errors
  static const String connectionTimeout = 'CONNECTION_TIMEOUT';
  static const String connectionRefused = 'CONNECTION_REFUSED';
  static const String authenticationFailed = 'AUTHENTICATION_FAILED';
  static const String invalidCredentials = 'INVALID_CREDENTIALS';
  static const String connectionLost = 'CONNECTION_LOST';

  // Network configuration errors
  static const String networkNotFound = 'NETWORK_NOT_FOUND';
  static const String weakSignal = 'WEAK_SIGNAL';
  static const String invalidPassword = 'INVALID_PASSWORD';
  static const String dhcpFailed = 'DHCP_FAILED';
  static const String internetNotReachable = 'INTERNET_NOT_REACHABLE';

  // OCPP errors
  static const String ocppHandshakeFailed = 'OCPP_HANDSHAKE_FAILED';
  static const String cmsUnreachable = 'CMS_UNREACHABLE';
  static const String invalidOcppMessage = 'INVALID_OCPP_MESSAGE';
  static const String ocppVersionMismatch = 'OCPP_VERSION_MISMATCH';
  static const String chargerRegistrationFailed = 'CHARGER_REGISTRATION_FAILED';

  // Firmware errors
  static const String firmwareDownloadFailed = 'FIRMWARE_DOWNLOAD_FAILED';
  static const String firmwareCorrupted = 'FIRMWARE_CORRUPTED';
  static const String firmwareInstallationFailed =
      'FIRMWARE_INSTALLATION_FAILED';
  static const String firmwareVerificationFailed =
      'FIRMWARE_VERIFICATION_FAILED';
  static const String incompatibleFirmware = 'INCOMPATIBLE_FIRMWARE';

  // Parameter configuration errors
  static const String invalidParameterValue = 'INVALID_PARAMETER_VALUE';
  static const String parameterOutOfRange = 'PARAMETER_OUT_OF_RANGE';
  static const String readOnlyParameter = 'READ_ONLY_PARAMETER';
  static const String unsupportedParameter = 'UNSUPPORTED_PARAMETER';
  static const String safetyLimitExceeded = 'SAFETY_LIMIT_EXCEEDED';

  // Compliance errors
  static const String complianceViolation = 'COMPLIANCE_VIOLATION';
  static const String certificationMissing = 'CERTIFICATION_MISSING';
  static const String safetyTestFailed = 'SAFETY_TEST_FAILED';
  static const String regulatoryNonCompliance = 'REGULATORY_NON_COMPLIANCE';

  // General errors
  static const String operationCancelled = 'OPERATION_CANCELLED';
  static const String insufficientPermissions = 'INSUFFICIENT_PERMISSIONS';
  static const String deviceNotSupported = 'DEVICE_NOT_SUPPORTED';
  static const String serviceUnavailable = 'SERVICE_UNAVAILABLE';
  static const String rateLimitExceeded = 'RATE_LIMIT_EXCEEDED';
}
