// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ocpp_credentials.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OCPPCredentials _$OCPPCredentialsFromJson(Map<String, dynamic> json) {
  return _OCPPCredentials.fromJson(json);
}

/// @nodoc
mixin _$OCPPCredentials {
  String get chargePointId => throw _privateConstructorUsedError;
  String get cmsUrl => throw _privateConstructorUsedError;
  OCPPVersion get version => throw _privateConstructorUsedError;
  OCPPAuthType get authType => throw _privateConstructorUsedError;
  String? get username => throw _privateConstructorUsedError;
  String? get password => throw _privateConstructorUsedError;
  String? get certificatePath => throw _privateConstructorUsedError;
  String? get privateKeyPath => throw _privateConstructorUsedError;
  String? get caCertificatePath => throw _privateConstructorUsedError;
  String? get authToken => throw _privateConstructorUsedError;
  Map<String, String>? get headers => throw _privateConstructorUsedError;
  int? get heartbeatInterval => throw _privateConstructorUsedError;
  int? get connectionTimeout => throw _privateConstructorUsedError;
  bool? get useSSL => throw _privateConstructorUsedError;

  /// Serializes this OCPPCredentials to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OCPPCredentials
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OCPPCredentialsCopyWith<OCPPCredentials> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OCPPCredentialsCopyWith<$Res> {
  factory $OCPPCredentialsCopyWith(
          OCPPCredentials value, $Res Function(OCPPCredentials) then) =
      _$OCPPCredentialsCopyWithImpl<$Res, OCPPCredentials>;
  @useResult
  $Res call(
      {String chargePointId,
      String cmsUrl,
      OCPPVersion version,
      OCPPAuthType authType,
      String? username,
      String? password,
      String? certificatePath,
      String? privateKeyPath,
      String? caCertificatePath,
      String? authToken,
      Map<String, String>? headers,
      int? heartbeatInterval,
      int? connectionTimeout,
      bool? useSSL});
}

/// @nodoc
class _$OCPPCredentialsCopyWithImpl<$Res, $Val extends OCPPCredentials>
    implements $OCPPCredentialsCopyWith<$Res> {
  _$OCPPCredentialsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OCPPCredentials
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chargePointId = null,
    Object? cmsUrl = null,
    Object? version = null,
    Object? authType = null,
    Object? username = freezed,
    Object? password = freezed,
    Object? certificatePath = freezed,
    Object? privateKeyPath = freezed,
    Object? caCertificatePath = freezed,
    Object? authToken = freezed,
    Object? headers = freezed,
    Object? heartbeatInterval = freezed,
    Object? connectionTimeout = freezed,
    Object? useSSL = freezed,
  }) {
    return _then(_value.copyWith(
      chargePointId: null == chargePointId
          ? _value.chargePointId
          : chargePointId // ignore: cast_nullable_to_non_nullable
              as String,
      cmsUrl: null == cmsUrl
          ? _value.cmsUrl
          : cmsUrl // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as OCPPVersion,
      authType: null == authType
          ? _value.authType
          : authType // ignore: cast_nullable_to_non_nullable
              as OCPPAuthType,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      certificatePath: freezed == certificatePath
          ? _value.certificatePath
          : certificatePath // ignore: cast_nullable_to_non_nullable
              as String?,
      privateKeyPath: freezed == privateKeyPath
          ? _value.privateKeyPath
          : privateKeyPath // ignore: cast_nullable_to_non_nullable
              as String?,
      caCertificatePath: freezed == caCertificatePath
          ? _value.caCertificatePath
          : caCertificatePath // ignore: cast_nullable_to_non_nullable
              as String?,
      authToken: freezed == authToken
          ? _value.authToken
          : authToken // ignore: cast_nullable_to_non_nullable
              as String?,
      headers: freezed == headers
          ? _value.headers
          : headers // ignore: cast_nullable_to_non_nullable
              as Map<String, String>?,
      heartbeatInterval: freezed == heartbeatInterval
          ? _value.heartbeatInterval
          : heartbeatInterval // ignore: cast_nullable_to_non_nullable
              as int?,
      connectionTimeout: freezed == connectionTimeout
          ? _value.connectionTimeout
          : connectionTimeout // ignore: cast_nullable_to_non_nullable
              as int?,
      useSSL: freezed == useSSL
          ? _value.useSSL
          : useSSL // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OCPPCredentialsImplCopyWith<$Res>
    implements $OCPPCredentialsCopyWith<$Res> {
  factory _$$OCPPCredentialsImplCopyWith(_$OCPPCredentialsImpl value,
          $Res Function(_$OCPPCredentialsImpl) then) =
      __$$OCPPCredentialsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String chargePointId,
      String cmsUrl,
      OCPPVersion version,
      OCPPAuthType authType,
      String? username,
      String? password,
      String? certificatePath,
      String? privateKeyPath,
      String? caCertificatePath,
      String? authToken,
      Map<String, String>? headers,
      int? heartbeatInterval,
      int? connectionTimeout,
      bool? useSSL});
}

/// @nodoc
class __$$OCPPCredentialsImplCopyWithImpl<$Res>
    extends _$OCPPCredentialsCopyWithImpl<$Res, _$OCPPCredentialsImpl>
    implements _$$OCPPCredentialsImplCopyWith<$Res> {
  __$$OCPPCredentialsImplCopyWithImpl(
      _$OCPPCredentialsImpl _value, $Res Function(_$OCPPCredentialsImpl) _then)
      : super(_value, _then);

  /// Create a copy of OCPPCredentials
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chargePointId = null,
    Object? cmsUrl = null,
    Object? version = null,
    Object? authType = null,
    Object? username = freezed,
    Object? password = freezed,
    Object? certificatePath = freezed,
    Object? privateKeyPath = freezed,
    Object? caCertificatePath = freezed,
    Object? authToken = freezed,
    Object? headers = freezed,
    Object? heartbeatInterval = freezed,
    Object? connectionTimeout = freezed,
    Object? useSSL = freezed,
  }) {
    return _then(_$OCPPCredentialsImpl(
      chargePointId: null == chargePointId
          ? _value.chargePointId
          : chargePointId // ignore: cast_nullable_to_non_nullable
              as String,
      cmsUrl: null == cmsUrl
          ? _value.cmsUrl
          : cmsUrl // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as OCPPVersion,
      authType: null == authType
          ? _value.authType
          : authType // ignore: cast_nullable_to_non_nullable
              as OCPPAuthType,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      certificatePath: freezed == certificatePath
          ? _value.certificatePath
          : certificatePath // ignore: cast_nullable_to_non_nullable
              as String?,
      privateKeyPath: freezed == privateKeyPath
          ? _value.privateKeyPath
          : privateKeyPath // ignore: cast_nullable_to_non_nullable
              as String?,
      caCertificatePath: freezed == caCertificatePath
          ? _value.caCertificatePath
          : caCertificatePath // ignore: cast_nullable_to_non_nullable
              as String?,
      authToken: freezed == authToken
          ? _value.authToken
          : authToken // ignore: cast_nullable_to_non_nullable
              as String?,
      headers: freezed == headers
          ? _value._headers
          : headers // ignore: cast_nullable_to_non_nullable
              as Map<String, String>?,
      heartbeatInterval: freezed == heartbeatInterval
          ? _value.heartbeatInterval
          : heartbeatInterval // ignore: cast_nullable_to_non_nullable
              as int?,
      connectionTimeout: freezed == connectionTimeout
          ? _value.connectionTimeout
          : connectionTimeout // ignore: cast_nullable_to_non_nullable
              as int?,
      useSSL: freezed == useSSL
          ? _value.useSSL
          : useSSL // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OCPPCredentialsImpl implements _OCPPCredentials {
  const _$OCPPCredentialsImpl(
      {required this.chargePointId,
      required this.cmsUrl,
      required this.version,
      required this.authType,
      this.username,
      this.password,
      this.certificatePath,
      this.privateKeyPath,
      this.caCertificatePath,
      this.authToken,
      final Map<String, String>? headers,
      this.heartbeatInterval,
      this.connectionTimeout,
      this.useSSL})
      : _headers = headers;

  factory _$OCPPCredentialsImpl.fromJson(Map<String, dynamic> json) =>
      _$$OCPPCredentialsImplFromJson(json);

  @override
  final String chargePointId;
  @override
  final String cmsUrl;
  @override
  final OCPPVersion version;
  @override
  final OCPPAuthType authType;
  @override
  final String? username;
  @override
  final String? password;
  @override
  final String? certificatePath;
  @override
  final String? privateKeyPath;
  @override
  final String? caCertificatePath;
  @override
  final String? authToken;
  final Map<String, String>? _headers;
  @override
  Map<String, String>? get headers {
    final value = _headers;
    if (value == null) return null;
    if (_headers is EqualUnmodifiableMapView) return _headers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final int? heartbeatInterval;
  @override
  final int? connectionTimeout;
  @override
  final bool? useSSL;

  @override
  String toString() {
    return 'OCPPCredentials(chargePointId: $chargePointId, cmsUrl: $cmsUrl, version: $version, authType: $authType, username: $username, password: $password, certificatePath: $certificatePath, privateKeyPath: $privateKeyPath, caCertificatePath: $caCertificatePath, authToken: $authToken, headers: $headers, heartbeatInterval: $heartbeatInterval, connectionTimeout: $connectionTimeout, useSSL: $useSSL)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OCPPCredentialsImpl &&
            (identical(other.chargePointId, chargePointId) ||
                other.chargePointId == chargePointId) &&
            (identical(other.cmsUrl, cmsUrl) || other.cmsUrl == cmsUrl) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.authType, authType) ||
                other.authType == authType) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.certificatePath, certificatePath) ||
                other.certificatePath == certificatePath) &&
            (identical(other.privateKeyPath, privateKeyPath) ||
                other.privateKeyPath == privateKeyPath) &&
            (identical(other.caCertificatePath, caCertificatePath) ||
                other.caCertificatePath == caCertificatePath) &&
            (identical(other.authToken, authToken) ||
                other.authToken == authToken) &&
            const DeepCollectionEquality().equals(other._headers, _headers) &&
            (identical(other.heartbeatInterval, heartbeatInterval) ||
                other.heartbeatInterval == heartbeatInterval) &&
            (identical(other.connectionTimeout, connectionTimeout) ||
                other.connectionTimeout == connectionTimeout) &&
            (identical(other.useSSL, useSSL) || other.useSSL == useSSL));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      chargePointId,
      cmsUrl,
      version,
      authType,
      username,
      password,
      certificatePath,
      privateKeyPath,
      caCertificatePath,
      authToken,
      const DeepCollectionEquality().hash(_headers),
      heartbeatInterval,
      connectionTimeout,
      useSSL);

  /// Create a copy of OCPPCredentials
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OCPPCredentialsImplCopyWith<_$OCPPCredentialsImpl> get copyWith =>
      __$$OCPPCredentialsImplCopyWithImpl<_$OCPPCredentialsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OCPPCredentialsImplToJson(
      this,
    );
  }
}

abstract class _OCPPCredentials implements OCPPCredentials {
  const factory _OCPPCredentials(
      {required final String chargePointId,
      required final String cmsUrl,
      required final OCPPVersion version,
      required final OCPPAuthType authType,
      final String? username,
      final String? password,
      final String? certificatePath,
      final String? privateKeyPath,
      final String? caCertificatePath,
      final String? authToken,
      final Map<String, String>? headers,
      final int? heartbeatInterval,
      final int? connectionTimeout,
      final bool? useSSL}) = _$OCPPCredentialsImpl;

  factory _OCPPCredentials.fromJson(Map<String, dynamic> json) =
      _$OCPPCredentialsImpl.fromJson;

  @override
  String get chargePointId;
  @override
  String get cmsUrl;
  @override
  OCPPVersion get version;
  @override
  OCPPAuthType get authType;
  @override
  String? get username;
  @override
  String? get password;
  @override
  String? get certificatePath;
  @override
  String? get privateKeyPath;
  @override
  String? get caCertificatePath;
  @override
  String? get authToken;
  @override
  Map<String, String>? get headers;
  @override
  int? get heartbeatInterval;
  @override
  int? get connectionTimeout;
  @override
  bool? get useSSL;

  /// Create a copy of OCPPCredentials
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OCPPCredentialsImplCopyWith<_$OCPPCredentialsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OCPPMessage _$OCPPMessageFromJson(Map<String, dynamic> json) {
  return _OCPPMessage.fromJson(json);
}

/// @nodoc
mixin _$OCPPMessage {
  OCPPMessageType get messageType => throw _privateConstructorUsedError;
  String get messageId => throw _privateConstructorUsedError;
  String get action => throw _privateConstructorUsedError;
  Map<String, dynamic> get payload => throw _privateConstructorUsedError;
  DateTime? get timestamp => throw _privateConstructorUsedError;
  String? get errorCode => throw _privateConstructorUsedError;
  String? get errorDescription => throw _privateConstructorUsedError;

  /// Serializes this OCPPMessage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OCPPMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OCPPMessageCopyWith<OCPPMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OCPPMessageCopyWith<$Res> {
  factory $OCPPMessageCopyWith(
          OCPPMessage value, $Res Function(OCPPMessage) then) =
      _$OCPPMessageCopyWithImpl<$Res, OCPPMessage>;
  @useResult
  $Res call(
      {OCPPMessageType messageType,
      String messageId,
      String action,
      Map<String, dynamic> payload,
      DateTime? timestamp,
      String? errorCode,
      String? errorDescription});
}

/// @nodoc
class _$OCPPMessageCopyWithImpl<$Res, $Val extends OCPPMessage>
    implements $OCPPMessageCopyWith<$Res> {
  _$OCPPMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OCPPMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageType = null,
    Object? messageId = null,
    Object? action = null,
    Object? payload = null,
    Object? timestamp = freezed,
    Object? errorCode = freezed,
    Object? errorDescription = freezed,
  }) {
    return _then(_value.copyWith(
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as OCPPMessageType,
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      errorDescription: freezed == errorDescription
          ? _value.errorDescription
          : errorDescription // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OCPPMessageImplCopyWith<$Res>
    implements $OCPPMessageCopyWith<$Res> {
  factory _$$OCPPMessageImplCopyWith(
          _$OCPPMessageImpl value, $Res Function(_$OCPPMessageImpl) then) =
      __$$OCPPMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {OCPPMessageType messageType,
      String messageId,
      String action,
      Map<String, dynamic> payload,
      DateTime? timestamp,
      String? errorCode,
      String? errorDescription});
}

/// @nodoc
class __$$OCPPMessageImplCopyWithImpl<$Res>
    extends _$OCPPMessageCopyWithImpl<$Res, _$OCPPMessageImpl>
    implements _$$OCPPMessageImplCopyWith<$Res> {
  __$$OCPPMessageImplCopyWithImpl(
      _$OCPPMessageImpl _value, $Res Function(_$OCPPMessageImpl) _then)
      : super(_value, _then);

  /// Create a copy of OCPPMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageType = null,
    Object? messageId = null,
    Object? action = null,
    Object? payload = null,
    Object? timestamp = freezed,
    Object? errorCode = freezed,
    Object? errorDescription = freezed,
  }) {
    return _then(_$OCPPMessageImpl(
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as OCPPMessageType,
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _value._payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      errorDescription: freezed == errorDescription
          ? _value.errorDescription
          : errorDescription // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OCPPMessageImpl implements _OCPPMessage {
  const _$OCPPMessageImpl(
      {required this.messageType,
      required this.messageId,
      required this.action,
      required final Map<String, dynamic> payload,
      this.timestamp,
      this.errorCode,
      this.errorDescription})
      : _payload = payload;

  factory _$OCPPMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$OCPPMessageImplFromJson(json);

  @override
  final OCPPMessageType messageType;
  @override
  final String messageId;
  @override
  final String action;
  final Map<String, dynamic> _payload;
  @override
  Map<String, dynamic> get payload {
    if (_payload is EqualUnmodifiableMapView) return _payload;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_payload);
  }

  @override
  final DateTime? timestamp;
  @override
  final String? errorCode;
  @override
  final String? errorDescription;

  @override
  String toString() {
    return 'OCPPMessage(messageType: $messageType, messageId: $messageId, action: $action, payload: $payload, timestamp: $timestamp, errorCode: $errorCode, errorDescription: $errorDescription)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OCPPMessageImpl &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.action, action) || other.action == action) &&
            const DeepCollectionEquality().equals(other._payload, _payload) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.errorDescription, errorDescription) ||
                other.errorDescription == errorDescription));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      messageType,
      messageId,
      action,
      const DeepCollectionEquality().hash(_payload),
      timestamp,
      errorCode,
      errorDescription);

  /// Create a copy of OCPPMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OCPPMessageImplCopyWith<_$OCPPMessageImpl> get copyWith =>
      __$$OCPPMessageImplCopyWithImpl<_$OCPPMessageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OCPPMessageImplToJson(
      this,
    );
  }
}

abstract class _OCPPMessage implements OCPPMessage {
  const factory _OCPPMessage(
      {required final OCPPMessageType messageType,
      required final String messageId,
      required final String action,
      required final Map<String, dynamic> payload,
      final DateTime? timestamp,
      final String? errorCode,
      final String? errorDescription}) = _$OCPPMessageImpl;

  factory _OCPPMessage.fromJson(Map<String, dynamic> json) =
      _$OCPPMessageImpl.fromJson;

  @override
  OCPPMessageType get messageType;
  @override
  String get messageId;
  @override
  String get action;
  @override
  Map<String, dynamic> get payload;
  @override
  DateTime? get timestamp;
  @override
  String? get errorCode;
  @override
  String? get errorDescription;

  /// Create a copy of OCPPMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OCPPMessageImplCopyWith<_$OCPPMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OCPPConnectionStatus _$OCPPConnectionStatusFromJson(Map<String, dynamic> json) {
  return _OCPPConnectionStatus.fromJson(json);
}

/// @nodoc
mixin _$OCPPConnectionStatus {
  bool get isConnected => throw _privateConstructorUsedError;
  bool get isRegistered => throw _privateConstructorUsedError;
  DateTime? get connectedAt => throw _privateConstructorUsedError;
  DateTime? get lastHeartbeat => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  int? get reconnectAttempts => throw _privateConstructorUsedError;
  Map<String, dynamic>? get connectionInfo =>
      throw _privateConstructorUsedError;

  /// Serializes this OCPPConnectionStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OCPPConnectionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OCPPConnectionStatusCopyWith<OCPPConnectionStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OCPPConnectionStatusCopyWith<$Res> {
  factory $OCPPConnectionStatusCopyWith(OCPPConnectionStatus value,
          $Res Function(OCPPConnectionStatus) then) =
      _$OCPPConnectionStatusCopyWithImpl<$Res, OCPPConnectionStatus>;
  @useResult
  $Res call(
      {bool isConnected,
      bool isRegistered,
      DateTime? connectedAt,
      DateTime? lastHeartbeat,
      String? sessionId,
      String? errorMessage,
      int? reconnectAttempts,
      Map<String, dynamic>? connectionInfo});
}

/// @nodoc
class _$OCPPConnectionStatusCopyWithImpl<$Res,
        $Val extends OCPPConnectionStatus>
    implements $OCPPConnectionStatusCopyWith<$Res> {
  _$OCPPConnectionStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OCPPConnectionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isConnected = null,
    Object? isRegistered = null,
    Object? connectedAt = freezed,
    Object? lastHeartbeat = freezed,
    Object? sessionId = freezed,
    Object? errorMessage = freezed,
    Object? reconnectAttempts = freezed,
    Object? connectionInfo = freezed,
  }) {
    return _then(_value.copyWith(
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      isRegistered: null == isRegistered
          ? _value.isRegistered
          : isRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      connectedAt: freezed == connectedAt
          ? _value.connectedAt
          : connectedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastHeartbeat: freezed == lastHeartbeat
          ? _value.lastHeartbeat
          : lastHeartbeat // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      reconnectAttempts: freezed == reconnectAttempts
          ? _value.reconnectAttempts
          : reconnectAttempts // ignore: cast_nullable_to_non_nullable
              as int?,
      connectionInfo: freezed == connectionInfo
          ? _value.connectionInfo
          : connectionInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OCPPConnectionStatusImplCopyWith<$Res>
    implements $OCPPConnectionStatusCopyWith<$Res> {
  factory _$$OCPPConnectionStatusImplCopyWith(_$OCPPConnectionStatusImpl value,
          $Res Function(_$OCPPConnectionStatusImpl) then) =
      __$$OCPPConnectionStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isConnected,
      bool isRegistered,
      DateTime? connectedAt,
      DateTime? lastHeartbeat,
      String? sessionId,
      String? errorMessage,
      int? reconnectAttempts,
      Map<String, dynamic>? connectionInfo});
}

/// @nodoc
class __$$OCPPConnectionStatusImplCopyWithImpl<$Res>
    extends _$OCPPConnectionStatusCopyWithImpl<$Res, _$OCPPConnectionStatusImpl>
    implements _$$OCPPConnectionStatusImplCopyWith<$Res> {
  __$$OCPPConnectionStatusImplCopyWithImpl(_$OCPPConnectionStatusImpl _value,
      $Res Function(_$OCPPConnectionStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of OCPPConnectionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isConnected = null,
    Object? isRegistered = null,
    Object? connectedAt = freezed,
    Object? lastHeartbeat = freezed,
    Object? sessionId = freezed,
    Object? errorMessage = freezed,
    Object? reconnectAttempts = freezed,
    Object? connectionInfo = freezed,
  }) {
    return _then(_$OCPPConnectionStatusImpl(
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      isRegistered: null == isRegistered
          ? _value.isRegistered
          : isRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      connectedAt: freezed == connectedAt
          ? _value.connectedAt
          : connectedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastHeartbeat: freezed == lastHeartbeat
          ? _value.lastHeartbeat
          : lastHeartbeat // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      reconnectAttempts: freezed == reconnectAttempts
          ? _value.reconnectAttempts
          : reconnectAttempts // ignore: cast_nullable_to_non_nullable
              as int?,
      connectionInfo: freezed == connectionInfo
          ? _value._connectionInfo
          : connectionInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OCPPConnectionStatusImpl implements _OCPPConnectionStatus {
  const _$OCPPConnectionStatusImpl(
      {required this.isConnected,
      required this.isRegistered,
      this.connectedAt,
      this.lastHeartbeat,
      this.sessionId,
      this.errorMessage,
      this.reconnectAttempts,
      final Map<String, dynamic>? connectionInfo})
      : _connectionInfo = connectionInfo;

  factory _$OCPPConnectionStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$OCPPConnectionStatusImplFromJson(json);

  @override
  final bool isConnected;
  @override
  final bool isRegistered;
  @override
  final DateTime? connectedAt;
  @override
  final DateTime? lastHeartbeat;
  @override
  final String? sessionId;
  @override
  final String? errorMessage;
  @override
  final int? reconnectAttempts;
  final Map<String, dynamic>? _connectionInfo;
  @override
  Map<String, dynamic>? get connectionInfo {
    final value = _connectionInfo;
    if (value == null) return null;
    if (_connectionInfo is EqualUnmodifiableMapView) return _connectionInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'OCPPConnectionStatus(isConnected: $isConnected, isRegistered: $isRegistered, connectedAt: $connectedAt, lastHeartbeat: $lastHeartbeat, sessionId: $sessionId, errorMessage: $errorMessage, reconnectAttempts: $reconnectAttempts, connectionInfo: $connectionInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OCPPConnectionStatusImpl &&
            (identical(other.isConnected, isConnected) ||
                other.isConnected == isConnected) &&
            (identical(other.isRegistered, isRegistered) ||
                other.isRegistered == isRegistered) &&
            (identical(other.connectedAt, connectedAt) ||
                other.connectedAt == connectedAt) &&
            (identical(other.lastHeartbeat, lastHeartbeat) ||
                other.lastHeartbeat == lastHeartbeat) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.reconnectAttempts, reconnectAttempts) ||
                other.reconnectAttempts == reconnectAttempts) &&
            const DeepCollectionEquality()
                .equals(other._connectionInfo, _connectionInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isConnected,
      isRegistered,
      connectedAt,
      lastHeartbeat,
      sessionId,
      errorMessage,
      reconnectAttempts,
      const DeepCollectionEquality().hash(_connectionInfo));

  /// Create a copy of OCPPConnectionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OCPPConnectionStatusImplCopyWith<_$OCPPConnectionStatusImpl>
      get copyWith =>
          __$$OCPPConnectionStatusImplCopyWithImpl<_$OCPPConnectionStatusImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OCPPConnectionStatusImplToJson(
      this,
    );
  }
}

abstract class _OCPPConnectionStatus implements OCPPConnectionStatus {
  const factory _OCPPConnectionStatus(
      {required final bool isConnected,
      required final bool isRegistered,
      final DateTime? connectedAt,
      final DateTime? lastHeartbeat,
      final String? sessionId,
      final String? errorMessage,
      final int? reconnectAttempts,
      final Map<String, dynamic>? connectionInfo}) = _$OCPPConnectionStatusImpl;

  factory _OCPPConnectionStatus.fromJson(Map<String, dynamic> json) =
      _$OCPPConnectionStatusImpl.fromJson;

  @override
  bool get isConnected;
  @override
  bool get isRegistered;
  @override
  DateTime? get connectedAt;
  @override
  DateTime? get lastHeartbeat;
  @override
  String? get sessionId;
  @override
  String? get errorMessage;
  @override
  int? get reconnectAttempts;
  @override
  Map<String, dynamic>? get connectionInfo;

  /// Create a copy of OCPPConnectionStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OCPPConnectionStatusImplCopyWith<_$OCPPConnectionStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ChargingTransaction _$ChargingTransactionFromJson(Map<String, dynamic> json) {
  return _ChargingTransaction.fromJson(json);
}

/// @nodoc
mixin _$ChargingTransaction {
  String get transactionId => throw _privateConstructorUsedError;
  String get connectorId => throw _privateConstructorUsedError;
  String get idTag => throw _privateConstructorUsedError;
  DateTime get startTime => throw _privateConstructorUsedError;
  double get startMeterValue => throw _privateConstructorUsedError;
  DateTime? get stopTime => throw _privateConstructorUsedError;
  double? get stopMeterValue => throw _privateConstructorUsedError;
  String? get stopReason => throw _privateConstructorUsedError;
  double? get energyDelivered => throw _privateConstructorUsedError;
  double? get cost => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this ChargingTransaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChargingTransaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChargingTransactionCopyWith<ChargingTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChargingTransactionCopyWith<$Res> {
  factory $ChargingTransactionCopyWith(
          ChargingTransaction value, $Res Function(ChargingTransaction) then) =
      _$ChargingTransactionCopyWithImpl<$Res, ChargingTransaction>;
  @useResult
  $Res call(
      {String transactionId,
      String connectorId,
      String idTag,
      DateTime startTime,
      double startMeterValue,
      DateTime? stopTime,
      double? stopMeterValue,
      String? stopReason,
      double? energyDelivered,
      double? cost,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$ChargingTransactionCopyWithImpl<$Res, $Val extends ChargingTransaction>
    implements $ChargingTransactionCopyWith<$Res> {
  _$ChargingTransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChargingTransaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactionId = null,
    Object? connectorId = null,
    Object? idTag = null,
    Object? startTime = null,
    Object? startMeterValue = null,
    Object? stopTime = freezed,
    Object? stopMeterValue = freezed,
    Object? stopReason = freezed,
    Object? energyDelivered = freezed,
    Object? cost = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      transactionId: null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      connectorId: null == connectorId
          ? _value.connectorId
          : connectorId // ignore: cast_nullable_to_non_nullable
              as String,
      idTag: null == idTag
          ? _value.idTag
          : idTag // ignore: cast_nullable_to_non_nullable
              as String,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      startMeterValue: null == startMeterValue
          ? _value.startMeterValue
          : startMeterValue // ignore: cast_nullable_to_non_nullable
              as double,
      stopTime: freezed == stopTime
          ? _value.stopTime
          : stopTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      stopMeterValue: freezed == stopMeterValue
          ? _value.stopMeterValue
          : stopMeterValue // ignore: cast_nullable_to_non_nullable
              as double?,
      stopReason: freezed == stopReason
          ? _value.stopReason
          : stopReason // ignore: cast_nullable_to_non_nullable
              as String?,
      energyDelivered: freezed == energyDelivered
          ? _value.energyDelivered
          : energyDelivered // ignore: cast_nullable_to_non_nullable
              as double?,
      cost: freezed == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as double?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChargingTransactionImplCopyWith<$Res>
    implements $ChargingTransactionCopyWith<$Res> {
  factory _$$ChargingTransactionImplCopyWith(_$ChargingTransactionImpl value,
          $Res Function(_$ChargingTransactionImpl) then) =
      __$$ChargingTransactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String transactionId,
      String connectorId,
      String idTag,
      DateTime startTime,
      double startMeterValue,
      DateTime? stopTime,
      double? stopMeterValue,
      String? stopReason,
      double? energyDelivered,
      double? cost,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$ChargingTransactionImplCopyWithImpl<$Res>
    extends _$ChargingTransactionCopyWithImpl<$Res, _$ChargingTransactionImpl>
    implements _$$ChargingTransactionImplCopyWith<$Res> {
  __$$ChargingTransactionImplCopyWithImpl(_$ChargingTransactionImpl _value,
      $Res Function(_$ChargingTransactionImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChargingTransaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactionId = null,
    Object? connectorId = null,
    Object? idTag = null,
    Object? startTime = null,
    Object? startMeterValue = null,
    Object? stopTime = freezed,
    Object? stopMeterValue = freezed,
    Object? stopReason = freezed,
    Object? energyDelivered = freezed,
    Object? cost = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_$ChargingTransactionImpl(
      transactionId: null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      connectorId: null == connectorId
          ? _value.connectorId
          : connectorId // ignore: cast_nullable_to_non_nullable
              as String,
      idTag: null == idTag
          ? _value.idTag
          : idTag // ignore: cast_nullable_to_non_nullable
              as String,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      startMeterValue: null == startMeterValue
          ? _value.startMeterValue
          : startMeterValue // ignore: cast_nullable_to_non_nullable
              as double,
      stopTime: freezed == stopTime
          ? _value.stopTime
          : stopTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      stopMeterValue: freezed == stopMeterValue
          ? _value.stopMeterValue
          : stopMeterValue // ignore: cast_nullable_to_non_nullable
              as double?,
      stopReason: freezed == stopReason
          ? _value.stopReason
          : stopReason // ignore: cast_nullable_to_non_nullable
              as String?,
      energyDelivered: freezed == energyDelivered
          ? _value.energyDelivered
          : energyDelivered // ignore: cast_nullable_to_non_nullable
              as double?,
      cost: freezed == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as double?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChargingTransactionImpl implements _ChargingTransaction {
  const _$ChargingTransactionImpl(
      {required this.transactionId,
      required this.connectorId,
      required this.idTag,
      required this.startTime,
      required this.startMeterValue,
      this.stopTime,
      this.stopMeterValue,
      this.stopReason,
      this.energyDelivered,
      this.cost,
      final Map<String, dynamic>? metadata})
      : _metadata = metadata;

  factory _$ChargingTransactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChargingTransactionImplFromJson(json);

  @override
  final String transactionId;
  @override
  final String connectorId;
  @override
  final String idTag;
  @override
  final DateTime startTime;
  @override
  final double startMeterValue;
  @override
  final DateTime? stopTime;
  @override
  final double? stopMeterValue;
  @override
  final String? stopReason;
  @override
  final double? energyDelivered;
  @override
  final double? cost;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ChargingTransaction(transactionId: $transactionId, connectorId: $connectorId, idTag: $idTag, startTime: $startTime, startMeterValue: $startMeterValue, stopTime: $stopTime, stopMeterValue: $stopMeterValue, stopReason: $stopReason, energyDelivered: $energyDelivered, cost: $cost, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChargingTransactionImpl &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.connectorId, connectorId) ||
                other.connectorId == connectorId) &&
            (identical(other.idTag, idTag) || other.idTag == idTag) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.startMeterValue, startMeterValue) ||
                other.startMeterValue == startMeterValue) &&
            (identical(other.stopTime, stopTime) ||
                other.stopTime == stopTime) &&
            (identical(other.stopMeterValue, stopMeterValue) ||
                other.stopMeterValue == stopMeterValue) &&
            (identical(other.stopReason, stopReason) ||
                other.stopReason == stopReason) &&
            (identical(other.energyDelivered, energyDelivered) ||
                other.energyDelivered == energyDelivered) &&
            (identical(other.cost, cost) || other.cost == cost) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      transactionId,
      connectorId,
      idTag,
      startTime,
      startMeterValue,
      stopTime,
      stopMeterValue,
      stopReason,
      energyDelivered,
      cost,
      const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of ChargingTransaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChargingTransactionImplCopyWith<_$ChargingTransactionImpl> get copyWith =>
      __$$ChargingTransactionImplCopyWithImpl<_$ChargingTransactionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChargingTransactionImplToJson(
      this,
    );
  }
}

abstract class _ChargingTransaction implements ChargingTransaction {
  const factory _ChargingTransaction(
      {required final String transactionId,
      required final String connectorId,
      required final String idTag,
      required final DateTime startTime,
      required final double startMeterValue,
      final DateTime? stopTime,
      final double? stopMeterValue,
      final String? stopReason,
      final double? energyDelivered,
      final double? cost,
      final Map<String, dynamic>? metadata}) = _$ChargingTransactionImpl;

  factory _ChargingTransaction.fromJson(Map<String, dynamic> json) =
      _$ChargingTransactionImpl.fromJson;

  @override
  String get transactionId;
  @override
  String get connectorId;
  @override
  String get idTag;
  @override
  DateTime get startTime;
  @override
  double get startMeterValue;
  @override
  DateTime? get stopTime;
  @override
  double? get stopMeterValue;
  @override
  String? get stopReason;
  @override
  double? get energyDelivered;
  @override
  double? get cost;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of ChargingTransaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChargingTransactionImplCopyWith<_$ChargingTransactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
