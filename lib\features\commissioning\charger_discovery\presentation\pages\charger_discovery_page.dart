import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../../../shared/domain/entities/charger_info.dart';
import '../widgets/discovered_charger_card.dart';
import '../widgets/discovery_status_widget.dart';
import '../../data/services/bluetooth_charger_service.dart';
import '../../data/services/wifi_charger_service.dart';

/// Main charger discovery page with Bluetooth and WiFi scanning
class ChargerDiscoveryPage extends StatefulWidget {
  static const String routeName = '/commissioning/discovery';

  const ChargerDiscoveryPage({super.key});

  @override
  State<ChargerDiscoveryPage> createState() => _ChargerDiscoveryPageState();
}

class _ChargerDiscoveryPageState extends State<ChargerDiscoveryPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isScanning = false;
  final List<DiscoveredCharger> _discoveredChargers = [];
  String? _errorMessage;

  // Services
  late BluetoothChargerService _bluetoothService;
  late WiFiChargerService _wifiService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _checkBluetoothState();
    _initializeBluetoothService();
    _initializeWiFiService();
    // Auto-start real network scanning
    Future.delayed(const Duration(milliseconds: 500), () {
      _startScanning();
    });
  }

  void _initializeBluetoothService() {
    _bluetoothService = BluetoothChargerService();

    // Listen to discovered chargers
    _bluetoothService.discoveredChargersStream.listen((chargers) {
      if (mounted) {
        setState(() {
          _discoveredChargers.clear();
          for (final charger in chargers) {
            _discoveredChargers.add(
              DiscoveredCharger(
                id: charger.id,
                name: '${charger.manufacturer} ${charger.model}',
                serialNumber: charger.serialNumber,
                signalStrength: -50, // Default signal strength for Bluetooth
                connectionType: 'bluetooth',
                discoveredAt: DateTime.now(),
                advertisementData: {
                  'manufacturer': charger.manufacturer,
                  'model': charger.model,
                  'firmware': charger.firmwareVersion,
                  'status': charger.status.toString(),
                },
              ),
            );
          }
        });
      }
    });

    // Listen to connection status
    _bluetoothService.connectionStatusStream.listen((status) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(status)));
      }
    });
  }

  void _initializeWiFiService() {
    _wifiService = WiFiChargerService();
    _wifiService.initialize();

    // Listen to discovered WiFi chargers
    _wifiService.discoveredChargersStream.listen((chargers) {
      if (mounted) {
        setState(() {
          // Remove existing WiFi chargers and add new ones
          _discoveredChargers.removeWhere((c) => c.connectionType == 'wifi');
          _discoveredChargers.addAll(chargers);
        });
      }
    });

    // Listen to scan status
    _wifiService.scanStatusStream.listen((status) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('WiFi: $status')),
        );
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _stopScanning();
    _bluetoothService.dispose();
    _wifiService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Discover Chargers'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.bluetooth), text: 'Bluetooth'),
            Tab(icon: Icon(Icons.wifi), text: 'Wi-Fi'),
            Tab(icon: Icon(Icons.cable), text: 'Ethernet'),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(_isScanning ? Icons.stop : Icons.refresh),
            onPressed: _isScanning ? _stopScanning : _startScanning,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'clear') {
                _clearChargers();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear',
                child: ListTile(
                  leading: Icon(Icons.clear_all),
                  title: Text('Clear All'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          DiscoveryStatusWidget(
            isScanning: _isScanning,
            discoveredCount: _discoveredChargers.length,
            errorMessage: _errorMessage,
            onRetry: _startScanning,
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBluetoothTab(),
                _buildWiFiTab(),
                _buildEthernetTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: 'qr_scan',
            onPressed: _navigateToQRScanner,
            backgroundColor: Theme.of(context).primaryColor,
            child: const Icon(Icons.qr_code_scanner),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: 'manual_entry',
            onPressed: _navigateToManualEntry,
            backgroundColor: Colors.grey[600],
            child: const Icon(Icons.keyboard),
          ),
        ],
      ),
    );
  }

  Widget _buildBluetoothTab() {
    return RefreshIndicator(
      onRefresh: _refreshBluetoothDevices,
      child: _buildChargerList(),
    );
  }

  Widget _buildWiFiTab() {
    return RefreshIndicator(
      onRefresh: _refreshWiFiDevices,
      child: _buildChargerList(),
    );
  }

  Widget _buildEthernetTab() {
    return RefreshIndicator(
      onRefresh: _refreshEthernetDevices,
      child: _buildChargerList(),
    );
  }

  Widget _buildChargerList() {
    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_discoveredChargers.isEmpty && !_isScanning) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _discoveredChargers.length,
      itemBuilder: (context, index) {
        final charger = _discoveredChargers[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: DiscoveredChargerCard(
            charger: charger,
            onConnect: () => _connectToCharger(charger),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No Chargers Found',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Make sure the charger is powered on and in pairing mode',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _startScanning,
            icon: const Icon(Icons.refresh),
            label: const Text('Start Scanning'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            'Discovery Error',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.red[600]),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _errorMessage!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.red[500]),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _clearErrorAndRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  Future<void> _checkBluetoothState() async {
    try {
      if (await FlutterBluePlus.isSupported == false) {
        setState(() {
          _errorMessage = 'Bluetooth is not supported on this device';
        });
        return;
      }

      final state = await FlutterBluePlus.adapterState.first;
      if (state != BluetoothAdapterState.on) {
        setState(() {
          _errorMessage = 'Please enable Bluetooth to discover chargers';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to check Bluetooth state: $e';
      });
    }
  }

  Future<void> _startScanning() async {
    setState(() {
      _isScanning = true;
      _errorMessage = null;
      _discoveredChargers.clear();
    });

    try {
      // Start both Bluetooth and WiFi scanning in parallel
      final futures = [
        _bluetoothService.startScanning(timeout: const Duration(seconds: 30)),
        _wifiService.startScanning(timeout: const Duration(seconds: 30)),
      ];

      await Future.wait(futures);

      // Real scanning completed - no mock data added
    } catch (e) {
      setState(() {
        _errorMessage = 'Scanning failed: $e';
        _isScanning = false;
      });
    }
  }





  void _stopScanning() {
    _bluetoothService.stopScanning();
    _wifiService.stopScanning();
    setState(() {
      _isScanning = false;
    });
  }

  Future<void> _refreshBluetoothDevices() async {
    await _startScanning();
  }

  Future<void> _refreshWiFiDevices() async {
    setState(() {
      _isScanning = true;
      _errorMessage = null;
      // Remove existing WiFi chargers
      _discoveredChargers.removeWhere((c) => c.connectionType == 'wifi');
    });

    try {
      await _wifiService.startScanning(timeout: const Duration(seconds: 30));
    } catch (e) {
      setState(() {
        _errorMessage = 'WiFi scan failed: $e';
      });
    } finally {
      setState(() {
        _isScanning = false;
      });
    }
  }

  Future<void> _refreshEthernetDevices() async {
    // Implement Ethernet discovery
    await Future.delayed(const Duration(seconds: 1));
  }

  void _clearErrorAndRetry() {
    setState(() {
      _errorMessage = null;
    });
    _startScanning();
  }

  void _connectToCharger(DiscoveredCharger charger) {
    Navigator.pushNamed(
      context,
      '/commissioning/discovery/connect',
      arguments: {
        'serial': charger.serialNumber,
        'name': charger.name,
        'connectionType': charger.connectionType,
        'macAddress': charger.macAddress,
        'ipAddress': charger.ipAddress,
        'signalStrength': charger.signalStrength,
      },
    );
  }

  void _navigateToQRScanner() {
    Navigator.pushNamed(context, '/commissioning/discovery/qr-scanner');
  }

  void _navigateToManualEntry() {
    Navigator.pushNamed(context, '/commissioning/discovery/manual-entry');
  }



  void _clearChargers() {
    setState(() {
      _discoveredChargers.clear();
      _errorMessage = null;
      _isScanning = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All chargers cleared'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}
