import 'package:flutter/material.dart';
import '../../shared/domain/entities/commissioning_workflow.dart';
import '../../shared/presentation/widgets/commissioning_progress_widget.dart';

/// Widget displaying active commissioning workflows
class ActiveWorkflowsWidget extends StatelessWidget {
  const ActiveWorkflowsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Active Workflows',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                TextButton(
                  onPressed: () => _navigateToAllWorkflows(context),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildActiveWorkflowsList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveWorkflowsList(BuildContext context) {
    // Mock data for demonstration
    final activeWorkflows = _getMockActiveWorkflows();

    if (activeWorkflows.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      children: activeWorkflows
          .map((workflow) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: CompactCommissioningProgressWidget(
                  workflow: workflow,
                  onTap: () => _navigateToWorkflowDetails(context, workflow.id),
                ),
              ))
          .toList(),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Active Workflows',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start a new commissioning workflow to see it here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _startNewWorkflow(context),
            icon: const Icon(Icons.add),
            label: const Text('Start New Workflow'),
          ),
        ],
      ),
    );
  }

  List<CommissioningWorkflow> _getMockActiveWorkflows() {
    return [
      CommissioningWorkflow(
        id: 'workflow_001',
        chargerId: 'EVSE_001',
        technicianId: 'tech_001',
        steps: _createMockSteps(),
        currentStep: CommissioningStep.networkConfig,
        startedAt: DateTime.now().subtract(const Duration(minutes: 15)),
        progressPercentage: 0.4,
        isCompleted: false,
      ),
      CommissioningWorkflow(
        id: 'workflow_002',
        chargerId: 'EVSE_002',
        technicianId: 'tech_001',
        steps: _createMockSteps(),
        currentStep: CommissioningStep.parameterConfig,
        startedAt: DateTime.now().subtract(const Duration(minutes: 45)),
        progressPercentage: 0.7,
        isCompleted: false,
      ),
      CommissioningWorkflow(
        id: 'workflow_003',
        chargerId: 'EVSE_003',
        technicianId: 'tech_001',
        steps: _createMockSteps(),
        currentStep: CommissioningStep.discovery,
        startedAt: DateTime.now().subtract(const Duration(minutes: 5)),
        progressPercentage: 0.1,
        isCompleted: false,
      ),
    ];
  }

  List<WorkflowStep> _createMockSteps() {
    return [
      const WorkflowStep(
        step: CommissioningStep.discovery,
        name: 'Charger Discovery',
        description: 'Discover and connect to the charging station',
        status: StepStatus.completed,
      ),
      const WorkflowStep(
        step: CommissioningStep.authentication,
        name: 'Authentication',
        description: 'Authenticate with the charging station',
        status: StepStatus.completed,
      ),
      const WorkflowStep(
        step: CommissioningStep.networkConfig,
        name: 'Network Configuration',
        description: 'Configure network settings',
        status: StepStatus.inProgress,
      ),
      const WorkflowStep(
        step: CommissioningStep.ocppSetup,
        name: 'OCPP Setup',
        description: 'Configure OCPP connection to CMS',
        status: StepStatus.pending,
      ),
      const WorkflowStep(
        step: CommissioningStep.parameterConfig,
        name: 'Parameter Configuration',
        description: 'Configure charging parameters',
        status: StepStatus.pending,
      ),
      const WorkflowStep(
        step: CommissioningStep.diagnostics,
        name: 'Diagnostics',
        description: 'Run diagnostic tests',
        status: StepStatus.pending,
      ),
      const WorkflowStep(
        step: CommissioningStep.complianceCheck,
        name: 'Compliance Check',
        description: 'Verify compliance with local regulations',
        status: StepStatus.pending,
      ),
      const WorkflowStep(
        step: CommissioningStep.finalization,
        name: 'Finalization',
        description: 'Complete commissioning and generate report',
        status: StepStatus.pending,
      ),
    ];
  }

  void _navigateToAllWorkflows(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/workflows');
  }

  void _navigateToWorkflowDetails(BuildContext context, String workflowId) {
    Navigator.pushNamed(
      context,
      '/commissioning/workflow/details',
      arguments: workflowId,
    );
  }

  void _startNewWorkflow(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/workflow/new');
  }
}
