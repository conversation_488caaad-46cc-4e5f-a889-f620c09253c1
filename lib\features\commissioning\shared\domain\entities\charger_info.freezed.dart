// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'charger_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Connector _$ConnectorFromJson(Map<String, dynamic> json) {
  return _Connector.fromJson(json);
}

/// @nodoc
mixin _$Connector {
  String get id => throw _privateConstructorUsedError;
  ChargerType get type => throw _privateConstructorUsedError;
  double get maxPower => throw _privateConstructorUsedError;
  double get maxCurrent => throw _privateConstructorUsedError;
  ChargerStatus get status => throw _privateConstructorUsedError;
  String? get currentTransactionId => throw _privateConstructorUsedError;
  DateTime? get lastUsed => throw _privateConstructorUsedError;

  /// Serializes this Connector to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Connector
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConnectorCopyWith<Connector> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConnectorCopyWith<$Res> {
  factory $ConnectorCopyWith(Connector value, $Res Function(Connector) then) =
      _$ConnectorCopyWithImpl<$Res, Connector>;
  @useResult
  $Res call(
      {String id,
      ChargerType type,
      double maxPower,
      double maxCurrent,
      ChargerStatus status,
      String? currentTransactionId,
      DateTime? lastUsed});
}

/// @nodoc
class _$ConnectorCopyWithImpl<$Res, $Val extends Connector>
    implements $ConnectorCopyWith<$Res> {
  _$ConnectorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Connector
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? maxPower = null,
    Object? maxCurrent = null,
    Object? status = null,
    Object? currentTransactionId = freezed,
    Object? lastUsed = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ChargerType,
      maxPower: null == maxPower
          ? _value.maxPower
          : maxPower // ignore: cast_nullable_to_non_nullable
              as double,
      maxCurrent: null == maxCurrent
          ? _value.maxCurrent
          : maxCurrent // ignore: cast_nullable_to_non_nullable
              as double,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ChargerStatus,
      currentTransactionId: freezed == currentTransactionId
          ? _value.currentTransactionId
          : currentTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastUsed: freezed == lastUsed
          ? _value.lastUsed
          : lastUsed // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConnectorImplCopyWith<$Res>
    implements $ConnectorCopyWith<$Res> {
  factory _$$ConnectorImplCopyWith(
          _$ConnectorImpl value, $Res Function(_$ConnectorImpl) then) =
      __$$ConnectorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      ChargerType type,
      double maxPower,
      double maxCurrent,
      ChargerStatus status,
      String? currentTransactionId,
      DateTime? lastUsed});
}

/// @nodoc
class __$$ConnectorImplCopyWithImpl<$Res>
    extends _$ConnectorCopyWithImpl<$Res, _$ConnectorImpl>
    implements _$$ConnectorImplCopyWith<$Res> {
  __$$ConnectorImplCopyWithImpl(
      _$ConnectorImpl _value, $Res Function(_$ConnectorImpl) _then)
      : super(_value, _then);

  /// Create a copy of Connector
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? maxPower = null,
    Object? maxCurrent = null,
    Object? status = null,
    Object? currentTransactionId = freezed,
    Object? lastUsed = freezed,
  }) {
    return _then(_$ConnectorImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ChargerType,
      maxPower: null == maxPower
          ? _value.maxPower
          : maxPower // ignore: cast_nullable_to_non_nullable
              as double,
      maxCurrent: null == maxCurrent
          ? _value.maxCurrent
          : maxCurrent // ignore: cast_nullable_to_non_nullable
              as double,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ChargerStatus,
      currentTransactionId: freezed == currentTransactionId
          ? _value.currentTransactionId
          : currentTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastUsed: freezed == lastUsed
          ? _value.lastUsed
          : lastUsed // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ConnectorImpl implements _Connector {
  const _$ConnectorImpl(
      {required this.id,
      required this.type,
      required this.maxPower,
      required this.maxCurrent,
      required this.status,
      this.currentTransactionId,
      this.lastUsed});

  factory _$ConnectorImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConnectorImplFromJson(json);

  @override
  final String id;
  @override
  final ChargerType type;
  @override
  final double maxPower;
  @override
  final double maxCurrent;
  @override
  final ChargerStatus status;
  @override
  final String? currentTransactionId;
  @override
  final DateTime? lastUsed;

  @override
  String toString() {
    return 'Connector(id: $id, type: $type, maxPower: $maxPower, maxCurrent: $maxCurrent, status: $status, currentTransactionId: $currentTransactionId, lastUsed: $lastUsed)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConnectorImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.maxPower, maxPower) ||
                other.maxPower == maxPower) &&
            (identical(other.maxCurrent, maxCurrent) ||
                other.maxCurrent == maxCurrent) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.currentTransactionId, currentTransactionId) ||
                other.currentTransactionId == currentTransactionId) &&
            (identical(other.lastUsed, lastUsed) ||
                other.lastUsed == lastUsed));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, type, maxPower, maxCurrent,
      status, currentTransactionId, lastUsed);

  /// Create a copy of Connector
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConnectorImplCopyWith<_$ConnectorImpl> get copyWith =>
      __$$ConnectorImplCopyWithImpl<_$ConnectorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConnectorImplToJson(
      this,
    );
  }
}

abstract class _Connector implements Connector {
  const factory _Connector(
      {required final String id,
      required final ChargerType type,
      required final double maxPower,
      required final double maxCurrent,
      required final ChargerStatus status,
      final String? currentTransactionId,
      final DateTime? lastUsed}) = _$ConnectorImpl;

  factory _Connector.fromJson(Map<String, dynamic> json) =
      _$ConnectorImpl.fromJson;

  @override
  String get id;
  @override
  ChargerType get type;
  @override
  double get maxPower;
  @override
  double get maxCurrent;
  @override
  ChargerStatus get status;
  @override
  String? get currentTransactionId;
  @override
  DateTime? get lastUsed;

  /// Create a copy of Connector
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConnectorImplCopyWith<_$ConnectorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChargerInfo _$ChargerInfoFromJson(Map<String, dynamic> json) {
  return _ChargerInfo.fromJson(json);
}

/// @nodoc
mixin _$ChargerInfo {
  String get id => throw _privateConstructorUsedError;
  String get serialNumber => throw _privateConstructorUsedError;
  String get model => throw _privateConstructorUsedError;
  String get manufacturer => throw _privateConstructorUsedError;
  String get firmwareVersion => throw _privateConstructorUsedError;
  List<Connector> get connectors => throw _privateConstructorUsedError;
  ChargerStatus get status => throw _privateConstructorUsedError;
  DateTime get lastSeen => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double? get latitude => throw _privateConstructorUsedError;
  double? get longitude => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ChargerInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChargerInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChargerInfoCopyWith<ChargerInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChargerInfoCopyWith<$Res> {
  factory $ChargerInfoCopyWith(
          ChargerInfo value, $Res Function(ChargerInfo) then) =
      _$ChargerInfoCopyWithImpl<$Res, ChargerInfo>;
  @useResult
  $Res call(
      {String id,
      String serialNumber,
      String model,
      String manufacturer,
      String firmwareVersion,
      List<Connector> connectors,
      ChargerStatus status,
      DateTime lastSeen,
      String? location,
      String? description,
      double? latitude,
      double? longitude,
      Map<String, dynamic>? metadata,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$ChargerInfoCopyWithImpl<$Res, $Val extends ChargerInfo>
    implements $ChargerInfoCopyWith<$Res> {
  _$ChargerInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChargerInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? serialNumber = null,
    Object? model = null,
    Object? manufacturer = null,
    Object? firmwareVersion = null,
    Object? connectors = null,
    Object? status = null,
    Object? lastSeen = null,
    Object? location = freezed,
    Object? description = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? metadata = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      serialNumber: null == serialNumber
          ? _value.serialNumber
          : serialNumber // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      manufacturer: null == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String,
      firmwareVersion: null == firmwareVersion
          ? _value.firmwareVersion
          : firmwareVersion // ignore: cast_nullable_to_non_nullable
              as String,
      connectors: null == connectors
          ? _value.connectors
          : connectors // ignore: cast_nullable_to_non_nullable
              as List<Connector>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ChargerStatus,
      lastSeen: null == lastSeen
          ? _value.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as DateTime,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double?,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChargerInfoImplCopyWith<$Res>
    implements $ChargerInfoCopyWith<$Res> {
  factory _$$ChargerInfoImplCopyWith(
          _$ChargerInfoImpl value, $Res Function(_$ChargerInfoImpl) then) =
      __$$ChargerInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String serialNumber,
      String model,
      String manufacturer,
      String firmwareVersion,
      List<Connector> connectors,
      ChargerStatus status,
      DateTime lastSeen,
      String? location,
      String? description,
      double? latitude,
      double? longitude,
      Map<String, dynamic>? metadata,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$ChargerInfoImplCopyWithImpl<$Res>
    extends _$ChargerInfoCopyWithImpl<$Res, _$ChargerInfoImpl>
    implements _$$ChargerInfoImplCopyWith<$Res> {
  __$$ChargerInfoImplCopyWithImpl(
      _$ChargerInfoImpl _value, $Res Function(_$ChargerInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChargerInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? serialNumber = null,
    Object? model = null,
    Object? manufacturer = null,
    Object? firmwareVersion = null,
    Object? connectors = null,
    Object? status = null,
    Object? lastSeen = null,
    Object? location = freezed,
    Object? description = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? metadata = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ChargerInfoImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      serialNumber: null == serialNumber
          ? _value.serialNumber
          : serialNumber // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      manufacturer: null == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String,
      firmwareVersion: null == firmwareVersion
          ? _value.firmwareVersion
          : firmwareVersion // ignore: cast_nullable_to_non_nullable
              as String,
      connectors: null == connectors
          ? _value._connectors
          : connectors // ignore: cast_nullable_to_non_nullable
              as List<Connector>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ChargerStatus,
      lastSeen: null == lastSeen
          ? _value.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as DateTime,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double?,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChargerInfoImpl implements _ChargerInfo {
  const _$ChargerInfoImpl(
      {required this.id,
      required this.serialNumber,
      required this.model,
      required this.manufacturer,
      required this.firmwareVersion,
      required final List<Connector> connectors,
      required this.status,
      required this.lastSeen,
      this.location,
      this.description,
      this.latitude,
      this.longitude,
      final Map<String, dynamic>? metadata,
      this.createdAt,
      this.updatedAt})
      : _connectors = connectors,
        _metadata = metadata;

  factory _$ChargerInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChargerInfoImplFromJson(json);

  @override
  final String id;
  @override
  final String serialNumber;
  @override
  final String model;
  @override
  final String manufacturer;
  @override
  final String firmwareVersion;
  final List<Connector> _connectors;
  @override
  List<Connector> get connectors {
    if (_connectors is EqualUnmodifiableListView) return _connectors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_connectors);
  }

  @override
  final ChargerStatus status;
  @override
  final DateTime lastSeen;
  @override
  final String? location;
  @override
  final String? description;
  @override
  final double? latitude;
  @override
  final double? longitude;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ChargerInfo(id: $id, serialNumber: $serialNumber, model: $model, manufacturer: $manufacturer, firmwareVersion: $firmwareVersion, connectors: $connectors, status: $status, lastSeen: $lastSeen, location: $location, description: $description, latitude: $latitude, longitude: $longitude, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChargerInfoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.serialNumber, serialNumber) ||
                other.serialNumber == serialNumber) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.manufacturer, manufacturer) ||
                other.manufacturer == manufacturer) &&
            (identical(other.firmwareVersion, firmwareVersion) ||
                other.firmwareVersion == firmwareVersion) &&
            const DeepCollectionEquality()
                .equals(other._connectors, _connectors) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.lastSeen, lastSeen) ||
                other.lastSeen == lastSeen) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      serialNumber,
      model,
      manufacturer,
      firmwareVersion,
      const DeepCollectionEquality().hash(_connectors),
      status,
      lastSeen,
      location,
      description,
      latitude,
      longitude,
      const DeepCollectionEquality().hash(_metadata),
      createdAt,
      updatedAt);

  /// Create a copy of ChargerInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChargerInfoImplCopyWith<_$ChargerInfoImpl> get copyWith =>
      __$$ChargerInfoImplCopyWithImpl<_$ChargerInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChargerInfoImplToJson(
      this,
    );
  }
}

abstract class _ChargerInfo implements ChargerInfo {
  const factory _ChargerInfo(
      {required final String id,
      required final String serialNumber,
      required final String model,
      required final String manufacturer,
      required final String firmwareVersion,
      required final List<Connector> connectors,
      required final ChargerStatus status,
      required final DateTime lastSeen,
      final String? location,
      final String? description,
      final double? latitude,
      final double? longitude,
      final Map<String, dynamic>? metadata,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ChargerInfoImpl;

  factory _ChargerInfo.fromJson(Map<String, dynamic> json) =
      _$ChargerInfoImpl.fromJson;

  @override
  String get id;
  @override
  String get serialNumber;
  @override
  String get model;
  @override
  String get manufacturer;
  @override
  String get firmwareVersion;
  @override
  List<Connector> get connectors;
  @override
  ChargerStatus get status;
  @override
  DateTime get lastSeen;
  @override
  String? get location;
  @override
  String? get description;
  @override
  double? get latitude;
  @override
  double? get longitude;
  @override
  Map<String, dynamic>? get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of ChargerInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChargerInfoImplCopyWith<_$ChargerInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DiscoveredCharger _$DiscoveredChargerFromJson(Map<String, dynamic> json) {
  return _DiscoveredCharger.fromJson(json);
}

/// @nodoc
mixin _$DiscoveredCharger {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get serialNumber => throw _privateConstructorUsedError;
  int get signalStrength => throw _privateConstructorUsedError;
  String get connectionType =>
      throw _privateConstructorUsedError; // 'bluetooth', 'wifi', 'ethernet'
  String? get macAddress => throw _privateConstructorUsedError;
  String? get ipAddress => throw _privateConstructorUsedError;
  Map<String, dynamic>? get advertisementData =>
      throw _privateConstructorUsedError;
  DateTime? get discoveredAt => throw _privateConstructorUsedError;

  /// Serializes this DiscoveredCharger to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DiscoveredCharger
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DiscoveredChargerCopyWith<DiscoveredCharger> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscoveredChargerCopyWith<$Res> {
  factory $DiscoveredChargerCopyWith(
          DiscoveredCharger value, $Res Function(DiscoveredCharger) then) =
      _$DiscoveredChargerCopyWithImpl<$Res, DiscoveredCharger>;
  @useResult
  $Res call(
      {String id,
      String name,
      String serialNumber,
      int signalStrength,
      String connectionType,
      String? macAddress,
      String? ipAddress,
      Map<String, dynamic>? advertisementData,
      DateTime? discoveredAt});
}

/// @nodoc
class _$DiscoveredChargerCopyWithImpl<$Res, $Val extends DiscoveredCharger>
    implements $DiscoveredChargerCopyWith<$Res> {
  _$DiscoveredChargerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DiscoveredCharger
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? serialNumber = null,
    Object? signalStrength = null,
    Object? connectionType = null,
    Object? macAddress = freezed,
    Object? ipAddress = freezed,
    Object? advertisementData = freezed,
    Object? discoveredAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      serialNumber: null == serialNumber
          ? _value.serialNumber
          : serialNumber // ignore: cast_nullable_to_non_nullable
              as String,
      signalStrength: null == signalStrength
          ? _value.signalStrength
          : signalStrength // ignore: cast_nullable_to_non_nullable
              as int,
      connectionType: null == connectionType
          ? _value.connectionType
          : connectionType // ignore: cast_nullable_to_non_nullable
              as String,
      macAddress: freezed == macAddress
          ? _value.macAddress
          : macAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      advertisementData: freezed == advertisementData
          ? _value.advertisementData
          : advertisementData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      discoveredAt: freezed == discoveredAt
          ? _value.discoveredAt
          : discoveredAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DiscoveredChargerImplCopyWith<$Res>
    implements $DiscoveredChargerCopyWith<$Res> {
  factory _$$DiscoveredChargerImplCopyWith(_$DiscoveredChargerImpl value,
          $Res Function(_$DiscoveredChargerImpl) then) =
      __$$DiscoveredChargerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String serialNumber,
      int signalStrength,
      String connectionType,
      String? macAddress,
      String? ipAddress,
      Map<String, dynamic>? advertisementData,
      DateTime? discoveredAt});
}

/// @nodoc
class __$$DiscoveredChargerImplCopyWithImpl<$Res>
    extends _$DiscoveredChargerCopyWithImpl<$Res, _$DiscoveredChargerImpl>
    implements _$$DiscoveredChargerImplCopyWith<$Res> {
  __$$DiscoveredChargerImplCopyWithImpl(_$DiscoveredChargerImpl _value,
      $Res Function(_$DiscoveredChargerImpl) _then)
      : super(_value, _then);

  /// Create a copy of DiscoveredCharger
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? serialNumber = null,
    Object? signalStrength = null,
    Object? connectionType = null,
    Object? macAddress = freezed,
    Object? ipAddress = freezed,
    Object? advertisementData = freezed,
    Object? discoveredAt = freezed,
  }) {
    return _then(_$DiscoveredChargerImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      serialNumber: null == serialNumber
          ? _value.serialNumber
          : serialNumber // ignore: cast_nullable_to_non_nullable
              as String,
      signalStrength: null == signalStrength
          ? _value.signalStrength
          : signalStrength // ignore: cast_nullable_to_non_nullable
              as int,
      connectionType: null == connectionType
          ? _value.connectionType
          : connectionType // ignore: cast_nullable_to_non_nullable
              as String,
      macAddress: freezed == macAddress
          ? _value.macAddress
          : macAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      advertisementData: freezed == advertisementData
          ? _value._advertisementData
          : advertisementData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      discoveredAt: freezed == discoveredAt
          ? _value.discoveredAt
          : discoveredAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DiscoveredChargerImpl implements _DiscoveredCharger {
  const _$DiscoveredChargerImpl(
      {required this.id,
      required this.name,
      required this.serialNumber,
      required this.signalStrength,
      required this.connectionType,
      this.macAddress,
      this.ipAddress,
      final Map<String, dynamic>? advertisementData,
      this.discoveredAt})
      : _advertisementData = advertisementData;

  factory _$DiscoveredChargerImpl.fromJson(Map<String, dynamic> json) =>
      _$$DiscoveredChargerImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String serialNumber;
  @override
  final int signalStrength;
  @override
  final String connectionType;
// 'bluetooth', 'wifi', 'ethernet'
  @override
  final String? macAddress;
  @override
  final String? ipAddress;
  final Map<String, dynamic>? _advertisementData;
  @override
  Map<String, dynamic>? get advertisementData {
    final value = _advertisementData;
    if (value == null) return null;
    if (_advertisementData is EqualUnmodifiableMapView)
      return _advertisementData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? discoveredAt;

  @override
  String toString() {
    return 'DiscoveredCharger(id: $id, name: $name, serialNumber: $serialNumber, signalStrength: $signalStrength, connectionType: $connectionType, macAddress: $macAddress, ipAddress: $ipAddress, advertisementData: $advertisementData, discoveredAt: $discoveredAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiscoveredChargerImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.serialNumber, serialNumber) ||
                other.serialNumber == serialNumber) &&
            (identical(other.signalStrength, signalStrength) ||
                other.signalStrength == signalStrength) &&
            (identical(other.connectionType, connectionType) ||
                other.connectionType == connectionType) &&
            (identical(other.macAddress, macAddress) ||
                other.macAddress == macAddress) &&
            (identical(other.ipAddress, ipAddress) ||
                other.ipAddress == ipAddress) &&
            const DeepCollectionEquality()
                .equals(other._advertisementData, _advertisementData) &&
            (identical(other.discoveredAt, discoveredAt) ||
                other.discoveredAt == discoveredAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      serialNumber,
      signalStrength,
      connectionType,
      macAddress,
      ipAddress,
      const DeepCollectionEquality().hash(_advertisementData),
      discoveredAt);

  /// Create a copy of DiscoveredCharger
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DiscoveredChargerImplCopyWith<_$DiscoveredChargerImpl> get copyWith =>
      __$$DiscoveredChargerImplCopyWithImpl<_$DiscoveredChargerImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DiscoveredChargerImplToJson(
      this,
    );
  }
}

abstract class _DiscoveredCharger implements DiscoveredCharger {
  const factory _DiscoveredCharger(
      {required final String id,
      required final String name,
      required final String serialNumber,
      required final int signalStrength,
      required final String connectionType,
      final String? macAddress,
      final String? ipAddress,
      final Map<String, dynamic>? advertisementData,
      final DateTime? discoveredAt}) = _$DiscoveredChargerImpl;

  factory _DiscoveredCharger.fromJson(Map<String, dynamic> json) =
      _$DiscoveredChargerImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get serialNumber;
  @override
  int get signalStrength;
  @override
  String get connectionType; // 'bluetooth', 'wifi', 'ethernet'
  @override
  String? get macAddress;
  @override
  String? get ipAddress;
  @override
  Map<String, dynamic>? get advertisementData;
  @override
  DateTime? get discoveredAt;

  /// Create a copy of DiscoveredCharger
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DiscoveredChargerImplCopyWith<_$DiscoveredChargerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChargerConnection _$ChargerConnectionFromJson(Map<String, dynamic> json) {
  return _ChargerConnection.fromJson(json);
}

/// @nodoc
mixin _$ChargerConnection {
  String get chargerId => throw _privateConstructorUsedError;
  String get connectionType => throw _privateConstructorUsedError;
  bool get isConnected => throw _privateConstructorUsedError;
  DateTime get connectedAt => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  Map<String, dynamic>? get connectionParams =>
      throw _privateConstructorUsedError;
  DateTime? get lastActivity => throw _privateConstructorUsedError;

  /// Serializes this ChargerConnection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChargerConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChargerConnectionCopyWith<ChargerConnection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChargerConnectionCopyWith<$Res> {
  factory $ChargerConnectionCopyWith(
          ChargerConnection value, $Res Function(ChargerConnection) then) =
      _$ChargerConnectionCopyWithImpl<$Res, ChargerConnection>;
  @useResult
  $Res call(
      {String chargerId,
      String connectionType,
      bool isConnected,
      DateTime connectedAt,
      String? sessionId,
      Map<String, dynamic>? connectionParams,
      DateTime? lastActivity});
}

/// @nodoc
class _$ChargerConnectionCopyWithImpl<$Res, $Val extends ChargerConnection>
    implements $ChargerConnectionCopyWith<$Res> {
  _$ChargerConnectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChargerConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chargerId = null,
    Object? connectionType = null,
    Object? isConnected = null,
    Object? connectedAt = null,
    Object? sessionId = freezed,
    Object? connectionParams = freezed,
    Object? lastActivity = freezed,
  }) {
    return _then(_value.copyWith(
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      connectionType: null == connectionType
          ? _value.connectionType
          : connectionType // ignore: cast_nullable_to_non_nullable
              as String,
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      connectedAt: null == connectedAt
          ? _value.connectedAt
          : connectedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      connectionParams: freezed == connectionParams
          ? _value.connectionParams
          : connectionParams // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      lastActivity: freezed == lastActivity
          ? _value.lastActivity
          : lastActivity // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChargerConnectionImplCopyWith<$Res>
    implements $ChargerConnectionCopyWith<$Res> {
  factory _$$ChargerConnectionImplCopyWith(_$ChargerConnectionImpl value,
          $Res Function(_$ChargerConnectionImpl) then) =
      __$$ChargerConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String chargerId,
      String connectionType,
      bool isConnected,
      DateTime connectedAt,
      String? sessionId,
      Map<String, dynamic>? connectionParams,
      DateTime? lastActivity});
}

/// @nodoc
class __$$ChargerConnectionImplCopyWithImpl<$Res>
    extends _$ChargerConnectionCopyWithImpl<$Res, _$ChargerConnectionImpl>
    implements _$$ChargerConnectionImplCopyWith<$Res> {
  __$$ChargerConnectionImplCopyWithImpl(_$ChargerConnectionImpl _value,
      $Res Function(_$ChargerConnectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChargerConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chargerId = null,
    Object? connectionType = null,
    Object? isConnected = null,
    Object? connectedAt = null,
    Object? sessionId = freezed,
    Object? connectionParams = freezed,
    Object? lastActivity = freezed,
  }) {
    return _then(_$ChargerConnectionImpl(
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      connectionType: null == connectionType
          ? _value.connectionType
          : connectionType // ignore: cast_nullable_to_non_nullable
              as String,
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      connectedAt: null == connectedAt
          ? _value.connectedAt
          : connectedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      connectionParams: freezed == connectionParams
          ? _value._connectionParams
          : connectionParams // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      lastActivity: freezed == lastActivity
          ? _value.lastActivity
          : lastActivity // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChargerConnectionImpl implements _ChargerConnection {
  const _$ChargerConnectionImpl(
      {required this.chargerId,
      required this.connectionType,
      required this.isConnected,
      required this.connectedAt,
      this.sessionId,
      final Map<String, dynamic>? connectionParams,
      this.lastActivity})
      : _connectionParams = connectionParams;

  factory _$ChargerConnectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChargerConnectionImplFromJson(json);

  @override
  final String chargerId;
  @override
  final String connectionType;
  @override
  final bool isConnected;
  @override
  final DateTime connectedAt;
  @override
  final String? sessionId;
  final Map<String, dynamic>? _connectionParams;
  @override
  Map<String, dynamic>? get connectionParams {
    final value = _connectionParams;
    if (value == null) return null;
    if (_connectionParams is EqualUnmodifiableMapView) return _connectionParams;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? lastActivity;

  @override
  String toString() {
    return 'ChargerConnection(chargerId: $chargerId, connectionType: $connectionType, isConnected: $isConnected, connectedAt: $connectedAt, sessionId: $sessionId, connectionParams: $connectionParams, lastActivity: $lastActivity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChargerConnectionImpl &&
            (identical(other.chargerId, chargerId) ||
                other.chargerId == chargerId) &&
            (identical(other.connectionType, connectionType) ||
                other.connectionType == connectionType) &&
            (identical(other.isConnected, isConnected) ||
                other.isConnected == isConnected) &&
            (identical(other.connectedAt, connectedAt) ||
                other.connectedAt == connectedAt) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            const DeepCollectionEquality()
                .equals(other._connectionParams, _connectionParams) &&
            (identical(other.lastActivity, lastActivity) ||
                other.lastActivity == lastActivity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      chargerId,
      connectionType,
      isConnected,
      connectedAt,
      sessionId,
      const DeepCollectionEquality().hash(_connectionParams),
      lastActivity);

  /// Create a copy of ChargerConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChargerConnectionImplCopyWith<_$ChargerConnectionImpl> get copyWith =>
      __$$ChargerConnectionImplCopyWithImpl<_$ChargerConnectionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChargerConnectionImplToJson(
      this,
    );
  }
}

abstract class _ChargerConnection implements ChargerConnection {
  const factory _ChargerConnection(
      {required final String chargerId,
      required final String connectionType,
      required final bool isConnected,
      required final DateTime connectedAt,
      final String? sessionId,
      final Map<String, dynamic>? connectionParams,
      final DateTime? lastActivity}) = _$ChargerConnectionImpl;

  factory _ChargerConnection.fromJson(Map<String, dynamic> json) =
      _$ChargerConnectionImpl.fromJson;

  @override
  String get chargerId;
  @override
  String get connectionType;
  @override
  bool get isConnected;
  @override
  DateTime get connectedAt;
  @override
  String? get sessionId;
  @override
  Map<String, dynamic>? get connectionParams;
  @override
  DateTime? get lastActivity;

  /// Create a copy of ChargerConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChargerConnectionImplCopyWith<_$ChargerConnectionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
