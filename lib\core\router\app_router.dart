import 'package:flutter/material.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/commissioning/presentation/pages/commissioning_home_page.dart';
import '../../features/commissioning/charger_discovery/presentation/pages/qr_scanner_page.dart';
import '../../features/commissioning/charger_discovery/presentation/pages/manual_entry_page.dart';

import '../../features/commissioning/charger_discovery/presentation/pages/charger_connection_page.dart';
import '../../features/commissioning/network_configuration/presentation/pages/network_config_page.dart';
import '../../features/commissioning/ocpp_integration/presentation/pages/ocpp_integration_page.dart';

/// Application router for navigation
class AppRouter {
  /// Generate route based on route settings
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/':
      case LoginPage.routeName:
        return MaterialPageRoute(
          builder: (_) => const LoginPage(),
          settings: settings,
        );

      case '/home':
      case '/commissioning-home':
        return MaterialPageRoute(
          builder: (_) => const CommissioningHomePage(),
          settings: settings,
        );

      // Charger Discovery Routes
      case '/commissioning/discovery':
        return MaterialPageRoute(
          builder: (_) => const ChargerDiscoveryPage(),
          settings: settings,
        );

      case '/commissioning/discovery/qr-scanner':
        return MaterialPageRoute(
          builder: (_) => const QRScannerPage(),
          settings: settings,
        );

      case '/commissioning/discovery/manual-entry':
        return MaterialPageRoute(
          builder: (_) => const ManualEntryPage(),
          settings: settings,
        );

      case '/commissioning/discovery/connect':
        return MaterialPageRoute(
          builder: (_) => const ChargerConnectionPage(),
          settings: settings,
        );

      // Network Configuration Routes
      case '/commissioning/network-config':
        return MaterialPageRoute(
          builder: (_) => const NetworkConfigPage(),
          settings: settings,
        );

      // OCPP Integration Routes
      case '/commissioning/ocpp':
        return MaterialPageRoute(
          builder: (_) => const OCPPIntegrationPage(),
          settings: settings,
        );

      // Firmware Management Routes
      case '/commissioning/firmware':
        return MaterialPageRoute(
          builder: (_) => const FirmwareManagementPage(),
          settings: settings,
        );

      // Parameter Configuration Routes
      case '/commissioning/parameters':
        return MaterialPageRoute(
          builder: (_) => const ParameterConfigPage(),
          settings: settings,
        );

      // Diagnostics Routes
      case '/commissioning/diagnostics':
        return MaterialPageRoute(
          builder: (_) => const DiagnosticsPage(),
          settings: settings,
        );

      // Workflow Routes
      case '/commissioning/workflow/new':
        return MaterialPageRoute(
          builder: (_) => const NewWorkflowPage(),
          settings: settings,
        );

      case '/commissioning/workflows':
        return MaterialPageRoute(
          builder: (_) => const WorkflowListPage(),
          settings: settings,
        );

      case '/commissioning/workflow/details':
        return MaterialPageRoute(
          builder: (_) => const WorkflowDetailsPage(),
          settings: settings,
        );

      // Legacy routes for backward compatibility
      case '/reports':
        return MaterialPageRoute(
          builder: (_) => const ReportsPage(),
          settings: settings,
        );

      case '/settings':
        return MaterialPageRoute(
          builder: (_) => const SettingsPage(),
          settings: settings,
        );

      case '/profile':
        return MaterialPageRoute(
          builder: (_) => const ProfilePage(),
          settings: settings,
        );

      case '/forgot-password':
        return MaterialPageRoute(
          builder: (_) => const ForgotPasswordPage(),
          settings: settings,
        );

      default:
        return MaterialPageRoute(
          builder: (_) => const NotFoundPage(),
          settings: settings,
        );
    }
  }
}

/// Placeholder pages - these will be implemented in later phases

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Home')),
      body: const Center(child: Text('Home Page - Coming Soon')),
    );
  }
}

class ChargerDiscoveryPage extends StatelessWidget {
  const ChargerDiscoveryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Charger Discovery')),
      body: const Center(child: Text('Charger Discovery - Coming Soon')),
    );
  }
}

class CommissioningPage extends StatelessWidget {
  const CommissioningPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Commissioning')),
      body: const Center(child: Text('Commissioning - Coming Soon')),
    );
  }
}

class DiagnosticsPage extends StatelessWidget {
  const DiagnosticsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Diagnostics')),
      body: const Center(child: Text('Diagnostics - Coming Soon')),
    );
  }
}

class ReportsPage extends StatelessWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Reports')),
      body: const Center(child: Text('Reports - Coming Soon')),
    );
  }
}

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: const Center(child: Text('Settings - Coming Soon')),
    );
  }
}

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Profile')),
      body: const Center(child: Text('Profile - Coming Soon')),
    );
  }
}

class ForgotPasswordPage extends StatelessWidget {
  const ForgotPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Forgot Password')),
      body: const Center(child: Text('Forgot Password - Coming Soon')),
    );
  }
}

class NotFoundPage extends StatelessWidget {
  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Page Not Found')),
      body: const Center(child: Text('404 - Page Not Found')),
    );
  }
}

// Network Configuration is now imported from the actual implementation

// OCPP Integration is now imported from the actual implementation

// Firmware Management
class FirmwareManagementPage extends StatelessWidget {
  const FirmwareManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firmware Management'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(child: Text('Firmware Management - Coming Soon')),
    );
  }
}

// Parameter Configuration
class ParameterConfigPage extends StatelessWidget {
  const ParameterConfigPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Parameter Configuration'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(child: Text('Parameter Configuration - Coming Soon')),
    );
  }
}

// Workflow Pages
class NewWorkflowPage extends StatelessWidget {
  const NewWorkflowPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('New Workflow'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(child: Text('New Workflow - Coming Soon')),
    );
  }
}

class WorkflowListPage extends StatelessWidget {
  const WorkflowListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Workflows'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(child: Text('Workflow List - Coming Soon')),
    );
  }
}

class WorkflowDetailsPage extends StatelessWidget {
  const WorkflowDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Workflow Details'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(child: Text('Workflow Details - Coming Soon')),
    );
  }
}
