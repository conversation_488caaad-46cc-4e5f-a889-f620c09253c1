import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

/// Certificate upload page for EV charger commissioning
class UploadCertificatesPage extends StatefulWidget {
  static const String routeName = '/commissioning/certificates/upload';

  const UploadCertificatesPage({super.key});

  @override
  State<UploadCertificatesPage> createState() => _UploadCertificatesPageState();
}

class _UploadCertificatesPageState extends State<UploadCertificatesPage> {
  Map<String, dynamic>? _chargerData;
  final List<CertificateFile> _selectedCertificates = [];
  bool _isUploading = false;
  double _uploadProgress = 0.0;
  String _statusMessage = '';

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    _chargerData = arguments?['chargerData'];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upload Certificates'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildChargerInfoCard(),
            const SizedBox(height: 24),
            _buildInstructionsCard(),
            const SizedBox(height: 24),
            _buildCertificateTypesSection(),
            const SizedBox(height: 24),
            if (_selectedCertificates.isNotEmpty) _buildSelectedCertificatesSection(),
            if (_isUploading) ...[
              const SizedBox(height: 24),
              _buildUploadProgressSection(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChargerInfoCard() {
    final chargerName = _chargerData?['name'] ?? 'Unknown Charger';
    final chargerSerial = _chargerData?['serial'] ?? 'Unknown Serial';

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.security, size: 32, color: Theme.of(context).primaryColor),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Certificate Management',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Device: $chargerName ($chargerSerial)',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionsCard() {
    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'Certificate Upload Instructions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '• Select the appropriate certificate files for your charger\n'
              '• Supported formats: .pem, .crt, .cer, .p12, .pfx\n'
              '• Ensure certificates are valid and not expired\n'
              '• Root CA and intermediate certificates may be required',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.blue[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCertificateTypesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.upload_file, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Certificate Types',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildCertificateTypeCard(
              'Client Certificate',
              'Certificate for client authentication with the OCPP server',
              Icons.person,
              Colors.blue,
              CertificateType.client,
            ),
            const SizedBox(height: 12),
            _buildCertificateTypeCard(
              'Root CA Certificate',
              'Root Certificate Authority for validating server certificates',
              Icons.verified_user,
              Colors.green,
              CertificateType.rootCA,
            ),
            const SizedBox(height: 12),
            _buildCertificateTypeCard(
              'Intermediate CA Certificate',
              'Intermediate Certificate Authority in the trust chain',
              Icons.link,
              Colors.orange,
              CertificateType.intermediateCA,
            ),
            const SizedBox(height: 12),
            _buildCertificateTypeCard(
              'Server Certificate',
              'Certificate for TLS server authentication',
              Icons.dns,
              Colors.purple,
              CertificateType.server,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCertificateTypeCard(
    String title,
    String description,
    IconData icon,
    Color color,
    CertificateType type,
  ) {
    return Card(
      elevation: 1,
      child: InkWell(
        onTap: () => _selectCertificateFile(type),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.add_circle_outline, color: color),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedCertificatesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.file_present, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Selected Certificates (${_selectedCertificates.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _selectedCertificates.length,
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                final cert = _selectedCertificates[index];
                return _buildCertificateFileCard(cert, index);
              },
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isUploading ? null : _uploadCertificates,
                icon: const Icon(Icons.cloud_upload),
                label: const Text('Upload All Certificates'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCertificateFileCard(CertificateFile cert, int index) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getCertificateTypeColor(cert.type).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getCertificateTypeColor(cert.type).withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(_getCertificateTypeIcon(cert.type), color: _getCertificateTypeColor(cert.type)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getCertificateTypeName(cert.type),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  cert.file.path.split('/').last,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  'Size: ${(cert.file.lengthSync() / 1024).toStringAsFixed(1)} KB',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _selectedCertificates.removeAt(index);
              });
            },
            icon: const Icon(Icons.delete),
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildUploadProgressSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cloud_upload, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Upload Progress',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: _uploadProgress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
            ),
            const SizedBox(height: 8),
            Text(
              '${(_uploadProgress * 100).toInt()}% Complete',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _statusMessage,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectCertificateFile(CertificateType type) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pem', 'crt', 'cer', 'p12', 'pfx', 'key'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        // Check if certificate type already exists
        final existingIndex = _selectedCertificates.indexWhere((cert) => cert.type == type);

        setState(() {
          if (existingIndex != -1) {
            // Replace existing certificate of same type
            _selectedCertificates[existingIndex] = CertificateFile(file: file, type: type);
          } else {
            // Add new certificate
            _selectedCertificates.add(CertificateFile(file: file, type: type));
          }
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${_getCertificateTypeName(type)} selected successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to select certificate: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _uploadCertificates() async {
    if (_selectedCertificates.isEmpty) return;

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
      _statusMessage = 'Preparing certificates for upload...';
    });

    try {
      for (int i = 0; i < _selectedCertificates.length; i++) {
        final cert = _selectedCertificates[i];

        setState(() {
          _statusMessage = 'Uploading ${_getCertificateTypeName(cert.type)}...';
          _uploadProgress = (i + 0.5) / _selectedCertificates.length;
        });

        // Simulate upload process
        await Future.delayed(const Duration(seconds: 2));

        setState(() {
          _statusMessage = 'Validating ${_getCertificateTypeName(cert.type)}...';
          _uploadProgress = (i + 1) / _selectedCertificates.length;
        });

        // Simulate validation
        await Future.delayed(const Duration(seconds: 1));
      }

      setState(() {
        _statusMessage = 'All certificates uploaded successfully!';
        _uploadProgress = 1.0;
      });

      // Show success dialog
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        _showSuccessDialog();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Upload failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 32),
            const SizedBox(width: 12),
            const Text('Upload Complete'),
          ],
        ),
        content: Text(
          '${_selectedCertificates.length} certificate(s) have been successfully uploaded and installed on the charger.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  Color _getCertificateTypeColor(CertificateType type) {
    switch (type) {
      case CertificateType.client:
        return Colors.blue;
      case CertificateType.rootCA:
        return Colors.green;
      case CertificateType.intermediateCA:
        return Colors.orange;
      case CertificateType.server:
        return Colors.purple;
    }
  }

  IconData _getCertificateTypeIcon(CertificateType type) {
    switch (type) {
      case CertificateType.client:
        return Icons.person;
      case CertificateType.rootCA:
        return Icons.verified_user;
      case CertificateType.intermediateCA:
        return Icons.link;
      case CertificateType.server:
        return Icons.dns;
    }
  }

  String _getCertificateTypeName(CertificateType type) {
    switch (type) {
      case CertificateType.client:
        return 'Client Certificate';
      case CertificateType.rootCA:
        return 'Root CA Certificate';
      case CertificateType.intermediateCA:
        return 'Intermediate CA Certificate';
      case CertificateType.server:
        return 'Server Certificate';
    }
  }
}

enum CertificateType {
  client,
  rootCA,
  intermediateCA,
  server,
}

class CertificateFile {
  final File file;
  final CertificateType type;

  CertificateFile({required this.file, required this.type});
}
