import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  const Failure({required this.message, this.code, this.details});

  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  @override
  List<Object?> get props => [message, code, details];

  /// Factory method for unknown failures
  const factory Failure.unknown({
    required String message,
    String? code,
    Map<String, dynamic>? details,
  }) = UnknownFailure;

  /// Factory method for not found failures
  const factory Failure.notFound({
    required String message,
    String? code,
    Map<String, dynamic>? details,
  }) = NotFoundFailure;
}

/// Network-related failures
class NetworkFailure extends Failure {
  const NetworkFailure({required super.message, super.code, super.details});
}

/// Server-related failures
class ServerFailure extends Failure {
  const ServerFailure({required super.message, super.code, super.details});
}

/// Authentication failures
class AuthFailure extends Failure {
  const AuthFailure({required super.message, super.code, super.details});
}

/// Cache/Storage failures
class CacheFailure extends Failure {
  const CacheFailure({required super.message, super.code, super.details});
}

/// Validation failures
class ValidationFailure extends Failure {
  const ValidationFailure({required super.message, super.code, super.details});
}

/// Permission failures
class PermissionFailure extends Failure {
  const PermissionFailure({required super.message, super.code, super.details});
}

/// Device/Hardware failures
class DeviceFailure extends Failure {
  const DeviceFailure({required super.message, super.code, super.details});
}

/// OCPP Protocol failures
class OcppFailure extends Failure {
  const OcppFailure({required super.message, super.code, super.details});
}

/// Charger-specific failures
class ChargerFailure extends Failure {
  const ChargerFailure({required super.message, super.code, super.details});
}

/// File operation failures
class FileFailure extends Failure {
  const FileFailure({required super.message, super.code, super.details});
}

/// Biometric authentication failures
class BiometricFailure extends Failure {
  const BiometricFailure({required super.message, super.code, super.details});
}

/// Network discovery failures
class NetworkDiscoveryFailure extends Failure {
  const NetworkDiscoveryFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// Firmware update failures
class FirmwareFailure extends Failure {
  const FirmwareFailure({required super.message, super.code, super.details});
}

/// Report generation failures
class ReportFailure extends Failure {
  const ReportFailure({required super.message, super.code, super.details});
}

/// Unknown failures
class UnknownFailure extends Failure {
  const UnknownFailure({required super.message, super.code, super.details});
}

/// Not found failures
class NotFoundFailure extends Failure {
  const NotFoundFailure({required super.message, super.code, super.details});
}
