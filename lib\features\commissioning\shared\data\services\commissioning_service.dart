import 'dart:async';
import 'dart:io';
import '../../ocpp/data/services/ocpp_service.dart';

/// Comprehensive commissioning service that orchestrates the entire EV charger commissioning flow
/// Implements Indian standards IS-17017 Parts 21-23 and DC-001/AC-001 compliance
class CommissioningService {
  final OCPPService _ocppService = OCPPService();
  
  bool _isCommissioning = false;
  String? _currentStep;
  double _progress = 0.0;
  
  final StreamController<CommissioningStatus> _statusController = 
      StreamController<CommissioningStatus>.broadcast();
  
  /// Stream of commissioning status updates
  Stream<CommissioningStatus> get statusStream => _statusController.stream;
  
  /// Check if commissioning is in progress
  bool get isCommissioning => _isCommissioning;
  
  /// Get current commissioning progress (0.0 to 1.0)
  double get progress => _progress;
  
  /// Get current commissioning step
  String? get currentStep => _currentStep;
  
  /// Start complete commissioning process
  Future<CommissioningResult> startCommissioning({
    required String centralSystemUrl,
    required String chargePointId,
    required CommissioningConfiguration config,
  }) async {
    if (_isCommissioning) {
      throw Exception('Commissioning already in progress');
    }
    
    _isCommissioning = true;
    _progress = 0.0;
    
    try {
      _updateStatus('Initializing commissioning...', 0.1);
      
      // Step 1: Connect to Central System
      _updateStatus('Connecting to Central System...', 0.2);
      final connected = await _ocppService.connect(
        centralSystemUrl: centralSystemUrl,
        chargePointId: chargePointId,
      );
      
      if (!connected) {
        throw CommissioningException('Failed to connect to Central System');
      }
      
      // Step 2: Configure Network Settings
      if (config.networkConfig != null) {
        _updateStatus('Configuring network settings...', 0.4);
        await _configureNetwork(config.networkConfig!);
      }
      
      // Step 3: Configure Charger Settings
      if (config.chargerConfig != null) {
        _updateStatus('Configuring charger settings...', 0.6);
        await _configureCharger(config.chargerConfig!);
      }
      
      // Step 4: Configure Charging Point
      _updateStatus('Configuring charging point...', 0.8);
      await _configureChargingPoint(config.chargingPointConfig);
      
      // Step 5: Validate Configuration
      _updateStatus('Validating configuration...', 0.9);
      await _validateConfiguration();
      
      // Step 6: Complete Commissioning
      _updateStatus('Commissioning completed successfully!', 1.0);
      
      await _ocppService.disconnect();
      
      return CommissioningResult(
        success: true,
        message: 'Commissioning completed successfully',
        chargePointId: chargePointId,
        centralSystemUrl: centralSystemUrl,
        timestamp: DateTime.now(),
      );
      
    } catch (e) {
      _updateStatus('Commissioning failed: $e', _progress);
      await _ocppService.disconnect();
      
      return CommissioningResult(
        success: false,
        message: 'Commissioning failed: $e',
        chargePointId: chargePointId,
        centralSystemUrl: centralSystemUrl,
        timestamp: DateTime.now(),
        error: e.toString(),
      );
    } finally {
      _isCommissioning = false;
      _ocppService.dispose();
    }
  }
  
  /// Configure network settings via OCPP
  Future<void> _configureNetwork(NetworkConfiguration config) async {
    await _ocppService.configureNetwork(
      wifiSSID: config.wifiSSID,
      wifiPassword: config.wifiPassword,
      gsmAPN: config.gsmAPN,
      ethernetMode: config.ethernetMode,
      staticIP: config.staticIP,
      subnetMask: config.subnetMask,
      gateway: config.gateway,
      dns: config.dns,
    );
  }
  
  /// Configure charger settings via OCPP
  Future<void> _configureCharger(ChargerConfiguration config) async {
    await _ocppService.configureCharger(
      maxPower: config.maxPower,
      controlMode: config.controlMode,
      chargerType: config.chargerType,
      acType2Enabled: config.acType2Enabled,
      gun1MaxCurrent: config.gun1MaxCurrent,
      gun2MaxCurrent: config.gun2MaxCurrent,
      authenticationType: config.authenticationType,
      sessionTimeout: config.sessionTimeout,
    );
  }
  
  /// Configure charging point settings via OCPP
  Future<void> _configureChargingPoint(ChargingPointConfiguration config) async {
    await _ocppService.configureChargingPoint(
      model: config.model,
      serialNumber: config.serialNumber,
      vendor: config.vendor,
      websocketUrl: config.websocketUrl,
      authToken: config.authToken,
      electricityPrice: config.electricityPrice,
      rfidTagLength: config.rfidTagLength,
      timeZone: config.timeZone,
    );
  }
  
  /// Validate the complete configuration
  Future<void> _validateConfiguration() async {
    // Send a test message to validate connection
    try {
      await _ocppService.changeConfiguration(
        key: 'CommissioningValidation',
        value: DateTime.now().toIso8601String(),
      );
    } catch (e) {
      throw CommissioningException('Configuration validation failed: $e');
    }
  }
  
  /// Update commissioning status
  void _updateStatus(String message, double progress) {
    _currentStep = message;
    _progress = progress;
    _statusController.add(CommissioningStatus(
      message: message,
      progress: progress,
      timestamp: DateTime.now(),
    ));
  }
  
  /// Upload firmware to charger
  Future<bool> uploadFirmware({
    required String firmwareUrl,
    required DateTime retrieveDate,
    int retries = 3,
    int retryInterval = 60,
  }) async {
    try {
      await _ocppService.updateFirmware(
        location: firmwareUrl,
        retrieveDate: retrieveDate,
        retries: retries,
        retryInterval: retryInterval,
      );
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Install certificate on charger
  Future<bool> installCertificate({
    required String certificateChain,
    required String certificateType,
  }) async {
    try {
      await _ocppService.installCertificate(
        certificateChain: certificateChain,
        certificateType: certificateType,
      );
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Delete certificate from charger
  Future<bool> deleteCertificate({
    required String certificateHashData,
  }) async {
    try {
      await _ocppService.deleteCertificate(
        certificateHashData: certificateHashData,
      );
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Get installed certificates from charger
  Future<List<String>> getInstalledCertificates() async {
    try {
      final result = await _ocppService.getInstalledCertificates();
      return (result['certificateHashData'] as List<dynamic>?)
          ?.cast<String>() ?? [];
    } catch (e) {
      return [];
    }
  }
  
  /// Dispose resources
  void dispose() {
    _statusController.close();
    _ocppService.dispose();
  }
}

/// Commissioning configuration data
class CommissioningConfiguration {
  final NetworkConfiguration? networkConfig;
  final ChargerConfiguration? chargerConfig;
  final ChargingPointConfiguration chargingPointConfig;
  
  CommissioningConfiguration({
    this.networkConfig,
    this.chargerConfig,
    required this.chargingPointConfig,
  });
}

/// Network configuration parameters
class NetworkConfiguration {
  final String? wifiSSID;
  final String? wifiPassword;
  final String? gsmAPN;
  final String? ethernetMode;
  final String? staticIP;
  final String? subnetMask;
  final String? gateway;
  final String? dns;
  
  NetworkConfiguration({
    this.wifiSSID,
    this.wifiPassword,
    this.gsmAPN,
    this.ethernetMode,
    this.staticIP,
    this.subnetMask,
    this.gateway,
    this.dns,
  });
}

/// Charger configuration parameters
class ChargerConfiguration {
  final double? maxPower;
  final String? controlMode;
  final String? chargerType;
  final bool? acType2Enabled;
  final int? gun1MaxCurrent;
  final int? gun2MaxCurrent;
  final String? authenticationType;
  final int? sessionTimeout;
  
  ChargerConfiguration({
    this.maxPower,
    this.controlMode,
    this.chargerType,
    this.acType2Enabled,
    this.gun1MaxCurrent,
    this.gun2MaxCurrent,
    this.authenticationType,
    this.sessionTimeout,
  });
}

/// Commissioning status update
class CommissioningStatus {
  final String message;
  final double progress;
  final DateTime timestamp;

  CommissioningStatus({
    required this.message,
    required this.progress,
    required this.timestamp,
  });
}

/// Commissioning result
class CommissioningResult {
  final bool success;
  final String message;
  final String chargePointId;
  final String centralSystemUrl;
  final DateTime timestamp;
  final String? error;

  CommissioningResult({
    required this.success,
    required this.message,
    required this.chargePointId,
    required this.centralSystemUrl,
    required this.timestamp,
    this.error,
  });
}

/// Commissioning exception
class CommissioningException implements Exception {
  final String message;

  CommissioningException(this.message);

  @override
  String toString() => 'CommissioningException: $message';
}

/// Charging point configuration parameters
class ChargingPointConfiguration {
  final String model;
  final String serialNumber;
  final String vendor;
  final String websocketUrl;
  final String? authToken;
  final double? electricityPrice;
  final int? rfidTagLength;
  final String? timeZone;
  
  ChargingPointConfiguration({
    required this.model,
    required this.serialNumber,
    required this.vendor,
    required this.websocketUrl,
    this.authToken,
    this.electricityPrice,
    this.rfidTagLength,
    this.timeZone,
  });
}
