import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Final charging point configuration page with device info, network settings, pricing, and RFID
class ChargingPointConfigurationPage extends StatefulWidget {
  static const String routeName = '/commissioning/configuration/charging-point';

  const ChargingPointConfigurationPage({super.key});

  @override
  State<ChargingPointConfigurationPage> createState() => _ChargingPointConfigurationPageState();
}

class _ChargingPointConfigurationPageState extends State<ChargingPointConfigurationPage> {
  final _formKey = GlobalKey<FormState>();
  
  // Device Information
  final _modelNameController = TextEditingController();
  final _serialNumberController = TextEditingController();
  final _vendorController = TextEditingController();
  
  // Network Settings
  final _websocketUrlController = TextEditingController();
  final _authTokenController = TextEditingController();
  
  // Pricing & RFID
  final _electricityPriceController = TextEditingController();
  final _rfidTagLengthController = TextEditingController();
  
  // System Settings
  String _selectedTimezone = 'UTC';
  final List<String> _timezones = [
    'UTC',
    'America/New_York',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Berlin',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney',
  ];
  
  bool _isLoading = false;
  Map<String, dynamic>? _chargerData;
  Map<String, dynamic>? _networkConfig;
  Map<String, dynamic>? _chargerConfig;

  @override
  void initState() {
    super.initState();
    // Pre-fill some demo values
    _modelNameController.text = '60kW DC Fast Charger';
    _serialNumberController.text = '381';
    _vendorController.text = 'EcoPlug';
    _websocketUrlController.text = 'ws://ocpp.eeil.online:8080/mr003';
    _electricityPriceController.text = '17.90';
    _rfidTagLengthController.text = '10';
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    _chargerData = arguments?['chargerData'];
    _networkConfig = arguments?['networkConfig'];
    _chargerConfig = arguments?['chargerConfig'];
  }

  @override
  void dispose() {
    _modelNameController.dispose();
    _serialNumberController.dispose();
    _vendorController.dispose();
    _websocketUrlController.dispose();
    _authTokenController.dispose();
    _electricityPriceController.dispose();
    _rfidTagLengthController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Charging Point Configuration'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProgressIndicator(),
              const SizedBox(height: 24),
              _buildDeviceInformationSection(),
              const SizedBox(height: 24),
              _buildNetworkSettingsSection(),
              const SizedBox(height: 24),
              _buildPricingAndRFIDSection(),
              const SizedBox(height: 24),
              _buildSystemSettingsSection(),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildProgressIndicator() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, size: 32, color: Colors.green),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Final Configuration Step',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Complete the charging point setup to finish commissioning',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: 1.0, // 100% complete
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            const SizedBox(height: 8),
            Text(
              'Step 3 of 3 - Charging Point Configuration',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceInformationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Device Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _modelNameController,
              decoration: const InputDecoration(
                labelText: 'Model Name',
                hintText: 'e.g., 60kW DC Fast Charger',
                prefixIcon: Icon(Icons.device_hub),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter model name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _serialNumberController,
              decoration: const InputDecoration(
                labelText: 'Serial Number',
                hintText: 'e.g., 381',
                prefixIcon: Icon(Icons.confirmation_number),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter serial number';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _vendorController,
              decoration: const InputDecoration(
                labelText: 'Vendor',
                hintText: 'e.g., EcoPlug',
                prefixIcon: Icon(Icons.business),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter vendor name';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cloud, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Network Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _websocketUrlController,
              decoration: const InputDecoration(
                labelText: 'WebSocket URL',
                hintText: 'e.g., ws://ocpp.eeil.online:8080/mr003',
                prefixIcon: Icon(Icons.link),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter WebSocket URL';
                }
                if (!value.startsWith('ws://') && !value.startsWith('wss://')) {
                  return 'URL must start with ws:// or wss://';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _authTokenController,
              decoration: const InputDecoration(
                labelText: 'Authorization Token (Optional)',
                hintText: 'Enter authorization token if required',
                prefixIcon: Icon(Icons.vpn_key),
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingAndRFIDSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Pricing & RFID',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _electricityPriceController,
              decoration: const InputDecoration(
                labelText: 'Electricity Unit Price',
                hintText: 'e.g., 17.90',
                prefixIcon: Icon(Icons.attach_money),
                suffixText: 'per kWh',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter electricity unit price';
                }
                final price = double.tryParse(value);
                if (price == null || price < 0) {
                  return 'Please enter a valid price';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _rfidTagLengthController,
              decoration: const InputDecoration(
                labelText: 'RFID Tag Length',
                hintText: 'e.g., 10',
                prefixIcon: Icon(Icons.nfc),
                suffixText: 'characters',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter RFID tag length';
                }
                final length = int.tryParse(value);
                if (length == null || length < 4 || length > 32) {
                  return 'RFID tag length must be between 4 and 32 characters';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'System Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedTimezone,
              decoration: const InputDecoration(
                labelText: 'Time Zone',
                prefixIcon: Icon(Icons.access_time),
                border: OutlineInputBorder(),
              ),
              items: _timezones.map((timezone) {
                return DropdownMenuItem(
                  value: timezone,
                  child: Text(timezone),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTimezone = value!;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select a timezone';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isLoading ? null : _skipConfiguration,
              child: const Text('Skip'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _completeCommissioning,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                    )
                  : const Text('Complete Commissioning'),
            ),
          ),
        ],
      ),
    );
  }

  void _skipConfiguration() {
    _completeCommissioning();
  }

  Future<void> _completeCommissioning() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate saving final configuration
      await Future.delayed(const Duration(seconds: 3));

      // Show success dialog
      if (mounted) {
        await _showCompletionDialog();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to complete commissioning: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _showCompletionDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 32),
              const SizedBox(width: 12),
              const Text('Commissioning Complete!'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Your EV charger has been successfully commissioned with the following configuration:'),
                const SizedBox(height: 16),
                _buildConfigurationSummary(),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/commissioning-home',
                  (route) => false,
                );
              },
              child: const Text('Return to Home'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/commissioning/discovery',
                  (route) => false,
                );
              },
              child: const Text('Commission Another'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildConfigurationSummary() {
    return Card(
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Device: ${_modelNameController.text}', style: const TextStyle(fontWeight: FontWeight.bold)),
            Text('Serial: ${_serialNumberController.text}'),
            Text('Vendor: ${_vendorController.text}'),
            const SizedBox(height: 8),
            Text('WebSocket: ${_websocketUrlController.text}'),
            Text('Price: \$${_electricityPriceController.text}/kWh'),
            Text('Timezone: $_selectedTimezone'),
          ],
        ),
      ),
    );
  }
}
