// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commissioning_workflow.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WorkflowStepImpl _$$WorkflowStepImplFromJson(Map<String, dynamic> json) =>
    _$WorkflowStepImpl(
      step: $enumDecode(_$CommissioningStepEnumMap, json['step']),
      name: json['name'] as String,
      description: json['description'] as String,
      status: $enumDecode(_$StepStatusEnumMap, json['status']),
      startedAt: json['startedAt'] == null
          ? null
          : DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      errorMessage: json['errorMessage'] as String?,
      stepData: json['stepData'] as Map<String, dynamic>?,
      prerequisites: (json['prerequisites'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isOptional: json['isOptional'] as bool?,
      estimatedDurationMinutes:
          (json['estimatedDurationMinutes'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$WorkflowStepImplToJson(_$WorkflowStepImpl instance) =>
    <String, dynamic>{
      'step': _$CommissioningStepEnumMap[instance.step]!,
      'name': instance.name,
      'description': instance.description,
      'status': _$StepStatusEnumMap[instance.status]!,
      'startedAt': instance.startedAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'errorMessage': instance.errorMessage,
      'stepData': instance.stepData,
      'prerequisites': instance.prerequisites,
      'isOptional': instance.isOptional,
      'estimatedDurationMinutes': instance.estimatedDurationMinutes,
    };

const _$CommissioningStepEnumMap = {
  CommissioningStep.discovery: 'discovery',
  CommissioningStep.connection: 'connection',
  CommissioningStep.authentication: 'authentication',
  CommissioningStep.networkConfig: 'network_config',
  CommissioningStep.ocppSetup: 'ocpp_setup',
  CommissioningStep.firmwareUpdate: 'firmware_update',
  CommissioningStep.parameterConfig: 'parameter_config',
  CommissioningStep.diagnostics: 'diagnostics',
  CommissioningStep.complianceCheck: 'compliance_check',
  CommissioningStep.finalization: 'finalization',
  CommissioningStep.completed: 'completed',
};

const _$StepStatusEnumMap = {
  StepStatus.pending: 'pending',
  StepStatus.inProgress: 'in_progress',
  StepStatus.completed: 'completed',
  StepStatus.failed: 'failed',
  StepStatus.skipped: 'skipped',
  StepStatus.cancelled: 'cancelled',
};

_$CommissioningWorkflowImpl _$$CommissioningWorkflowImplFromJson(
        Map<String, dynamic> json) =>
    _$CommissioningWorkflowImpl(
      id: json['id'] as String,
      chargerId: json['chargerId'] as String,
      technicianId: json['technicianId'] as String,
      steps: (json['steps'] as List<dynamic>)
          .map((e) => WorkflowStep.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentStep: $enumDecode(_$CommissioningStepEnumMap, json['currentStep']),
      startedAt: DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      notes: json['notes'] as String?,
      configuration: json['configuration'] as Map<String, dynamic>?,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isCompleted: json['isCompleted'] as bool?,
      progressPercentage: (json['progressPercentage'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$CommissioningWorkflowImplToJson(
        _$CommissioningWorkflowImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'chargerId': instance.chargerId,
      'technicianId': instance.technicianId,
      'steps': instance.steps,
      'currentStep': _$CommissioningStepEnumMap[instance.currentStep]!,
      'startedAt': instance.startedAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'notes': instance.notes,
      'configuration': instance.configuration,
      'attachments': instance.attachments,
      'isCompleted': instance.isCompleted,
      'progressPercentage': instance.progressPercentage,
    };

_$CommissioningTemplateImpl _$$CommissioningTemplateImplFromJson(
        Map<String, dynamic> json) =>
    _$CommissioningTemplateImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      steps: (json['steps'] as List<dynamic>)
          .map((e) => $enumDecode(_$CommissioningStepEnumMap, e))
          .toList(),
      defaultParameters: json['defaultParameters'] as Map<String, dynamic>,
      chargerModel: json['chargerModel'] as String?,
      version: json['version'] as String?,
      isDefault: json['isDefault'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      createdBy: json['createdBy'] as String?,
    );

Map<String, dynamic> _$$CommissioningTemplateImplToJson(
        _$CommissioningTemplateImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'steps':
          instance.steps.map((e) => _$CommissioningStepEnumMap[e]!).toList(),
      'defaultParameters': instance.defaultParameters,
      'chargerModel': instance.chargerModel,
      'version': instance.version,
      'isDefault': instance.isDefault,
      'createdAt': instance.createdAt?.toIso8601String(),
      'createdBy': instance.createdBy,
    };

_$CommissioningSessionImpl _$$CommissioningSessionImplFromJson(
        Map<String, dynamic> json) =>
    _$CommissioningSessionImpl(
      sessionId: json['sessionId'] as String,
      chargerId: json['chargerId'] as String,
      connection: ChargerConnection.fromJson(
          json['connection'] as Map<String, dynamic>),
      chargerInfo: json['chargerInfo'] == null
          ? null
          : ChargerInfo.fromJson(json['chargerInfo'] as Map<String, dynamic>),
      networkConfig: json['networkConfig'] == null
          ? null
          : NetworkConfig.fromJson(
              json['networkConfig'] as Map<String, dynamic>),
      ocppCredentials: json['ocppCredentials'] == null
          ? null
          : OCPPCredentials.fromJson(
              json['ocppCredentials'] as Map<String, dynamic>),
      firmwareInfo: json['firmwareInfo'] == null
          ? null
          : FirmwareInfo.fromJson(json['firmwareInfo'] as Map<String, dynamic>),
      parameters: json['parameters'] as Map<String, dynamic>?,
      diagnostics: json['diagnostics'] as Map<String, dynamic>?,
      sessionStarted: json['sessionStarted'] == null
          ? null
          : DateTime.parse(json['sessionStarted'] as String),
      lastActivity: json['lastActivity'] == null
          ? null
          : DateTime.parse(json['lastActivity'] as String),
      isActive: json['isActive'] as bool?,
    );

Map<String, dynamic> _$$CommissioningSessionImplToJson(
        _$CommissioningSessionImpl instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'chargerId': instance.chargerId,
      'connection': instance.connection,
      'chargerInfo': instance.chargerInfo,
      'networkConfig': instance.networkConfig,
      'ocppCredentials': instance.ocppCredentials,
      'firmwareInfo': instance.firmwareInfo,
      'parameters': instance.parameters,
      'diagnostics': instance.diagnostics,
      'sessionStarted': instance.sessionStarted?.toIso8601String(),
      'lastActivity': instance.lastActivity?.toIso8601String(),
      'isActive': instance.isActive,
    };

_$CommissioningProgressImpl _$$CommissioningProgressImplFromJson(
        Map<String, dynamic> json) =>
    _$CommissioningProgressImpl(
      workflowId: json['workflowId'] as String,
      currentStep: $enumDecode(_$CommissioningStepEnumMap, json['currentStep']),
      progressPercentage: (json['progressPercentage'] as num).toDouble(),
      statusMessage: json['statusMessage'] as String,
      stepStatus: $enumDecodeNullable(_$StepStatusEnumMap, json['stepStatus']),
      errorMessage: json['errorMessage'] as String?,
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
      stepData: json['stepData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$CommissioningProgressImplToJson(
        _$CommissioningProgressImpl instance) =>
    <String, dynamic>{
      'workflowId': instance.workflowId,
      'currentStep': _$CommissioningStepEnumMap[instance.currentStep]!,
      'progressPercentage': instance.progressPercentage,
      'statusMessage': instance.statusMessage,
      'stepStatus': _$StepStatusEnumMap[instance.stepStatus],
      'errorMessage': instance.errorMessage,
      'timestamp': instance.timestamp?.toIso8601String(),
      'stepData': instance.stepData,
    };

_$CommissioningResultImpl _$$CommissioningResultImplFromJson(
        Map<String, dynamic> json) =>
    _$CommissioningResultImpl(
      workflowId: json['workflowId'] as String,
      chargerId: json['chargerId'] as String,
      isSuccessful: json['isSuccessful'] as bool,
      completedAt: DateTime.parse(json['completedAt'] as String),
      totalDuration:
          Duration(microseconds: (json['totalDuration'] as num).toInt()),
      completedSteps: (json['completedSteps'] as List<dynamic>?)
          ?.map((e) => WorkflowStep.fromJson(e as Map<String, dynamic>))
          .toList(),
      failedSteps: (json['failedSteps'] as List<dynamic>?)
          ?.map((e) => WorkflowStep.fromJson(e as Map<String, dynamic>))
          .toList(),
      finalConfiguration: json['finalConfiguration'] as Map<String, dynamic>?,
      diagnosticsReport: json['diagnosticsReport'] as Map<String, dynamic>?,
      complianceCertificates: (json['complianceCertificates'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      summary: json['summary'] as String?,
      recommendations: (json['recommendations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$CommissioningResultImplToJson(
        _$CommissioningResultImpl instance) =>
    <String, dynamic>{
      'workflowId': instance.workflowId,
      'chargerId': instance.chargerId,
      'isSuccessful': instance.isSuccessful,
      'completedAt': instance.completedAt.toIso8601String(),
      'totalDuration': instance.totalDuration.inMicroseconds,
      'completedSteps': instance.completedSteps,
      'failedSteps': instance.failedSteps,
      'finalConfiguration': instance.finalConfiguration,
      'diagnosticsReport': instance.diagnosticsReport,
      'complianceCertificates': instance.complianceCertificates,
      'summary': instance.summary,
      'recommendations': instance.recommendations,
    };
