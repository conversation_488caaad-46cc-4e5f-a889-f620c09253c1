import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

/// QR code scanner page for charger discovery
class QRScannerPage extends StatefulWidget {
  static const String routeName = '/commissioning/discovery/qr-scanner';

  const QRScannerPage({super.key});

  @override
  State<QRScannerPage> createState() => _QRScannerPageState();
}

class _QRScannerPageState extends State<QRScannerPage> {
  MobileScannerController cameraController = MobileScannerController();
  bool isScanning = true;
  String? scannedData;

  @override
  void dispose() {
    cameraController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Charger QR Code'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.flash_on),
            onPressed: () => cameraController.toggleTorch(),
          ),
          IconButton(
            icon: const Icon(Icons.camera_front),
            onPressed: () => cameraController.switchCamera(),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 4,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  MobileScanner(
                    controller: cameraController,
                    onDetect: _onQRCodeDetected,
                    fit: BoxFit.cover,
                  ),
                  _buildScannerOverlay(),
                  if (!isScanning)
                    Container(
                      color: Colors.black54,
                      child: const Center(
                        child: Text(
                          'Camera Paused',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          Expanded(flex: 1, child: _buildBottomSection()),
        ],
      ),
    );
  }

  Widget _buildScannerOverlay() {
    return Container(
      decoration: ShapeDecoration(
        shape: QrScannerOverlayShape(
          borderColor: Theme.of(context).primaryColor,
          borderRadius: 10,
          borderLength: 30,
          borderWidth: 4,
          cutOutSize: MediaQuery.of(context).size.width * 0.7,
          overlayColor: Colors.black54,
        ),
      ),
      child: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.7,
          height: MediaQuery.of(context).size.width * 0.7,
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).primaryColor, width: 2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Stack(
            children: [
              // Corner indicators
              Positioned(
                top: 0,
                left: 0,
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 4,
                      ),
                      left: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 4,
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 4,
                      ),
                      right: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 4,
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 4,
                      ),
                      left: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 4,
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 4,
                      ),
                      right: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 4,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (scannedData != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'QR Code Detected: ${scannedData!.substring(0, scannedData!.length > 30 ? 30 : scannedData!.length)}...',
                      style: const TextStyle(color: Colors.green),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _processScannedData,
                    child: const Text('Process QR Code'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: _resetScanner,
                    child: const Text('Scan Again'),
                  ),
                ),
              ],
            ),
          ] else ...[
            const Icon(Icons.qr_code_scanner, size: 48, color: Colors.grey),
            const SizedBox(height: 8),
            Text(
              'Position the QR code within the frame',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _enterManualSerial,
                    icon: const Icon(Icons.keyboard),
                    label: const Text('Manual Entry'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _simulateQRScan,
                    icon: const Icon(Icons.qr_code),
                    label: const Text('Demo QR'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _onQRCodeDetected(BarcodeCapture capture) {
    if (!isScanning) return;

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isNotEmpty) {
      final String? code = barcodes.first.rawValue;
      if (code != null && code.isNotEmpty) {
        setState(() {
          scannedData = code;
          isScanning = false;
        });
        cameraController.stop();
      }
    }
  }

  void _processScannedData() {
    if (scannedData == null) return;

    // Parse QR code data
    final qrData = _parseQRCode(scannedData!);

    if (qrData != null) {
      // Navigate to charger connection page with parsed data
      Navigator.pushReplacementNamed(
        context,
        '/commissioning/discovery/connect',
        arguments: qrData,
      );
    } else {
      _showErrorDialog(
        'Invalid QR Code',
        'The scanned QR code does not contain valid charger information.',
      );
    }
  }

  Map<String, String>? _parseQRCode(String qrData) {
    try {
      // Support multiple QR formats for better compatibility

      // Format 1: "EVSE:serial=ABC123;model=FastCharger;manufacturer=EVTech"
      if (qrData.startsWith('EVSE:')) {
        final data = qrData.substring(5); // Remove "EVSE:" prefix
        final pairs = data.split(';');
        final Map<String, String> result = {};

        for (final pair in pairs) {
          final keyValue = pair.split('=');
          if (keyValue.length == 2) {
            result[keyValue[0]] = keyValue[1];
          }
        }

        // Validate required fields
        if (result.containsKey('serial') && result['serial']!.isNotEmpty) {
          return result;
        }
      }

      // Format 2: Simple serial number (for demo purposes)
      if (qrData.length >= 8 && RegExp(r'^[A-Z0-9]+$').hasMatch(qrData)) {
        return {
          'serial': qrData,
          'model': 'Unknown Model',
          'manufacturer': 'Unknown Manufacturer',
          'source': 'qr_simple',
        };
      }

      // Format 3: JSON format
      if (qrData.startsWith('{') && qrData.endsWith('}')) {
        try {
          // For demo, create a simple parser
          if (qrData.contains('serial')) {
            return {
              'serial': 'DEMO001QR',
              'model': 'QR Demo Charger',
              'manufacturer': 'Demo Corp',
              'source': 'qr_json',
            };
          }
        } catch (e) {
          // Continue to next format
        }
      }

      // Format 4: Any text containing "EVSE" or "CHARGER" (for demo)
      if (qrData.toUpperCase().contains('EVSE') ||
          qrData.toUpperCase().contains('CHARGER')) {
        return {
          'serial': 'DEMO${DateTime.now().millisecondsSinceEpoch % 1000}',
          'model': 'Demo Charger',
          'manufacturer': 'Demo Manufacturer',
          'source': 'qr_demo',
          'originalData': qrData,
        };
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  void _resetScanner() {
    setState(() {
      scannedData = null;
      isScanning = true;
    });
    cameraController.start();
  }

  void _enterManualSerial() {
    Navigator.pushReplacementNamed(
      context,
      '/commissioning/discovery/manual-entry',
    );
  }

  void _simulateQRScan() {
    // Simulate scanning a demo QR code for testing
    final demoQRData =
        'EVSE:serial=DEMO001;model=FastCharger Pro;manufacturer=EVTech Solutions';
    setState(() {
      scannedData = demoQRData;
      isScanning = false;
    });
    cameraController.stop();

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Demo QR code scanned successfully!'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetScanner();
            },
            child: const Text('Try Again'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

/// Custom shape for QR scanner overlay
class QrScannerOverlayShape extends ShapeBorder {
  final Color borderColor;
  final double borderWidth;
  final Color overlayColor;
  final double borderRadius;
  final double borderLength;
  final double cutOutSize;

  const QrScannerOverlayShape({
    this.borderColor = Colors.red,
    this.borderWidth = 3.0,
    this.overlayColor = const Color.fromRGBO(0, 0, 0, 80),
    this.borderRadius = 0,
    this.borderLength = 40,
    this.cutOutSize = 250,
  });

  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..fillType = PathFillType.evenOdd
      ..addPath(getOuterPath(rect), Offset.zero);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    Path getLeftTopPath(Rect rect) {
      return Path()
        ..moveTo(rect.left, rect.bottom)
        ..lineTo(rect.left, rect.top + borderRadius)
        ..quadraticBezierTo(
          rect.left,
          rect.top,
          rect.left + borderRadius,
          rect.top,
        )
        ..lineTo(rect.right, rect.top);
    }

    return getLeftTopPath(rect)
      ..lineTo(rect.right, rect.bottom)
      ..lineTo(rect.left, rect.bottom)
      ..lineTo(rect.left, rect.top);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final width = rect.width;
    final height = rect.height;
    final cutOutWidth = cutOutSize < width ? cutOutSize : width - borderWidth;
    final cutOutHeight = cutOutSize < height
        ? cutOutSize
        : height - borderWidth;

    final backgroundPaint = Paint()
      ..color = overlayColor
      ..style = PaintingStyle.fill;

    final boxPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    final cutOutRect = Rect.fromLTWH(
      rect.left + (width - cutOutWidth) / 2 + borderWidth,
      rect.top + (height - cutOutHeight) / 2 + borderWidth,
      cutOutWidth - borderWidth * 2,
      cutOutHeight - borderWidth * 2,
    );

    canvas
      ..saveLayer(rect, backgroundPaint)
      ..drawRect(rect, backgroundPaint)
      ..drawRRect(
        RRect.fromRectAndCorners(
          cutOutRect,
          topLeft: Radius.circular(borderRadius),
          topRight: Radius.circular(borderRadius),
          bottomLeft: Radius.circular(borderRadius),
          bottomRight: Radius.circular(borderRadius),
        ),
        boxPaint..blendMode = BlendMode.clear,
      )
      ..restore();

    // Draw corner borders
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    final path = Path();

    // Top left corner
    path.moveTo(cutOutRect.left - borderLength, cutOutRect.top);
    path.lineTo(cutOutRect.left, cutOutRect.top);
    path.lineTo(cutOutRect.left, cutOutRect.top + borderLength);

    // Top right corner
    path.moveTo(cutOutRect.right + borderLength, cutOutRect.top);
    path.lineTo(cutOutRect.right, cutOutRect.top);
    path.lineTo(cutOutRect.right, cutOutRect.top + borderLength);

    // Bottom left corner
    path.moveTo(cutOutRect.left - borderLength, cutOutRect.bottom);
    path.lineTo(cutOutRect.left, cutOutRect.bottom);
    path.lineTo(cutOutRect.left, cutOutRect.bottom - borderLength);

    // Bottom right corner
    path.moveTo(cutOutRect.right + borderLength, cutOutRect.bottom);
    path.lineTo(cutOutRect.right, cutOutRect.bottom);
    path.lineTo(cutOutRect.right, cutOutRect.bottom - borderLength);

    canvas.drawPath(path, borderPaint);
  }

  @override
  ShapeBorder scale(double t) {
    return QrScannerOverlayShape(
      borderColor: borderColor,
      borderWidth: borderWidth,
      overlayColor: overlayColor,
    );
  }
}
