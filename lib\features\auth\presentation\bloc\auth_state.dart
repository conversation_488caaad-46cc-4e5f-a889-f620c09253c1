part of 'auth_bloc.dart';

/// Authentication status enumeration
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Authentication state
class AuthState extends Equatable {
  const AuthState({
    required this.status,
    this.user,
    this.tokens,
    this.error,
    this.message,
    this.isFirstLogin = false,
    this.biometricData,
    this.lastActivity,
  });

  /// Initial state
  const AuthState.initial()
      : status = AuthStatus.initial,
        user = null,
        tokens = null,
        error = null,
        message = null,
        isFirstLogin = false,
        biometricData = null,
        lastActivity = null;

  final AuthStatus status;
  final User? user;
  final AuthTokens? tokens;
  final String? error;
  final String? message;
  final bool isFirstLogin;
  final BiometricAuthData? biometricData;
  final DateTime? lastActivity;

  /// Check if user is authenticated
  bool get isAuthenticated => status == AuthStatus.authenticated && user != null;

  /// Check if authentication is in progress
  bool get isLoading => status == AuthStatus.loading;

  /// Check if there's an error
  bool get hasError => error != null;

  /// Check if there's a message
  bool get hasMessage => message != null;

  /// Check if tokens are expired or expiring soon
  bool get needsTokenRefresh => tokens?.isExpiringSoon ?? false;

  /// Check if session is valid
  bool get isSessionValid => isAuthenticated && !needsTokenRefresh;

  /// Get user role
  UserRole? get userRole => user?.role;

  /// Check if user has specific permission
  bool hasPermission(String permission) => user?.hasPermission(permission) ?? false;

  /// Check if user can commission chargers
  bool get canCommissionChargers => user?.canCommissionChargers ?? false;

  /// Check if user can manage other users
  bool get canManageUsers => user?.canManageUsers ?? false;

  /// Check if user can view reports
  bool get canViewReports => user?.canViewReports ?? false;

  /// Check if user can generate reports
  bool get canGenerateReports => user?.canGenerateReports ?? false;

  /// Copy state with new values
  AuthState copyWith({
    AuthStatus? status,
    User? user,
    AuthTokens? tokens,
    String? error,
    String? message,
    bool? isFirstLogin,
    BiometricAuthData? biometricData,
    DateTime? lastActivity,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      tokens: tokens ?? this.tokens,
      error: error,
      message: message,
      isFirstLogin: isFirstLogin ?? this.isFirstLogin,
      biometricData: biometricData ?? this.biometricData,
      lastActivity: lastActivity ?? this.lastActivity,
    );
  }

  /// Clear error and message
  AuthState clearMessages() {
    return copyWith(
      error: null,
      message: null,
    );
  }

  @override
  List<Object?> get props => [
        status,
        user,
        tokens,
        error,
        message,
        isFirstLogin,
        biometricData,
        lastActivity,
      ];

  @override
  String toString() {
    return 'AuthState(status: $status, user: ${user?.email}, hasTokens: ${tokens != null}, error: $error)';
  }
}
