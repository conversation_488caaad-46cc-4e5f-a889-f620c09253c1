// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'network_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WifiNetworkImpl _$$WifiNetworkImplFromJson(Map<String, dynamic> json) =>
    _$WifiNetworkImpl(
      ssid: json['ssid'] as String,
      bssid: json['bssid'] as String,
      signalStrength: (json['signalStrength'] as num).toInt(),
      securityType: $enumDecode(_$SecurityTypeEnumMap, json['securityType']),
      frequency: (json['frequency'] as num).toInt(),
      isHidden: json['isHidden'] as bool?,
      capabilities: json['capabilities'] as String?,
    );

Map<String, dynamic> _$$WifiNetworkImplToJson(_$WifiNetworkImpl instance) =>
    <String, dynamic>{
      'ssid': instance.ssid,
      'bssid': instance.bssid,
      'signalStrength': instance.signalStrength,
      'securityType': _$SecurityTypeEnumMap[instance.securityType]!,
      'frequency': instance.frequency,
      'isHidden': instance.isHidden,
      'capabilities': instance.capabilities,
    };

const _$SecurityTypeEnumMap = {
  SecurityType.none: 'none',
  SecurityType.wep: 'wep',
  SecurityType.wpa: 'wpa',
  SecurityType.wpa2: 'wpa2',
  SecurityType.wpa3: 'wpa3',
  SecurityType.enterprise: 'enterprise',
};

_$NetworkConfigImpl _$$NetworkConfigImplFromJson(Map<String, dynamic> json) =>
    _$NetworkConfigImpl(
      ssid: json['ssid'] as String,
      password: json['password'] as String,
      securityType: $enumDecode(_$SecurityTypeEnumMap, json['securityType']),
      ipAddress: json['ipAddress'] as String?,
      gateway: json['gateway'] as String?,
      subnetMask: json['subnetMask'] as String?,
      primaryDns: json['primaryDns'] as String?,
      secondaryDns: json['secondaryDns'] as String?,
      isDhcp: json['isDhcp'] as bool?,
      mtu: (json['mtu'] as num?)?.toInt(),
      advancedSettings: json['advancedSettings'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$NetworkConfigImplToJson(_$NetworkConfigImpl instance) =>
    <String, dynamic>{
      'ssid': instance.ssid,
      'password': instance.password,
      'securityType': _$SecurityTypeEnumMap[instance.securityType]!,
      'ipAddress': instance.ipAddress,
      'gateway': instance.gateway,
      'subnetMask': instance.subnetMask,
      'primaryDns': instance.primaryDns,
      'secondaryDns': instance.secondaryDns,
      'isDhcp': instance.isDhcp,
      'mtu': instance.mtu,
      'advancedSettings': instance.advancedSettings,
    };

_$NetworkStatusImpl _$$NetworkStatusImplFromJson(Map<String, dynamic> json) =>
    _$NetworkStatusImpl(
      isConnected: json['isConnected'] as bool,
      hasInternet: json['hasInternet'] as bool,
      connectedSsid: json['connectedSsid'] as String?,
      ipAddress: json['ipAddress'] as String?,
      gateway: json['gateway'] as String?,
      signalStrength: (json['signalStrength'] as num?)?.toInt(),
      linkSpeed: (json['linkSpeed'] as num?)?.toDouble(),
      connectedAt: json['connectedAt'] == null
          ? null
          : DateTime.parse(json['connectedAt'] as String),
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$$NetworkStatusImplToJson(_$NetworkStatusImpl instance) =>
    <String, dynamic>{
      'isConnected': instance.isConnected,
      'hasInternet': instance.hasInternet,
      'connectedSsid': instance.connectedSsid,
      'ipAddress': instance.ipAddress,
      'gateway': instance.gateway,
      'signalStrength': instance.signalStrength,
      'linkSpeed': instance.linkSpeed,
      'connectedAt': instance.connectedAt?.toIso8601String(),
      'errorMessage': instance.errorMessage,
    };

_$GridConfigImpl _$$GridConfigImplFromJson(Map<String, dynamic> json) =>
    _$GridConfigImpl(
      maxCurrent: (json['maxCurrent'] as num).toDouble(),
      maxPower: (json['maxPower'] as num).toDouble(),
      voltage: (json['voltage'] as num).toDouble(),
      phases: (json['phases'] as num).toInt(),
      frequency: (json['frequency'] as num?)?.toDouble(),
      powerFactor: (json['powerFactor'] as num?)?.toDouble(),
      isThreePhase: json['isThreePhase'] as bool?,
      localRegulations: json['localRegulations'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$GridConfigImplToJson(_$GridConfigImpl instance) =>
    <String, dynamic>{
      'maxCurrent': instance.maxCurrent,
      'maxPower': instance.maxPower,
      'voltage': instance.voltage,
      'phases': instance.phases,
      'frequency': instance.frequency,
      'powerFactor': instance.powerFactor,
      'isThreePhase': instance.isThreePhase,
      'localRegulations': instance.localRegulations,
    };
