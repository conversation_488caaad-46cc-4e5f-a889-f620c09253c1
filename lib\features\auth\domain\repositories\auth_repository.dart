import '../../../../core/utils/typedef.dart';
import '../entities/user.dart';

/// Abstract repository for authentication operations
abstract class AuthRepository {
  /// Login with email and password
  FutureResult<AuthTokens> login(LoginCredentials credentials);

  /// Logout current user
  FutureResult<void> logout();

  /// Refresh authentication tokens
  FutureResult<AuthTokens> refreshTokens(String refreshToken);

  /// Get current user profile
  FutureResult<User> getCurrentUser();

  /// Update user profile
  FutureResult<User> updateProfile(User user);

  /// Change password
  FutureResult<void> changePassword(String currentPassword, String newPassword);

  /// Request password reset
  FutureResult<void> requestPasswordReset(String email);

  /// Reset password with token
  FutureResult<void> resetPassword(String token, String newPassword);

  /// Check if user is authenticated
  FutureResult<bool> isAuthenticated();

  /// Get stored authentication tokens
  FutureResult<AuthTokens?> getStoredTokens();

  /// Store authentication tokens
  FutureResult<void> storeTokens(AuthTokens tokens);

  /// Clear stored authentication data
  FutureResult<void> clearAuthData();

  /// Enable biometric authentication
  FutureResult<void> enableBiometricAuth();

  /// Disable biometric authentication
  FutureResult<void> disableBiometricAuth();

  /// Check if biometric authentication is available
  FutureResult<BiometricAuthData> getBiometricAuthData();

  /// Authenticate with biometrics
  FutureResult<bool> authenticateWithBiometrics();

  /// Verify current session
  FutureResult<bool> verifySession();

  /// Get user permissions
  FutureResult<List<String>> getUserPermissions();

  /// Check specific permission
  FutureResult<bool> hasPermission(String permission);

  /// Update last activity timestamp
  FutureResult<void> updateLastActivity();

  /// Check session timeout
  FutureResult<bool> isSessionExpired();

  /// Extend session
  FutureResult<void> extendSession();
}
