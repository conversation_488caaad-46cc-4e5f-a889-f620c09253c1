import 'package:freezed_annotation/freezed_annotation.dart';

part 'network_config.freezed.dart';
part 'network_config.g.dart';

/// Represents Wi-Fi security types
enum SecurityType {
  @JsonValue('none')
  none,
  @JsonValue('wep')
  wep,
  @JsonValue('wpa')
  wpa,
  @JsonValue('wpa2')
  wpa2,
  @JsonValue('wpa3')
  wpa3,
  @JsonValue('enterprise')
  enterprise,
}

/// Represents a discovered Wi-Fi network
@freezed
class WifiNetwork with _$WifiNetwork {
  const factory WifiNetwork({
    required String ssid,
    required String bssid,
    required int signalStrength,
    required SecurityType securityType,
    required int frequency,
    bool? isHidden,
    String? capabilities,
  }) = _WifiNetwork;

  factory WifiNetwork.fromJson(Map<String, dynamic> json) =>
      _$WifiNetworkFromJson(json);
}

/// Represents network configuration for a charging station
@freezed
class NetworkConfig with _$NetworkConfig {
  const factory NetworkConfig({
    required String ssid,
    required String password,
    required SecurityType securityType,
    String? ipAddress,
    String? gateway,
    String? subnetMask,
    String? primaryDns,
    String? secondaryDns,
    bool? isDhcp,
    int? mtu,
    Map<String, dynamic>? advancedSettings,
  }) = _NetworkConfig;

  factory NetworkConfig.fromJson(Map<String, dynamic> json) =>
      _$NetworkConfigFromJson(json);
}

/// Represents network connectivity status
@freezed
class NetworkStatus with _$NetworkStatus {
  const factory NetworkStatus({
    required bool isConnected,
    required bool hasInternet,
    String? connectedSsid,
    String? ipAddress,
    String? gateway,
    int? signalStrength,
    double? linkSpeed,
    DateTime? connectedAt,
    String? errorMessage,
  }) = _NetworkStatus;

  factory NetworkStatus.fromJson(Map<String, dynamic> json) =>
      _$NetworkStatusFromJson(json);
}

/// Represents grid configuration parameters
@freezed
class GridConfig with _$GridConfig {
  const factory GridConfig({
    required double maxCurrent,
    required double maxPower,
    required double voltage,
    required int phases,
    double? frequency,
    double? powerFactor,
    bool? isThreePhase,
    Map<String, dynamic>? localRegulations,
  }) = _GridConfig;

  factory GridConfig.fromJson(Map<String, dynamic> json) =>
      _$GridConfigFromJson(json);
}
