import '../error/failures.dart';

/// Common type definitions used throughout the application

/// Result type for operations that can fail
class Result<T> {
  const Result.success(this.data) : failure = null;
  const Result.failure(this.failure) : data = null;

  final T? data;
  final Failure? failure;

  bool get isSuccess => failure == null;
  bool get isFailure => failure != null;
}

/// Future result type for async operations
typedef FutureResult<T> = Future<Result<T>>;

/// JSON map type
typedef JsonMap = Map<String, dynamic>;

/// List of JSON maps
typedef JsonList = List<JsonMap>;

/// Callback function type
typedef VoidCallback = void Function();

/// Callback with parameter
typedef Callback<T> = void Function(T value);

/// Async callback
typedef AsyncCallback = Future<void> Function();

/// Async callback with parameter
typedef AsyncCallbackWithParam<T> = Future<void> Function(T value);

/// Progress callback for long-running operations
typedef ProgressCallback = void Function(double progress);

/// Error callback
typedef ErrorCallback = void Function(String error);

/// Success callback
typedef SuccessCallback = void Function();

/// Validation function type
typedef Validator<T> = String? Function(T? value);

/// Transformer function type
typedef Transformer<T, R> = R Function(T input);

/// Predicate function type
typedef Predicate<T> = bool Function(T value);

/// Comparator function type
typedef Comparator<T> = int Function(T a, T b);

/// Stream transformer type
typedef StreamTransformer<T, R> = Stream<R> Function(Stream<T> stream);

/// Event handler type
typedef EventHandler<T> = void Function(T event);

/// Factory function type
typedef Factory<T> = T Function();

/// Builder function type
typedef Builder<T> = T Function();

/// Disposer function type
typedef Disposer = void Function();
