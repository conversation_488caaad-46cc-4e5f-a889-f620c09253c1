import 'package:flutter/material.dart';
import '../../data/services/ocpp_websocket_client.dart';

/// OCPP Integration page for charger protocol setup
class OCPPIntegrationPage extends StatefulWidget {
  static const String routeName = '/commissioning/ocpp';

  const OCPPIntegrationPage({super.key});

  @override
  State<OCPPIntegrationPage> createState() => _OCPPIntegrationPageState();
}

class _OCPPIntegrationPageState extends State<OCPPIntegrationPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _centralSystemUrlController = TextEditingController();
  final _chargePointIdController = TextEditingController();
  final _authKeyController = TextEditingController();
  final _heartbeatIntervalController = TextEditingController();

  // Configuration state
  bool _isConnecting = false;
  bool _isConnected = false;
  String? _connectionStatus;
  String _selectedVersion = 'OCPP 1.6';
  bool _useWebSocket = true;
  bool _useTLS = true;

  // OCPP WebSocket client
  late OCPPWebSocketClient _ocppClient;
  final List<Map<String, dynamic>> _messages = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeDefaults();
    _initializeOCPPClient();
  }

  void _initializeOCPPClient() {
    _ocppClient = OCPPWebSocketClient();

    // Listen to connection state changes
    _ocppClient.stateStream.listen((state) {
      if (mounted) {
        setState(() {
          _isConnected = state == OCPPConnectionState.connected;
          _isConnecting = state == OCPPConnectionState.connecting;

          switch (state) {
            case OCPPConnectionState.connected:
              _connectionStatus = 'Connected to Central System';
              break;
            case OCPPConnectionState.connecting:
              _connectionStatus = 'Connecting...';
              break;
            case OCPPConnectionState.disconnected:
              _connectionStatus = 'Disconnected';
              break;
            case OCPPConnectionState.error:
              _connectionStatus = 'Connection error';
              break;
          }
        });
      }
    });

    // Listen to messages
    _ocppClient.messageStream.listen((message) {
      if (mounted) {
        setState(() {
          _messages.insert(0, message);
          // Keep only last 50 messages
          if (_messages.length > 50) {
            _messages.removeRange(50, _messages.length);
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _centralSystemUrlController.dispose();
    _chargePointIdController.dispose();
    _authKeyController.dispose();
    _heartbeatIntervalController.dispose();
    _ocppClient.dispose();
    super.dispose();
  }

  void _initializeDefaults() {
    _centralSystemUrlController.text = 'wss://cms.example.com/ocpp';
    _chargePointIdController.text = 'CP001';
    _heartbeatIntervalController.text = '300';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('OCPP Integration'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.settings), text: 'Configuration'),
            Tab(icon: Icon(Icons.link), text: 'Connection'),
            Tab(icon: Icon(Icons.message), text: 'Messages'),
          ],
        ),
        actions: [
          if (_isConnected)
            const Padding(
              padding: EdgeInsets.all(16),
              child: Icon(Icons.check_circle, color: Colors.green),
            ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildConfigurationTab(),
          _buildConnectionTab(),
          _buildMessagesTab(),
        ],
      ),
    );
  }

  Widget _buildConfigurationTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: ListView(
          children: [
            // OCPP Version Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'OCPP Version',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedVersion,
                      decoration: InputDecoration(
                        labelText: 'Protocol Version',
                        prefixIcon: const Icon(Icons.code),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: 'OCPP 1.6',
                          child: Text('OCPP 1.6'),
                        ),
                        DropdownMenuItem(
                          value: 'OCPP 2.0.1',
                          child: Text('OCPP 2.0.1'),
                        ),
                      ],
                      onChanged: (value) =>
                          setState(() => _selectedVersion = value!),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Central System Configuration
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Central System',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _centralSystemUrlController,
                      decoration: InputDecoration(
                        labelText: 'Central System URL',
                        hintText: 'wss://cms.example.com/ocpp',
                        prefixIcon: const Icon(Icons.cloud),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Central System URL is required';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _chargePointIdController,
                      decoration: InputDecoration(
                        labelText: 'Charge Point ID',
                        hintText: 'CP001',
                        prefixIcon: const Icon(Icons.ev_station),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Charge Point ID is required';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _authKeyController,
                      decoration: InputDecoration(
                        labelText: 'Authentication Key (Optional)',
                        hintText: 'Enter authentication key',
                        prefixIcon: const Icon(Icons.key),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      obscureText: true,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Protocol Settings
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Protocol Settings',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('Use WebSocket'),
                      subtitle: const Text('Enable WebSocket communication'),
                      value: _useWebSocket,
                      onChanged: (value) =>
                          setState(() => _useWebSocket = value),
                    ),
                    SwitchListTile(
                      title: const Text('Use TLS/SSL'),
                      subtitle: const Text('Enable secure communication'),
                      value: _useTLS,
                      onChanged: (value) => setState(() => _useTLS = value),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _heartbeatIntervalController,
                      decoration: InputDecoration(
                        labelText: 'Heartbeat Interval (seconds)',
                        hintText: '300',
                        prefixIcon: const Icon(Icons.favorite),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Heartbeat interval is required';
                        }
                        final interval = int.tryParse(value);
                        if (interval == null || interval < 30) {
                          return 'Interval must be at least 30 seconds';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _fillDemoData,
                    icon: const Icon(Icons.science),
                    label: const Text('Demo Config'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testConfiguration,
                    icon: const Icon(Icons.check),
                    label: const Text('Test Config'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Connection Status Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _isConnected
                            ? Icons.check_circle
                            : Icons.radio_button_unchecked,
                        color: _isConnected ? Colors.green : Colors.grey,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Connection Status',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _isConnected
                        ? 'Connected to Central System'
                        : 'Not Connected',
                    style: TextStyle(
                      color: _isConnected ? Colors.green : Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                  if (_connectionStatus != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      _connectionStatus!,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Connection Actions
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isConnecting
                  ? null
                  : (_isConnected ? _disconnect : _connect),
              child: _isConnecting
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 12),
                        Text('Connecting...'),
                      ],
                    )
                  : Text(
                      _isConnected ? 'Disconnect' : 'Connect to Central System',
                    ),
            ),
          ),
          const SizedBox(height: 16),

          // Connection Details
          if (_isConnected) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Connection Details',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildDetailRow('Protocol', _selectedVersion),
                    _buildDetailRow('URL', _centralSystemUrlController.text),
                    _buildDetailRow(
                      'Charge Point ID',
                      _chargePointIdController.text,
                    ),
                    _buildDetailRow(
                      'Transport',
                      _useWebSocket ? 'WebSocket' : 'HTTP',
                    ),
                    _buildDetailRow('Security', _useTLS ? 'TLS/SSL' : 'None'),
                    _buildDetailRow(
                      'Heartbeat',
                      '${_heartbeatIntervalController.text}s',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(color: Colors.grey[600])),
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'OCPP Messages',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _messages.isEmpty
                ? const Center(
                    child: Text(
                      'No messages yet. Connect to see OCPP messages.',
                      style: TextStyle(color: Colors.grey),
                    ),
                  )
                : ListView.builder(
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      final message = _messages[index];
                      return _buildRealMessageCard(message);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildRealMessageCard(Map<String, dynamic> message) {
    final type = message['type'] as String;
    final action = message['action'] as String? ?? 'Unknown';
    final timestamp = message['timestamp'] as String? ?? '';
    final messageId = message['messageId'] as String? ?? '';

    IconData icon;
    Color color;
    String direction;
    String description;

    switch (type) {
      case 'sent':
        icon = _getActionIcon(action);
        color = Colors.blue;
        direction = 'Sent';
        description = 'Message ID: $messageId';
        break;
      case 'received':
        icon = _getActionIcon(action);
        color = Colors.green;
        direction = 'Received';
        description = 'Message ID: $messageId';
        break;
      case 'result':
        icon = Icons.check_circle;
        color = Colors.green;
        direction = 'Result';
        description = 'Response received';
        break;
      case 'error':
        icon = Icons.error;
        color = Colors.red;
        direction = 'Error';
        description = message['message'] as String? ?? 'Unknown error';
        break;
      default:
        icon = Icons.info;
        color = Colors.grey;
        direction = 'Info';
        description = message['message'] as String? ?? '';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(action),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(description),
            if (timestamp.isNotEmpty)
              Text(
                DateTime.parse(timestamp).toString().substring(11, 19),
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            direction,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  IconData _getActionIcon(String action) {
    switch (action) {
      case 'BootNotification':
        return Icons.power_settings_new;
      case 'Heartbeat':
        return Icons.favorite;
      case 'StatusNotification':
        return Icons.info;
      case 'Authorize':
        return Icons.verified_user;
      case 'StartTransaction':
      case 'RemoteStartTransaction':
        return Icons.play_arrow;
      case 'StopTransaction':
      case 'RemoteStopTransaction':
        return Icons.stop;
      case 'ChangeConfiguration':
        return Icons.settings;
      case 'GetConfiguration':
        return Icons.list;
      case 'Reset':
        return Icons.restart_alt;
      default:
        return Icons.message;
    }
  }

  void _fillDemoData() {
    setState(() {
      _centralSystemUrlController.text = 'wss://demo-cms.evtech.com/ocpp';
      _chargePointIdController.text = 'DEMO_CP_001';
      _authKeyController.text = 'demo_auth_key_123';
      _heartbeatIntervalController.text = '300';
      _selectedVersion = 'OCPP 1.6';
      _useWebSocket = true;
      _useTLS = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Demo configuration loaded successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _testConfiguration() {
    if (!_formKey.currentState!.validate()) return;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Configuration validated successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Future<void> _connect() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final success = await _ocppClient.connect(
        centralSystemUrl: _centralSystemUrlController.text,
        chargePointId: _chargePointIdController.text,
        authKey: _authKeyController.text.isNotEmpty
            ? _authKeyController.text
            : null,
        heartbeatInterval:
            int.tryParse(_heartbeatIntervalController.text) ?? 300,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Successfully connected to Central System!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to next step after successful connection
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.pushNamed(context, '/commissioning/parameters');
          }
        });
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to connect to Central System'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connection error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _disconnect() {
    _ocppClient.disconnect();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Disconnected from Central System')),
    );
  }
}
