/// Base exception class for the application
abstract class AppException implements Exception {
  const AppException({
    required this.message,
    this.code,
    this.details,
  });

  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Server-related exceptions
class ServerException extends AppException {
  const ServerException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Authentication exceptions
class AuthException extends AppException {
  const AuthException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Cache/Storage exceptions
class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Validation exceptions
class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Permission exceptions
class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Device/Hardware exceptions
class DeviceException extends AppException {
  const DeviceException({
    required super.message,
    super.code,
    super.details,
  });
}

/// OCPP Protocol exceptions
class OcppException extends AppException {
  const OcppException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Charger-specific exceptions
class ChargerException extends AppException {
  const ChargerException({
    required super.message,
    super.code,
    super.details,
  });
}

/// File operation exceptions
class FileException extends AppException {
  const FileException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Biometric authentication exceptions
class BiometricException extends AppException {
  const BiometricException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Network discovery exceptions
class NetworkDiscoveryException extends AppException {
  const NetworkDiscoveryException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Firmware update exceptions
class FirmwareException extends AppException {
  const FirmwareException({
    required super.message,
    super.code,
    super.details,
  });
}

/// Report generation exceptions
class ReportException extends AppException {
  const ReportException({
    required super.message,
    super.code,
    super.details,
  });
}
