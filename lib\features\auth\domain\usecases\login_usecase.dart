import '../../../../core/utils/typedef.dart';
import '../../../../core/error/failures.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Use case for user login
class LoginUseCase {
  LoginUseCase(this._repository);

  final AuthRepository _repository;

  /// Execute login with credentials
  FutureResult<LoginResult> call(LoginCredentials credentials) async {
    // Validate credentials
    final validationResult = _validateCredentials(credentials);
    if (validationResult.isFailure) {
      return Result.failure(validationResult.failure!);
    }

    // Attempt login
    final loginResult = await _repository.login(credentials);
    if (loginResult.isFailure) {
      return Result.failure(loginResult.failure!);
    }

    final tokens = loginResult.data!;

    // Store tokens if login successful
    final storeResult = await _repository.storeTokens(tokens);
    if (storeResult.isFailure) {
      return Result.failure(storeResult.failure!);
    }

    // Get user profile
    final userResult = await _repository.getCurrentUser();
    if (userResult.isFailure) {
      return Result.failure(userResult.failure!);
    }

    final user = userResult.data!;

    // Update last activity
    await _repository.updateLastActivity();

    return Result.success(
      LoginResult(
        user: user,
        tokens: tokens,
        isFirstLogin: user.lastLoginAt == null,
      ),
    );
  }

  /// Validate login credentials
  Result<void> _validateCredentials(LoginCredentials credentials) {
    if (credentials.email.isEmpty) {
      return const Result.failure(
        ValidationFailure(message: 'Email is required', code: 'EMAIL_REQUIRED'),
      );
    }

    if (!_isValidEmail(credentials.email)) {
      return const Result.failure(
        ValidationFailure(
          message: 'Please enter a valid email address',
          code: 'INVALID_EMAIL',
        ),
      );
    }

    if (credentials.password.isEmpty) {
      return const Result.failure(
        ValidationFailure(
          message: 'Password is required',
          code: 'PASSWORD_REQUIRED',
        ),
      );
    }

    if (credentials.password.length < 6) {
      return const Result.failure(
        ValidationFailure(
          message: 'Password must be at least 6 characters long',
          code: 'PASSWORD_TOO_SHORT',
        ),
      );
    }

    return const Result.success(null);
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }
}

/// Result of login operation
class LoginResult {
  LoginResult({
    required this.user,
    required this.tokens,
    required this.isFirstLogin,
  });

  final User user;
  final AuthTokens tokens;
  final bool isFirstLogin;
}

/// Validation failure class
class ValidationFailure extends Failure {
  const ValidationFailure({required super.message, super.code, super.details});
}
