import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/charger_info.dart';

part 'charger_info_model.g.dart';

/// Data model for Connector with JSON serialization
@JsonSerializable(fieldRename: FieldRename.snake)
class ConnectorModel {
  final String id;
  final ChargerType type;
  final double maxPower;
  final double maxCurrent;
  final ChargerStatus status;
  final String? currentTransactionId;
  final DateTime? lastUsed;

  const ConnectorModel({
    required this.id,
    required this.type,
    required this.maxPower,
    required this.maxCurrent,
    required this.status,
    this.currentTransactionId,
    this.lastUsed,
  });

  factory ConnectorModel.fromJson(Map<String, dynamic> json) =>
      _$ConnectorModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConnectorModelToJson(this);

  /// Convert to domain entity
  Connector toEntity() => Connector(
        id: id,
        type: type,
        maxPower: maxPower,
        maxCurrent: maxCurrent,
        status: status,
        currentTransactionId: currentTransactionId,
        lastUsed: lastUsed,
      );

  /// Create from domain entity
  factory ConnectorModel.fromEntity(Connector connector) => ConnectorModel(
        id: connector.id,
        type: connector.type,
        maxPower: connector.maxPower,
        maxCurrent: connector.maxCurrent,
        status: connector.status,
        currentTransactionId: connector.currentTransactionId,
        lastUsed: connector.lastUsed,
      );
}

/// Data model for ChargerInfo with JSON serialization
@JsonSerializable(fieldRename: FieldRename.snake)
class ChargerInfoModel {
  final String id;
  final String serialNumber;
  final String model;
  final String manufacturer;
  final String firmwareVersion;
  final List<ConnectorModel> connectors;
  final ChargerStatus status;
  final DateTime lastSeen;
  final String? location;
  final String? description;
  final double? latitude;
  final double? longitude;
  final Map<String, dynamic>? metadata;
  @JsonKey(includeFromJson: true, includeToJson: false)
  final DateTime? createdAt;
  @JsonKey(includeFromJson: true, includeToJson: false)
  final DateTime? updatedAt;

  const ChargerInfoModel({
    required this.id,
    required this.serialNumber,
    required this.model,
    required this.manufacturer,
    required this.firmwareVersion,
    required this.connectors,
    required this.status,
    required this.lastSeen,
    this.location,
    this.description,
    this.latitude,
    this.longitude,
    this.metadata,
    this.createdAt,
    this.updatedAt,
  });

  factory ChargerInfoModel.fromJson(Map<String, dynamic> json) =>
      _$ChargerInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChargerInfoModelToJson(this);

  /// Convert to domain entity
  ChargerInfo toEntity() => ChargerInfo(
        id: id,
        serialNumber: serialNumber,
        model: model,
        manufacturer: manufacturer,
        firmwareVersion: firmwareVersion,
        connectors: connectors.map((c) => c.toEntity()).toList(),
        status: status,
        lastSeen: lastSeen,
        location: location,
        description: description,
        latitude: latitude,
        longitude: longitude,
        metadata: metadata,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );

  /// Create from domain entity
  factory ChargerInfoModel.fromEntity(ChargerInfo chargerInfo) =>
      ChargerInfoModel(
        id: chargerInfo.id,
        serialNumber: chargerInfo.serialNumber,
        model: chargerInfo.model,
        manufacturer: chargerInfo.manufacturer,
        firmwareVersion: chargerInfo.firmwareVersion,
        connectors: chargerInfo.connectors
            .map((c) => ConnectorModel.fromEntity(c))
            .toList(),
        status: chargerInfo.status,
        lastSeen: chargerInfo.lastSeen,
        location: chargerInfo.location,
        description: chargerInfo.description,
        latitude: chargerInfo.latitude,
        longitude: chargerInfo.longitude,
        metadata: chargerInfo.metadata,
        createdAt: chargerInfo.createdAt,
        updatedAt: chargerInfo.updatedAt,
      );
}

/// Data model for DiscoveredCharger with JSON serialization
@JsonSerializable(fieldRename: FieldRename.snake)
class DiscoveredChargerModel {
  final String id;
  final String name;
  final String serialNumber;
  final int signalStrength;
  final String connectionType;
  final String? macAddress;
  final String? ipAddress;
  final Map<String, dynamic>? advertisementData;
  final DateTime? discoveredAt;

  const DiscoveredChargerModel({
    required this.id,
    required this.name,
    required this.serialNumber,
    required this.signalStrength,
    required this.connectionType,
    this.macAddress,
    this.ipAddress,
    this.advertisementData,
    this.discoveredAt,
  });

  factory DiscoveredChargerModel.fromJson(Map<String, dynamic> json) =>
      _$DiscoveredChargerModelFromJson(json);

  Map<String, dynamic> toJson() => _$DiscoveredChargerModelToJson(this);

  /// Convert to domain entity
  DiscoveredCharger toEntity() => DiscoveredCharger(
        id: id,
        name: name,
        serialNumber: serialNumber,
        signalStrength: signalStrength,
        connectionType: connectionType,
        macAddress: macAddress,
        ipAddress: ipAddress,
        advertisementData: advertisementData,
        discoveredAt: discoveredAt,
      );

  /// Create from domain entity
  factory DiscoveredChargerModel.fromEntity(DiscoveredCharger charger) =>
      DiscoveredChargerModel(
        id: charger.id,
        name: charger.name,
        serialNumber: charger.serialNumber,
        signalStrength: charger.signalStrength,
        connectionType: charger.connectionType,
        macAddress: charger.macAddress,
        ipAddress: charger.ipAddress,
        advertisementData: charger.advertisementData,
        discoveredAt: charger.discoveredAt,
      );
}
