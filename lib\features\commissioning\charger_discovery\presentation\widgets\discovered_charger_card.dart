import 'package:flutter/material.dart';
import '../../../shared/domain/entities/charger_info.dart';

/// Card widget for displaying discovered chargers
class DiscoveredChargerCard extends StatelessWidget {
  final DiscoveredCharger charger;
  final VoidCallback onConnect;

  const DiscoveredChargerCard({
    super.key,
    required this.charger,
    required this.onConnect,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onConnect,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: 12),
              _buildDetails(context),
              const SizedBox(height: 12),
              _buildConnectionInfo(context),
              const SizedBox(height: 16),
              _buildActionButton(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getConnectionColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getConnectionIcon(),
            color: _getConnectionColor(),
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                charger.name,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                'Serial: ${charger.serialNumber}',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        _buildSignalStrengthIndicator(context),
      ],
    );
  }

  Widget _buildDetails(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildDetailItem(
              context,
              'Connection',
              charger.connectionType.toUpperCase(),
              Icons.link,
            ),
          ),
          if (charger.macAddress != null)
            Expanded(
              child: _buildDetailItem(
                context,
                'MAC Address',
                _formatMacAddress(charger.macAddress!),
                Icons.device_hub,
              ),
            ),
          if (charger.ipAddress != null)
            Expanded(
              child: _buildDetailItem(
                context,
                'IP Address',
                charger.ipAddress!,
                Icons.computer,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildConnectionInfo(BuildContext context) {
    return Row(
      children: [
        Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          'Discovered ${_formatDiscoveryTime()}',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
        const Spacer(),
        _buildConnectionTypeChip(context),
      ],
    );
  }

  Widget _buildSignalStrengthIndicator(BuildContext context) {
    final strength = _getSignalStrength();
    final color = _getSignalColor(strength);

    return Column(
      children: [
        Icon(_getSignalIcon(strength), color: color, size: 20),
        const SizedBox(height: 2),
        Text(
          '${charger.signalStrength} dBm',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildConnectionTypeChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getConnectionColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getConnectionColor().withValues(alpha: 0.3)),
      ),
      child: Text(
        charger.connectionType.toUpperCase(),
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getConnectionColor(),
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onConnect,
        icon: const Icon(Icons.link),
        label: const Text('Connect to Charger'),
        style: ElevatedButton.styleFrom(
          backgroundColor: _getConnectionColor(),
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  IconData _getConnectionIcon() {
    switch (charger.connectionType.toLowerCase()) {
      case 'bluetooth':
        return Icons.bluetooth;
      case 'wifi':
        return Icons.wifi;
      case 'ethernet':
        return Icons.cable;
      default:
        return Icons.device_hub;
    }
  }

  Color _getConnectionColor() {
    switch (charger.connectionType.toLowerCase()) {
      case 'bluetooth':
        return Colors.blue;
      case 'wifi':
        return Colors.green;
      case 'ethernet':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  double _getSignalStrength() {
    // Convert dBm to percentage (rough approximation)
    final dbm = charger.signalStrength.abs();
    if (dbm <= 30) return 1.0; // Excellent
    if (dbm <= 50) return 0.8; // Good
    if (dbm <= 70) return 0.6; // Fair
    if (dbm <= 85) return 0.4; // Poor
    return 0.2; // Very poor
  }

  Color _getSignalColor(double strength) {
    if (strength >= 0.8) return Colors.green;
    if (strength >= 0.6) return Colors.orange;
    return Colors.red;
  }

  IconData _getSignalIcon(double strength) {
    if (strength >= 0.8) return Icons.signal_cellular_4_bar;
    if (strength >= 0.6) return Icons.signal_cellular_alt;
    if (strength >= 0.4) return Icons.signal_cellular_alt;
    if (strength >= 0.2) return Icons.signal_cellular_alt;
    return Icons.signal_cellular_null;
  }

  String _formatMacAddress(String mac) {
    if (mac.length >= 12) {
      return '${mac.substring(0, 2)}:${mac.substring(2, 4)}:${mac.substring(4, 6)}:...';
    }
    return mac;
  }

  String _formatDiscoveryTime() {
    if (charger.discoveredAt == null) return 'Unknown';

    final now = DateTime.now();
    final difference = now.difference(charger.discoveredAt!);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
