part of 'auth_bloc.dart';

/// Base class for authentication events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event to request login with credentials
class AuthLoginRequested extends AuthEvent {
  const AuthLoginRequested(this.credentials);

  final LoginCredentials credentials;

  @override
  List<Object?> get props => [credentials];
}

/// Event to request logout
class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested();
}

/// Event to check current authentication status
class AuthStatusChecked extends AuthEvent {
  const AuthStatusChecked();
}

/// Event to refresh authentication tokens
class AuthTokenRefreshRequested extends AuthEvent {
  const AuthTokenRefreshRequested();
}

/// Event to request biometric login
class AuthBiometricLoginRequested extends AuthEvent {
  const AuthBiometricLoginRequested();
}

/// Event to extend current session
class AuthSessionExtended extends AuthEvent {
  const AuthSessionExtended();
}

/// Event to change password
class AuthPasswordChangeRequested extends AuthEvent {
  const AuthPasswordChangeRequested({
    required this.currentPassword,
    required this.newPassword,
  });

  final String currentPassword;
  final String newPassword;

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

/// Event to request password reset
class AuthPasswordResetRequested extends AuthEvent {
  const AuthPasswordResetRequested(this.email);

  final String email;

  @override
  List<Object?> get props => [email];
}

/// Event to reset password with token
class AuthPasswordResetConfirmed extends AuthEvent {
  const AuthPasswordResetConfirmed({
    required this.token,
    required this.newPassword,
  });

  final String token;
  final String newPassword;

  @override
  List<Object?> get props => [token, newPassword];
}

/// Event to enable biometric authentication
class AuthBiometricEnabled extends AuthEvent {
  const AuthBiometricEnabled();
}

/// Event to disable biometric authentication
class AuthBiometricDisabled extends AuthEvent {
  const AuthBiometricDisabled();
}

/// Event to update user profile
class AuthProfileUpdateRequested extends AuthEvent {
  const AuthProfileUpdateRequested(this.user);

  final User user;

  @override
  List<Object?> get props => [user];
}

/// Event to check session timeout
class AuthSessionTimeoutChecked extends AuthEvent {
  const AuthSessionTimeoutChecked();
}

/// Event to handle session timeout
class AuthSessionTimedOut extends AuthEvent {
  const AuthSessionTimedOut();
}

/// Event to verify current session
class AuthSessionVerificationRequested extends AuthEvent {
  const AuthSessionVerificationRequested();
}
