import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Comprehensive network configuration page for EV charger commissioning
class NetworkConfigurationPage extends StatefulWidget {
  static const String routeName = '/commissioning/configuration/network';

  const NetworkConfigurationPage({super.key});

  @override
  State<NetworkConfigurationPage> createState() => _NetworkConfigurationPageState();
}

class _NetworkConfigurationPageState extends State<NetworkConfigurationPage> {
  final _formKey = GlobalKey<FormState>();
  
  // WiFi Configuration
  final _wifiSsidController = TextEditingController();
  final _wifiPasswordController = TextEditingController();
  bool _wifiPasswordVisible = false;
  
  // GSM Configuration
  final _apnController = TextEditingController();
  final _gsmUsernameController = TextEditingController();
  final _gsmPasswordController = TextEditingController();
  bool _gsmPasswordVisible = false;
  
  // Ethernet Configuration
  String _ethernetMode = 'DHCP'; // 'DHCP' or 'Static'
  final _staticIpController = TextEditingController();
  final _subnetMaskController = TextEditingController();
  final _gatewayController = TextEditingController();
  final _dnsController = TextEditingController();
  
  bool _isLoading = false;
  Map<String, dynamic>? _chargerData;

  @override
  void initState() {
    super.initState();
    // Pre-fill some demo values
    _apnController.text = 'internet';
    _subnetMaskController.text = '*************';
    _dnsController.text = '*******';
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    _chargerData = arguments?['chargerData'];
  }

  @override
  void dispose() {
    _wifiSsidController.dispose();
    _wifiPasswordController.dispose();
    _apnController.dispose();
    _gsmUsernameController.dispose();
    _gsmPasswordController.dispose();
    _staticIpController.dispose();
    _subnetMaskController.dispose();
    _gatewayController.dispose();
    _dnsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Network Configuration'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildChargerInfoCard(),
              const SizedBox(height: 24),
              _buildWiFiConfigurationSection(),
              const SizedBox(height: 24),
              _buildGSMConfigurationSection(),
              const SizedBox(height: 24),
              _buildEthernetConfigurationSection(),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildChargerInfoCard() {
    final chargerName = _chargerData?['name'] ?? 'Unknown Charger';
    final chargerSerial = _chargerData?['serial'] ?? 'Unknown Serial';

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.router, size: 32, color: Theme.of(context).primaryColor),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Network Configuration',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Configuring: $chargerName ($chargerSerial)',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWiFiConfigurationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.wifi, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'WiFi Configuration',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _wifiSsidController,
              decoration: const InputDecoration(
                labelText: 'WiFi Network Name (SSID)',
                hintText: 'Enter WiFi network name',
                prefixIcon: Icon(Icons.wifi),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter WiFi network name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _wifiPasswordController,
              obscureText: !_wifiPasswordVisible,
              decoration: InputDecoration(
                labelText: 'WiFi Password',
                hintText: 'Enter WiFi password',
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(_wifiPasswordVisible ? Icons.visibility : Icons.visibility_off),
                  onPressed: () {
                    setState(() {
                      _wifiPasswordVisible = !_wifiPasswordVisible;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter WiFi password';
                }
                if (value.length < 8) {
                  return 'Password must be at least 8 characters';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGSMConfigurationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.signal_cellular_alt, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'GSM Configuration',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _apnController,
              decoration: const InputDecoration(
                labelText: 'APN Name',
                hintText: 'Enter APN name (e.g., internet)',
                prefixIcon: Icon(Icons.network_cell),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter APN name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _gsmUsernameController,
              decoration: const InputDecoration(
                labelText: 'GSM Username (Optional)',
                hintText: 'Enter GSM username if required',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _gsmPasswordController,
              obscureText: !_gsmPasswordVisible,
              decoration: InputDecoration(
                labelText: 'GSM Password (Optional)',
                hintText: 'Enter GSM password if required',
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(_gsmPasswordVisible ? Icons.visibility : Icons.visibility_off),
                  onPressed: () {
                    setState(() {
                      _gsmPasswordVisible = !_gsmPasswordVisible;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEthernetConfigurationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cable, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Ethernet Configuration',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'IP Configuration Mode',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('DHCP'),
                    subtitle: const Text('Automatic IP assignment'),
                    value: 'DHCP',
                    groupValue: _ethernetMode,
                    onChanged: (value) {
                      setState(() {
                        _ethernetMode = value!;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Static IP'),
                    subtitle: const Text('Manual IP configuration'),
                    value: 'Static',
                    groupValue: _ethernetMode,
                    onChanged: (value) {
                      setState(() {
                        _ethernetMode = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
            if (_ethernetMode == 'Static') ...[
              const SizedBox(height: 16),
              TextFormField(
                controller: _staticIpController,
                decoration: const InputDecoration(
                  labelText: 'Static IP Address',
                  hintText: 'e.g., ***********00',
                  prefixIcon: Icon(Icons.computer),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                ],
                validator: _ethernetMode == 'Static' ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter static IP address';
                  }
                  if (!_isValidIP(value)) {
                    return 'Please enter a valid IP address';
                  }
                  return null;
                } : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _subnetMaskController,
                decoration: const InputDecoration(
                  labelText: 'Subnet Mask',
                  hintText: 'e.g., *************',
                  prefixIcon: Icon(Icons.network_check),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                ],
                validator: _ethernetMode == 'Static' ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter subnet mask';
                  }
                  if (!_isValidIP(value)) {
                    return 'Please enter a valid subnet mask';
                  }
                  return null;
                } : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _gatewayController,
                decoration: const InputDecoration(
                  labelText: 'Gateway',
                  hintText: 'e.g., ***********',
                  prefixIcon: Icon(Icons.router),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                ],
                validator: _ethernetMode == 'Static' ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter gateway address';
                  }
                  if (!_isValidIP(value)) {
                    return 'Please enter a valid gateway address';
                  }
                  return null;
                } : null,
              ),
            ],
            const SizedBox(height: 16),
            TextFormField(
              controller: _dnsController,
              decoration: const InputDecoration(
                labelText: 'DNS Server',
                hintText: 'e.g., *******',
                prefixIcon: Icon(Icons.dns),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
              ],
              validator: (value) {
                if (value != null && value.isNotEmpty && !_isValidIP(value)) {
                  return 'Please enter a valid DNS server address';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isLoading ? null : _skipConfiguration,
              child: const Text('Skip'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveAndNext,
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Save & Next'),
            ),
          ),
        ],
      ),
    );
  }

  bool _isValidIP(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return false;

    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }

    return true;
  }

  void _skipConfiguration() {
    Navigator.pushReplacementNamed(
      context,
      '/commissioning/configuration/charger',
      arguments: {'chargerData': _chargerData},
    );
  }

  Future<void> _saveAndNext() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate saving configuration
      await Future.delayed(const Duration(seconds: 2));

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Network configuration saved successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to next step
        Navigator.pushReplacementNamed(
          context,
          '/commissioning/configuration/charger',
          arguments: {
            'chargerData': _chargerData,
            'networkConfig': {
              'wifi': {
                'ssid': _wifiSsidController.text,
                'password': _wifiPasswordController.text,
              },
              'gsm': {
                'apn': _apnController.text,
                'username': _gsmUsernameController.text,
                'password': _gsmPasswordController.text,
              },
              'ethernet': {
                'mode': _ethernetMode,
                'staticIp': _staticIpController.text,
                'subnetMask': _subnetMaskController.text,
                'gateway': _gatewayController.text,
                'dns': _dnsController.text,
              },
            },
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
