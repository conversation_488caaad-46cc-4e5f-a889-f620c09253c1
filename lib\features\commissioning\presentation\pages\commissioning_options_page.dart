import 'package:flutter/material.dart';

/// Commissioning options screen showing 4 main commissioning options
class CommissioningOptionsPage extends StatelessWidget {
  static const String routeName = '/commissioning/options';

  const CommissioningOptionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final chargerData = arguments?['chargerData'] as Map<String, dynamic>?;
    final chargerName = chargerData?['name'] ?? 'Unknown Charger';
    final chargerSerial = chargerData?['serial'] ?? 'Unknown Serial';

    return Scaffold(
      appBar: AppBar(
        title: const Text('Commissioning Options'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildChargerInfoCard(context, chargerName, chargerSerial),
            const SizedBox(height: 24),
            _buildInstructionCard(context),
            const SizedBox(height: 24),
            _buildCommissioningOptions(context, chargerData),
          ],
        ),
      ),
    );
  }

  Widget _buildChargerInfoCard(BuildContext context, String name, String serial) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.ev_station,
                size: 32,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Serial: $serial',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Connected',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.green[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionCard(BuildContext context) {
    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.blue[700], size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Select a commissioning option to configure your EV charger. Each option provides specific functionality for device setup and management.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.blue[700],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommissioningOptions(BuildContext context, Map<String, dynamic>? chargerData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Commissioning Options',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.1,
          children: [
            _buildOptionCard(
              context,
              'Configuration Settings',
              'Configure network, charger, and charging point settings',
              Icons.settings,
              Colors.blue,
              () => _navigateToConfiguration(context, chargerData),
            ),
            _buildOptionCard(
              context,
              'OTA Firmware Upgrade',
              'Update charger firmware to latest version',
              Icons.system_update,
              Colors.orange,
              () => _navigateToFirmwareUpgrade(context, chargerData),
            ),
            _buildOptionCard(
              context,
              'Upload Certificates',
              'Install security certificates for secure communication',
              Icons.security,
              Colors.green,
              () => _navigateToUploadCertificates(context, chargerData),
            ),
            _buildOptionCard(
              context,
              'Delete Certificates',
              'Remove existing certificates from the charger',
              Icons.delete_forever,
              Colors.red,
              () => _navigateToDeleteCertificates(context, chargerData),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOptionCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 3,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 32, color: color),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToConfiguration(BuildContext context, Map<String, dynamic>? chargerData) {
    Navigator.pushNamed(
      context,
      '/commissioning/configuration/network',
      arguments: {'chargerData': chargerData},
    );
  }

  void _navigateToFirmwareUpgrade(BuildContext context, Map<String, dynamic>? chargerData) {
    Navigator.pushNamed(
      context,
      '/commissioning/firmware-upgrade',
      arguments: {'chargerData': chargerData},
    );
  }

  void _navigateToUploadCertificates(BuildContext context, Map<String, dynamic>? chargerData) {
    Navigator.pushNamed(
      context,
      '/commissioning/certificates/upload',
      arguments: {'chargerData': chargerData},
    );
  }

  void _navigateToDeleteCertificates(BuildContext context, Map<String, dynamic>? chargerData) {
    Navigator.pushNamed(
      context,
      '/commissioning/certificates/delete',
      arguments: {'chargerData': chargerData},
    );
  }
}
