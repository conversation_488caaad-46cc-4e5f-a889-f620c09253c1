import 'package:flutter/material.dart';

/// Certificate deletion page for EV charger commissioning
class DeleteCertificatesPage extends StatefulWidget {
  static const String routeName = '/commissioning/certificates/delete';

  const DeleteCertificatesPage({super.key});

  @override
  State<DeleteCertificatesPage> createState() => _DeleteCertificatesPageState();
}

class _DeleteCertificatesPageState extends State<DeleteCertificatesPage> {
  Map<String, dynamic>? _chargerData;
  final List<InstalledCertificate> _installedCertificates = [];
  final Set<String> _selectedCertificates = {};
  bool _isDeleting = false;

  @override
  void initState() {
    super.initState();
    _loadInstalledCertificates();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    _chargerData = arguments?['chargerData'];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Delete Certificates'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          if (_selectedCertificates.isNotEmpty)
            IconButton(
              onPressed: _isDeleting ? null : _deleteSelectedCertificates,
              icon: const Icon(Icons.delete),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildChargerInfoCard(),
            const SizedBox(height: 24),
            _buildWarningCard(),
            const SizedBox(height: 24),
            _buildInstalledCertificatesSection(),
            if (_isDeleting) ...[
              const SizedBox(height: 24),
              _buildDeletionProgressSection(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChargerInfoCard() {
    final chargerName = _chargerData?['name'] ?? 'Unknown Charger';
    final chargerSerial = _chargerData?['serial'] ?? 'Unknown Serial';

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.delete_forever, size: 32, color: Colors.red),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Certificate Deletion',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Device: $chargerName ($chargerSerial)',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarningCard() {
    return Card(
      color: Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.red[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'Warning: Certificate Deletion',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '• Deleting certificates may disrupt secure communication\n'
              '• Ensure you have backup certificates before deletion\n'
              '• The charger may become inaccessible after deletion\n'
              '• This action cannot be undone',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.red[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstalledCertificatesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.verified_user, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Installed Certificates (${_installedCertificates.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_installedCertificates.isNotEmpty)
                  TextButton(
                    onPressed: _toggleSelectAll,
                    child: Text(_selectedCertificates.length == _installedCertificates.length
                        ? 'Deselect All'
                        : 'Select All'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (_installedCertificates.isEmpty)
              Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Icon(Icons.verified_user, size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'No certificates installed',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'There are no certificates currently installed on this charger.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _installedCertificates.length,
                separatorBuilder: (context, index) => const SizedBox(height: 8),
                itemBuilder: (context, index) {
                  final cert = _installedCertificates[index];
                  return _buildCertificateCard(cert);
                },
              ),
            if (_selectedCertificates.isNotEmpty) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isDeleting ? null : _deleteSelectedCertificates,
                  icon: const Icon(Icons.delete_forever),
                  label: Text('Delete Selected (${_selectedCertificates.length})'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCertificateCard(InstalledCertificate cert) {
    final isSelected = _selectedCertificates.contains(cert.id);
    
    return Card(
      elevation: 1,
      color: isSelected ? Colors.red[50] : null,
      child: CheckboxListTile(
        value: isSelected,
        onChanged: _isDeleting ? null : (selected) {
          setState(() {
            if (selected == true) {
              _selectedCertificates.add(cert.id);
            } else {
              _selectedCertificates.remove(cert.id);
            }
          });
        },
        title: Text(
          cert.name,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('Type: ${cert.type}'),
            Text('Expires: ${cert.expiryDate}'),
            Text('Issuer: ${cert.issuer}'),
          ],
        ),
        secondary: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getCertificateStatusColor(cert.status).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getCertificateStatusIcon(cert.status),
            color: _getCertificateStatusColor(cert.status),
          ),
        ),
        activeColor: Colors.red,
      ),
    );
  }

  Widget _buildDeletionProgressSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.delete_forever, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Deleting Certificates...',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const LinearProgressIndicator(),
            const SizedBox(height: 8),
            const Text('Please wait while certificates are being deleted...'),
          ],
        ),
      ),
    );
  }

  void _loadInstalledCertificates() {
    // Simulate loading installed certificates
    setState(() {
      _installedCertificates.addAll([
        InstalledCertificate(
          id: 'cert_1',
          name: 'Client Certificate',
          type: 'Client Authentication',
          expiryDate: '2025-12-31',
          issuer: 'EV Charging CA',
          status: CertificateStatus.valid,
        ),
        InstalledCertificate(
          id: 'cert_2',
          name: 'Root CA Certificate',
          type: 'Root Certificate Authority',
          expiryDate: '2030-01-15',
          issuer: 'Trusted Root CA',
          status: CertificateStatus.valid,
        ),
        InstalledCertificate(
          id: 'cert_3',
          name: 'Server Certificate',
          type: 'TLS Server Authentication',
          expiryDate: '2024-06-30',
          issuer: 'Server CA',
          status: CertificateStatus.expiringSoon,
        ),
      ]);
    });
  }

  void _toggleSelectAll() {
    setState(() {
      if (_selectedCertificates.length == _installedCertificates.length) {
        _selectedCertificates.clear();
      } else {
        _selectedCertificates.addAll(_installedCertificates.map((cert) => cert.id));
      }
    });
  }

  Future<void> _deleteSelectedCertificates() async {
    if (_selectedCertificates.isEmpty) return;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: Text(
          'Are you sure you want to delete ${_selectedCertificates.length} certificate(s)? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isDeleting = true;
    });

    try {
      // Simulate deletion process
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _installedCertificates.removeWhere((cert) => _selectedCertificates.contains(cert.id));
        _selectedCertificates.clear();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Certificates deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete certificates: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });
      }
    }
  }

  Color _getCertificateStatusColor(CertificateStatus status) {
    switch (status) {
      case CertificateStatus.valid:
        return Colors.green;
      case CertificateStatus.expiringSoon:
        return Colors.orange;
      case CertificateStatus.expired:
        return Colors.red;
    }
  }

  IconData _getCertificateStatusIcon(CertificateStatus status) {
    switch (status) {
      case CertificateStatus.valid:
        return Icons.check_circle;
      case CertificateStatus.expiringSoon:
        return Icons.warning;
      case CertificateStatus.expired:
        return Icons.error;
    }
  }
}

class InstalledCertificate {
  final String id;
  final String name;
  final String type;
  final String expiryDate;
  final String issuer;
  final CertificateStatus status;

  InstalledCertificate({
    required this.id,
    required this.name,
    required this.type,
    required this.expiryDate,
    required this.issuer,
    required this.status,
  });
}

enum CertificateStatus {
  valid,
  expiringSoon,
  expired,
}
