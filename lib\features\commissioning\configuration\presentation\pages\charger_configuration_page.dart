import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Charger configuration page for power settings, control modes, and hardware configuration
class ChargerConfigurationPage extends StatefulWidget {
  static const String routeName = '/commissioning/configuration/charger';

  const ChargerConfigurationPage({super.key});

  @override
  State<ChargerConfigurationPage> createState() => _ChargerConfigurationPageState();
}

class _ChargerConfigurationPageState extends State<ChargerConfigurationPage> {
  final _formKey = GlobalKey<FormState>();
  
  // Power Settings
  final _maxPowerController = TextEditingController();
  String _powerUnit = 'kW';
  
  // Control Mode
  String _controlMode = 'Standard Mode';
  
  // Hardware Configuration
  String _chargerType = 'Single Gun';
  bool _ac2TypeEnabled = true;
  final _gun1MaxCurrentController = TextEditingController();
  final _gun2MaxCurrentController = TextEditingController();
  
  // Authentication Settings
  String _authenticationType = 'User Authentication';
  final _serverTimeoutController = TextEditingController();
  
  bool _isLoading = false;
  Map<String, dynamic>? _chargerData;
  Map<String, dynamic>? _networkConfig;

  @override
  void initState() {
    super.initState();
    // Pre-fill some demo values
    _maxPowerController.text = '60';
    _gun1MaxCurrentController.text = '200';
    _gun2MaxCurrentController.text = '200';
    _serverTimeoutController.text = '0';
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    _chargerData = arguments?['chargerData'];
    _networkConfig = arguments?['networkConfig'];
  }

  @override
  void dispose() {
    _maxPowerController.dispose();
    _gun1MaxCurrentController.dispose();
    _gun2MaxCurrentController.dispose();
    _serverTimeoutController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Charger Configuration'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildChargerInfoCard(),
              const SizedBox(height: 24),
              _buildPowerSettingsSection(),
              const SizedBox(height: 24),
              _buildControlModeSection(),
              const SizedBox(height: 24),
              _buildHardwareConfigurationSection(),
              const SizedBox(height: 24),
              _buildAuthenticationSettingsSection(),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildChargerInfoCard() {
    final chargerName = _chargerData?['name'] ?? 'Unknown Charger';
    final chargerSerial = _chargerData?['serial'] ?? 'Unknown Serial';

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.tune, size: 32, color: Theme.of(context).primaryColor),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Charger Configuration',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Configuring: $chargerName ($chargerSerial)',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPowerSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Power Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: TextFormField(
                    controller: _maxPowerController,
                    decoration: const InputDecoration(
                      labelText: 'Max Power Capacity',
                      hintText: 'Enter maximum power',
                      prefixIcon: Icon(Icons.electric_bolt),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter max power capacity';
                      }
                      final power = double.tryParse(value);
                      if (power == null || power <= 0) {
                        return 'Please enter a valid power value';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _powerUnit,
                    decoration: const InputDecoration(
                      labelText: 'Unit',
                      border: OutlineInputBorder(),
                    ),
                    items: ['kW', 'MW'].map((unit) {
                      return DropdownMenuItem(value: unit, child: Text(unit));
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _powerUnit = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlModeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.control_camera, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Control Mode Selection',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            RadioListTile<String>(
              title: const Text('Standard Mode'),
              subtitle: const Text('Basic charging control with standard features'),
              value: 'Standard Mode',
              groupValue: _controlMode,
              onChanged: (value) {
                setState(() {
                  _controlMode = value!;
                });
              },
            ),
            RadioListTile<String>(
              title: const Text('Dynamic Mode'),
              subtitle: const Text('Advanced control with dynamic load balancing'),
              value: 'Dynamic Mode',
              groupValue: _controlMode,
              onChanged: (value) {
                setState(() {
                  _controlMode = value!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHardwareConfigurationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.hardware, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Hardware Configuration',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Charger Type',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Single Gun'),
                    subtitle: const Text('One charging connector'),
                    value: 'Single Gun',
                    groupValue: _chargerType,
                    onChanged: (value) {
                      setState(() {
                        _chargerType = value!;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Dual Gun'),
                    subtitle: const Text('Two charging connectors'),
                    value: 'Dual Gun',
                    groupValue: _chargerType,
                    onChanged: (value) {
                      setState(() {
                        _chargerType = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('AC Type 2 Features'),
              subtitle: const Text('Enable AC Type 2 connector features'),
              value: _ac2TypeEnabled,
              onChanged: (value) {
                setState(() {
                  _ac2TypeEnabled = value;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _gun1MaxCurrentController,
              decoration: const InputDecoration(
                labelText: 'Gun 1 Max Current Limit (A)',
                hintText: 'e.g., 200',
                prefixIcon: Icon(Icons.electrical_services),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter Gun 1 max current limit';
                }
                final current = int.tryParse(value);
                if (current == null || current <= 0) {
                  return 'Please enter a valid current value';
                }
                return null;
              },
            ),
            if (_chargerType == 'Dual Gun') ...[
              const SizedBox(height: 16),
              TextFormField(
                controller: _gun2MaxCurrentController,
                decoration: const InputDecoration(
                  labelText: 'Gun 2 Max Current Limit (A)',
                  hintText: 'e.g., 200',
                  prefixIcon: Icon(Icons.electrical_services),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                validator: _chargerType == 'Dual Gun' ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter Gun 2 max current limit';
                  }
                  final current = int.tryParse(value);
                  if (current == null || current <= 0) {
                    return 'Please enter a valid current value';
                  }
                  return null;
                } : null,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAuthenticationSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.security, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Authentication Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Authentication Type',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            RadioListTile<String>(
              title: const Text('User Authentication'),
              subtitle: const Text('Require user authentication to start charging'),
              value: 'User Authentication',
              groupValue: _authenticationType,
              onChanged: (value) {
                setState(() {
                  _authenticationType = value!;
                });
              },
            ),
            RadioListTile<String>(
              title: const Text('Plug & Charge'),
              subtitle: const Text('Automatic authentication when vehicle is connected'),
              value: 'Plug & Charge',
              groupValue: _authenticationType,
              onChanged: (value) {
                setState(() {
                  _authenticationType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _serverTimeoutController,
              decoration: const InputDecoration(
                labelText: 'Server Disconnection Timeout (minutes)',
                hintText: 'e.g., 0 for no timeout',
                prefixIcon: Icon(Icons.timer),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter server timeout value';
                }
                final timeout = int.tryParse(value);
                if (timeout == null || timeout < 0) {
                  return 'Please enter a valid timeout value (0 or greater)';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isLoading ? null : _skipConfiguration,
              child: const Text('Skip'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveAndNext,
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Save & Next'),
            ),
          ),
        ],
      ),
    );
  }

  void _skipConfiguration() {
    Navigator.pushReplacementNamed(
      context,
      '/commissioning/configuration/charging-point',
      arguments: {
        'chargerData': _chargerData,
        'networkConfig': _networkConfig,
      },
    );
  }

  Future<void> _saveAndNext() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate saving configuration
      await Future.delayed(const Duration(seconds: 2));

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Charger configuration saved successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to next step
        Navigator.pushReplacementNamed(
          context,
          '/commissioning/configuration/charging-point',
          arguments: {
            'chargerData': _chargerData,
            'networkConfig': _networkConfig,
            'chargerConfig': {
              'maxPower': _maxPowerController.text,
              'powerUnit': _powerUnit,
              'controlMode': _controlMode,
              'chargerType': _chargerType,
              'ac2TypeEnabled': _ac2TypeEnabled,
              'gun1MaxCurrent': _gun1MaxCurrentController.text,
              'gun2MaxCurrent': _gun2MaxCurrentController.text,
              'authenticationType': _authenticationType,
              'serverTimeout': _serverTimeoutController.text,
            },
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
