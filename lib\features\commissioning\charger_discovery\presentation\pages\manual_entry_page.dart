import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../shared/constants/commissioning_constants.dart';

/// Manual serial number entry page for charger discovery
class ManualEntryPage extends StatefulWidget {
  static const String routeName = '/commissioning/discovery/manual-entry';

  const ManualEntryPage({super.key});

  @override
  State<ManualEntryPage> createState() => _ManualEntryPageState();
}

class _ManualEntryPageState extends State<ManualEntryPage> {
  final _formKey = GlobalKey<FormState>();
  final _serialController = TextEditingController();
  final _modelController = TextEditingController();
  final _manufacturerController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _serialController.dispose();
    _modelController.dispose();
    _manufacturerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enter Charger Details'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInstructionCard(),
              const SizedBox(height: 24),
              _buildSerialNumberField(),
              const SizedBox(height: 16),
              _buildOptionalFieldsSection(),
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInstructionCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  'Manual Entry',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Enter the charger\'s serial number to begin the discovery process. You can find this information on the charger\'s label or documentation.',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.blue.shade600),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSerialNumberField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Serial Number *',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _serialController,
          decoration: InputDecoration(
            hintText: 'Enter charger serial number',
            prefixIcon: const Icon(Icons.qr_code),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            suffixIcon: IconButton(
              icon: const Icon(Icons.qr_code_scanner),
              onPressed: _scanQRCode,
              tooltip: 'Scan QR Code',
            ),
          ),
          textCapitalization: TextCapitalization.characters,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9]')),
            LengthLimitingTextInputFormatter(20),
          ],
          validator: _validateSerialNumber,
          onChanged: (_) => setState(() {}),
        ),
        const SizedBox(height: 8),
        Text(
          'Format: 8-20 alphanumeric characters (e.g., EVSE001ABC)',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildOptionalFieldsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Optional Information',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Text(
          'Providing additional details can help with charger identification',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _modelController,
          decoration: InputDecoration(
            labelText: 'Model',
            hintText: 'e.g., FastCharger Pro',
            prefixIcon: const Icon(Icons.device_hub),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          textCapitalization: TextCapitalization.words,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _manufacturerController,
          decoration: InputDecoration(
            labelText: 'Manufacturer',
            hintText: 'e.g., EVTech Solutions',
            prefixIcon: const Icon(Icons.business),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          textCapitalization: TextCapitalization.words,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading || !_isFormValid()
                ? null
                : _proceedWithDiscovery,
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Start Discovery'),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _scanQRCode,
                icon: const Icon(Icons.qr_code_scanner),
                label: const Text('Scan QR Code'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _fillDemoData,
                icon: const Icon(Icons.science),
                label: const Text('Demo Data'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String? _validateSerialNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Serial number is required';
    }

    final regex = RegExp(
      CommissioningConstants.validationPatterns['serial_number']!,
    );
    if (!regex.hasMatch(value)) {
      return 'Invalid format. Use 8-20 alphanumeric characters';
    }

    return null;
  }

  bool _isFormValid() {
    return _serialController.text.isNotEmpty &&
        _validateSerialNumber(_serialController.text) == null;
  }

  void _scanQRCode() {
    Navigator.pushReplacementNamed(
      context,
      '/commissioning/discovery/qr-scanner',
    );
  }

  void _fillDemoData() {
    setState(() {
      _serialController.text =
          'DEMO${DateTime.now().millisecondsSinceEpoch % 1000}';
      _modelController.text = 'FastCharger Pro Demo';
      _manufacturerController.text = 'EVTech Solutions';
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Demo data filled successfully!'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  Future<void> _proceedWithDiscovery() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Simulate discovery process
      await Future.delayed(const Duration(seconds: 2));

      // Create charger data from manual entry
      final chargerData = {
        'serial': _serialController.text.trim(),
        'model': _modelController.text.trim().isNotEmpty
            ? _modelController.text.trim()
            : 'Unknown Model',
        'manufacturer': _manufacturerController.text.trim().isNotEmpty
            ? _manufacturerController.text.trim()
            : 'Unknown Manufacturer',
        'source': 'manual_entry',
      };

      if (mounted) {
        Navigator.pushReplacementNamed(
          context,
          '/commissioning/discovery/connect',
          arguments: chargerData,
        );
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog(
          'Discovery Failed',
          'Failed to start discovery process. Please try again.',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying serial number format examples
class SerialNumberExamplesWidget extends StatelessWidget {
  const SerialNumberExamplesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Serial Number Examples',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildExample('EVSE001ABC', 'Standard format'),
            _buildExample('CHG2023001', 'Year-based format'),
            _buildExample('AC22KW001', 'Power-based format'),
            _buildExample('DC50KW001', 'DC fast charger format'),
          ],
        ),
      ),
    );
  }

  Widget _buildExample(String serial, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              serial,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(description),
        ],
      ),
    );
  }
}
