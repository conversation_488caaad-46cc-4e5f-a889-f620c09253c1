// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'firmware_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FirmwareInfo _$FirmwareInfoFromJson(Map<String, dynamic> json) {
  return _FirmwareInfo.fromJson(json);
}

/// @nodoc
mixin _$FirmwareInfo {
  String get version => throw _privateConstructorUsedError;
  String get downloadUrl => throw _privateConstructorUsedError;
  String get checksum => throw _privateConstructorUsedError;
  String get checksumAlgorithm => throw _privateConstructorUsedError;
  int get fileSize => throw _privateConstructorUsedError;
  DateTime get releaseDate => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get releaseNotes => throw _privateConstructorUsedError;
  bool? get isSecurityUpdate => throw _privateConstructorUsedError;
  bool? get isCritical => throw _privateConstructorUsedError;
  List<String>? get compatibleModels => throw _privateConstructorUsedError;
  String? get minimumVersion => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this FirmwareInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FirmwareInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FirmwareInfoCopyWith<FirmwareInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FirmwareInfoCopyWith<$Res> {
  factory $FirmwareInfoCopyWith(
          FirmwareInfo value, $Res Function(FirmwareInfo) then) =
      _$FirmwareInfoCopyWithImpl<$Res, FirmwareInfo>;
  @useResult
  $Res call(
      {String version,
      String downloadUrl,
      String checksum,
      String checksumAlgorithm,
      int fileSize,
      DateTime releaseDate,
      String? description,
      String? releaseNotes,
      bool? isSecurityUpdate,
      bool? isCritical,
      List<String>? compatibleModels,
      String? minimumVersion,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$FirmwareInfoCopyWithImpl<$Res, $Val extends FirmwareInfo>
    implements $FirmwareInfoCopyWith<$Res> {
  _$FirmwareInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FirmwareInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? downloadUrl = null,
    Object? checksum = null,
    Object? checksumAlgorithm = null,
    Object? fileSize = null,
    Object? releaseDate = null,
    Object? description = freezed,
    Object? releaseNotes = freezed,
    Object? isSecurityUpdate = freezed,
    Object? isCritical = freezed,
    Object? compatibleModels = freezed,
    Object? minimumVersion = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      checksum: null == checksum
          ? _value.checksum
          : checksum // ignore: cast_nullable_to_non_nullable
              as String,
      checksumAlgorithm: null == checksumAlgorithm
          ? _value.checksumAlgorithm
          : checksumAlgorithm // ignore: cast_nullable_to_non_nullable
              as String,
      fileSize: null == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int,
      releaseDate: null == releaseDate
          ? _value.releaseDate
          : releaseDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      releaseNotes: freezed == releaseNotes
          ? _value.releaseNotes
          : releaseNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      isSecurityUpdate: freezed == isSecurityUpdate
          ? _value.isSecurityUpdate
          : isSecurityUpdate // ignore: cast_nullable_to_non_nullable
              as bool?,
      isCritical: freezed == isCritical
          ? _value.isCritical
          : isCritical // ignore: cast_nullable_to_non_nullable
              as bool?,
      compatibleModels: freezed == compatibleModels
          ? _value.compatibleModels
          : compatibleModels // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      minimumVersion: freezed == minimumVersion
          ? _value.minimumVersion
          : minimumVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FirmwareInfoImplCopyWith<$Res>
    implements $FirmwareInfoCopyWith<$Res> {
  factory _$$FirmwareInfoImplCopyWith(
          _$FirmwareInfoImpl value, $Res Function(_$FirmwareInfoImpl) then) =
      __$$FirmwareInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String version,
      String downloadUrl,
      String checksum,
      String checksumAlgorithm,
      int fileSize,
      DateTime releaseDate,
      String? description,
      String? releaseNotes,
      bool? isSecurityUpdate,
      bool? isCritical,
      List<String>? compatibleModels,
      String? minimumVersion,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$FirmwareInfoImplCopyWithImpl<$Res>
    extends _$FirmwareInfoCopyWithImpl<$Res, _$FirmwareInfoImpl>
    implements _$$FirmwareInfoImplCopyWith<$Res> {
  __$$FirmwareInfoImplCopyWithImpl(
      _$FirmwareInfoImpl _value, $Res Function(_$FirmwareInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of FirmwareInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? downloadUrl = null,
    Object? checksum = null,
    Object? checksumAlgorithm = null,
    Object? fileSize = null,
    Object? releaseDate = null,
    Object? description = freezed,
    Object? releaseNotes = freezed,
    Object? isSecurityUpdate = freezed,
    Object? isCritical = freezed,
    Object? compatibleModels = freezed,
    Object? minimumVersion = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_$FirmwareInfoImpl(
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      checksum: null == checksum
          ? _value.checksum
          : checksum // ignore: cast_nullable_to_non_nullable
              as String,
      checksumAlgorithm: null == checksumAlgorithm
          ? _value.checksumAlgorithm
          : checksumAlgorithm // ignore: cast_nullable_to_non_nullable
              as String,
      fileSize: null == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int,
      releaseDate: null == releaseDate
          ? _value.releaseDate
          : releaseDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      releaseNotes: freezed == releaseNotes
          ? _value.releaseNotes
          : releaseNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      isSecurityUpdate: freezed == isSecurityUpdate
          ? _value.isSecurityUpdate
          : isSecurityUpdate // ignore: cast_nullable_to_non_nullable
              as bool?,
      isCritical: freezed == isCritical
          ? _value.isCritical
          : isCritical // ignore: cast_nullable_to_non_nullable
              as bool?,
      compatibleModels: freezed == compatibleModels
          ? _value._compatibleModels
          : compatibleModels // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      minimumVersion: freezed == minimumVersion
          ? _value.minimumVersion
          : minimumVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FirmwareInfoImpl implements _FirmwareInfo {
  const _$FirmwareInfoImpl(
      {required this.version,
      required this.downloadUrl,
      required this.checksum,
      required this.checksumAlgorithm,
      required this.fileSize,
      required this.releaseDate,
      this.description,
      this.releaseNotes,
      this.isSecurityUpdate,
      this.isCritical,
      final List<String>? compatibleModels,
      this.minimumVersion,
      final Map<String, dynamic>? metadata})
      : _compatibleModels = compatibleModels,
        _metadata = metadata;

  factory _$FirmwareInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$FirmwareInfoImplFromJson(json);

  @override
  final String version;
  @override
  final String downloadUrl;
  @override
  final String checksum;
  @override
  final String checksumAlgorithm;
  @override
  final int fileSize;
  @override
  final DateTime releaseDate;
  @override
  final String? description;
  @override
  final String? releaseNotes;
  @override
  final bool? isSecurityUpdate;
  @override
  final bool? isCritical;
  final List<String>? _compatibleModels;
  @override
  List<String>? get compatibleModels {
    final value = _compatibleModels;
    if (value == null) return null;
    if (_compatibleModels is EqualUnmodifiableListView)
      return _compatibleModels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? minimumVersion;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'FirmwareInfo(version: $version, downloadUrl: $downloadUrl, checksum: $checksum, checksumAlgorithm: $checksumAlgorithm, fileSize: $fileSize, releaseDate: $releaseDate, description: $description, releaseNotes: $releaseNotes, isSecurityUpdate: $isSecurityUpdate, isCritical: $isCritical, compatibleModels: $compatibleModels, minimumVersion: $minimumVersion, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FirmwareInfoImpl &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.downloadUrl, downloadUrl) ||
                other.downloadUrl == downloadUrl) &&
            (identical(other.checksum, checksum) ||
                other.checksum == checksum) &&
            (identical(other.checksumAlgorithm, checksumAlgorithm) ||
                other.checksumAlgorithm == checksumAlgorithm) &&
            (identical(other.fileSize, fileSize) ||
                other.fileSize == fileSize) &&
            (identical(other.releaseDate, releaseDate) ||
                other.releaseDate == releaseDate) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.releaseNotes, releaseNotes) ||
                other.releaseNotes == releaseNotes) &&
            (identical(other.isSecurityUpdate, isSecurityUpdate) ||
                other.isSecurityUpdate == isSecurityUpdate) &&
            (identical(other.isCritical, isCritical) ||
                other.isCritical == isCritical) &&
            const DeepCollectionEquality()
                .equals(other._compatibleModels, _compatibleModels) &&
            (identical(other.minimumVersion, minimumVersion) ||
                other.minimumVersion == minimumVersion) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      version,
      downloadUrl,
      checksum,
      checksumAlgorithm,
      fileSize,
      releaseDate,
      description,
      releaseNotes,
      isSecurityUpdate,
      isCritical,
      const DeepCollectionEquality().hash(_compatibleModels),
      minimumVersion,
      const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of FirmwareInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FirmwareInfoImplCopyWith<_$FirmwareInfoImpl> get copyWith =>
      __$$FirmwareInfoImplCopyWithImpl<_$FirmwareInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FirmwareInfoImplToJson(
      this,
    );
  }
}

abstract class _FirmwareInfo implements FirmwareInfo {
  const factory _FirmwareInfo(
      {required final String version,
      required final String downloadUrl,
      required final String checksum,
      required final String checksumAlgorithm,
      required final int fileSize,
      required final DateTime releaseDate,
      final String? description,
      final String? releaseNotes,
      final bool? isSecurityUpdate,
      final bool? isCritical,
      final List<String>? compatibleModels,
      final String? minimumVersion,
      final Map<String, dynamic>? metadata}) = _$FirmwareInfoImpl;

  factory _FirmwareInfo.fromJson(Map<String, dynamic> json) =
      _$FirmwareInfoImpl.fromJson;

  @override
  String get version;
  @override
  String get downloadUrl;
  @override
  String get checksum;
  @override
  String get checksumAlgorithm;
  @override
  int get fileSize;
  @override
  DateTime get releaseDate;
  @override
  String? get description;
  @override
  String? get releaseNotes;
  @override
  bool? get isSecurityUpdate;
  @override
  bool? get isCritical;
  @override
  List<String>? get compatibleModels;
  @override
  String? get minimumVersion;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of FirmwareInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FirmwareInfoImplCopyWith<_$FirmwareInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DownloadProgress _$DownloadProgressFromJson(Map<String, dynamic> json) {
  return _DownloadProgress.fromJson(json);
}

/// @nodoc
mixin _$DownloadProgress {
  int get bytesDownloaded => throw _privateConstructorUsedError;
  int get totalBytes => throw _privateConstructorUsedError;
  double get percentage => throw _privateConstructorUsedError;
  double get speed => throw _privateConstructorUsedError; // bytes per second
  DateTime? get estimatedCompletion => throw _privateConstructorUsedError;
  String? get currentPhase => throw _privateConstructorUsedError;

  /// Serializes this DownloadProgress to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DownloadProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DownloadProgressCopyWith<DownloadProgress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DownloadProgressCopyWith<$Res> {
  factory $DownloadProgressCopyWith(
          DownloadProgress value, $Res Function(DownloadProgress) then) =
      _$DownloadProgressCopyWithImpl<$Res, DownloadProgress>;
  @useResult
  $Res call(
      {int bytesDownloaded,
      int totalBytes,
      double percentage,
      double speed,
      DateTime? estimatedCompletion,
      String? currentPhase});
}

/// @nodoc
class _$DownloadProgressCopyWithImpl<$Res, $Val extends DownloadProgress>
    implements $DownloadProgressCopyWith<$Res> {
  _$DownloadProgressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DownloadProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bytesDownloaded = null,
    Object? totalBytes = null,
    Object? percentage = null,
    Object? speed = null,
    Object? estimatedCompletion = freezed,
    Object? currentPhase = freezed,
  }) {
    return _then(_value.copyWith(
      bytesDownloaded: null == bytesDownloaded
          ? _value.bytesDownloaded
          : bytesDownloaded // ignore: cast_nullable_to_non_nullable
              as int,
      totalBytes: null == totalBytes
          ? _value.totalBytes
          : totalBytes // ignore: cast_nullable_to_non_nullable
              as int,
      percentage: null == percentage
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
      speed: null == speed
          ? _value.speed
          : speed // ignore: cast_nullable_to_non_nullable
              as double,
      estimatedCompletion: freezed == estimatedCompletion
          ? _value.estimatedCompletion
          : estimatedCompletion // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentPhase: freezed == currentPhase
          ? _value.currentPhase
          : currentPhase // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DownloadProgressImplCopyWith<$Res>
    implements $DownloadProgressCopyWith<$Res> {
  factory _$$DownloadProgressImplCopyWith(_$DownloadProgressImpl value,
          $Res Function(_$DownloadProgressImpl) then) =
      __$$DownloadProgressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int bytesDownloaded,
      int totalBytes,
      double percentage,
      double speed,
      DateTime? estimatedCompletion,
      String? currentPhase});
}

/// @nodoc
class __$$DownloadProgressImplCopyWithImpl<$Res>
    extends _$DownloadProgressCopyWithImpl<$Res, _$DownloadProgressImpl>
    implements _$$DownloadProgressImplCopyWith<$Res> {
  __$$DownloadProgressImplCopyWithImpl(_$DownloadProgressImpl _value,
      $Res Function(_$DownloadProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of DownloadProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bytesDownloaded = null,
    Object? totalBytes = null,
    Object? percentage = null,
    Object? speed = null,
    Object? estimatedCompletion = freezed,
    Object? currentPhase = freezed,
  }) {
    return _then(_$DownloadProgressImpl(
      bytesDownloaded: null == bytesDownloaded
          ? _value.bytesDownloaded
          : bytesDownloaded // ignore: cast_nullable_to_non_nullable
              as int,
      totalBytes: null == totalBytes
          ? _value.totalBytes
          : totalBytes // ignore: cast_nullable_to_non_nullable
              as int,
      percentage: null == percentage
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
      speed: null == speed
          ? _value.speed
          : speed // ignore: cast_nullable_to_non_nullable
              as double,
      estimatedCompletion: freezed == estimatedCompletion
          ? _value.estimatedCompletion
          : estimatedCompletion // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentPhase: freezed == currentPhase
          ? _value.currentPhase
          : currentPhase // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DownloadProgressImpl implements _DownloadProgress {
  const _$DownloadProgressImpl(
      {required this.bytesDownloaded,
      required this.totalBytes,
      required this.percentage,
      required this.speed,
      this.estimatedCompletion,
      this.currentPhase});

  factory _$DownloadProgressImpl.fromJson(Map<String, dynamic> json) =>
      _$$DownloadProgressImplFromJson(json);

  @override
  final int bytesDownloaded;
  @override
  final int totalBytes;
  @override
  final double percentage;
  @override
  final double speed;
// bytes per second
  @override
  final DateTime? estimatedCompletion;
  @override
  final String? currentPhase;

  @override
  String toString() {
    return 'DownloadProgress(bytesDownloaded: $bytesDownloaded, totalBytes: $totalBytes, percentage: $percentage, speed: $speed, estimatedCompletion: $estimatedCompletion, currentPhase: $currentPhase)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DownloadProgressImpl &&
            (identical(other.bytesDownloaded, bytesDownloaded) ||
                other.bytesDownloaded == bytesDownloaded) &&
            (identical(other.totalBytes, totalBytes) ||
                other.totalBytes == totalBytes) &&
            (identical(other.percentage, percentage) ||
                other.percentage == percentage) &&
            (identical(other.speed, speed) || other.speed == speed) &&
            (identical(other.estimatedCompletion, estimatedCompletion) ||
                other.estimatedCompletion == estimatedCompletion) &&
            (identical(other.currentPhase, currentPhase) ||
                other.currentPhase == currentPhase));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, bytesDownloaded, totalBytes,
      percentage, speed, estimatedCompletion, currentPhase);

  /// Create a copy of DownloadProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DownloadProgressImplCopyWith<_$DownloadProgressImpl> get copyWith =>
      __$$DownloadProgressImplCopyWithImpl<_$DownloadProgressImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DownloadProgressImplToJson(
      this,
    );
  }
}

abstract class _DownloadProgress implements DownloadProgress {
  const factory _DownloadProgress(
      {required final int bytesDownloaded,
      required final int totalBytes,
      required final double percentage,
      required final double speed,
      final DateTime? estimatedCompletion,
      final String? currentPhase}) = _$DownloadProgressImpl;

  factory _DownloadProgress.fromJson(Map<String, dynamic> json) =
      _$DownloadProgressImpl.fromJson;

  @override
  int get bytesDownloaded;
  @override
  int get totalBytes;
  @override
  double get percentage;
  @override
  double get speed; // bytes per second
  @override
  DateTime? get estimatedCompletion;
  @override
  String? get currentPhase;

  /// Create a copy of DownloadProgress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DownloadProgressImplCopyWith<_$DownloadProgressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

InstallationProgress _$InstallationProgressFromJson(Map<String, dynamic> json) {
  return _InstallationProgress.fromJson(json);
}

/// @nodoc
mixin _$InstallationProgress {
  String get phase => throw _privateConstructorUsedError;
  double get percentage => throw _privateConstructorUsedError;
  String? get currentStep => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  DateTime? get estimatedCompletion => throw _privateConstructorUsedError;

  /// Serializes this InstallationProgress to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of InstallationProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InstallationProgressCopyWith<InstallationProgress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstallationProgressCopyWith<$Res> {
  factory $InstallationProgressCopyWith(InstallationProgress value,
          $Res Function(InstallationProgress) then) =
      _$InstallationProgressCopyWithImpl<$Res, InstallationProgress>;
  @useResult
  $Res call(
      {String phase,
      double percentage,
      String? currentStep,
      String? message,
      DateTime? estimatedCompletion});
}

/// @nodoc
class _$InstallationProgressCopyWithImpl<$Res,
        $Val extends InstallationProgress>
    implements $InstallationProgressCopyWith<$Res> {
  _$InstallationProgressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InstallationProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phase = null,
    Object? percentage = null,
    Object? currentStep = freezed,
    Object? message = freezed,
    Object? estimatedCompletion = freezed,
  }) {
    return _then(_value.copyWith(
      phase: null == phase
          ? _value.phase
          : phase // ignore: cast_nullable_to_non_nullable
              as String,
      percentage: null == percentage
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
      currentStep: freezed == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedCompletion: freezed == estimatedCompletion
          ? _value.estimatedCompletion
          : estimatedCompletion // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InstallationProgressImplCopyWith<$Res>
    implements $InstallationProgressCopyWith<$Res> {
  factory _$$InstallationProgressImplCopyWith(_$InstallationProgressImpl value,
          $Res Function(_$InstallationProgressImpl) then) =
      __$$InstallationProgressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String phase,
      double percentage,
      String? currentStep,
      String? message,
      DateTime? estimatedCompletion});
}

/// @nodoc
class __$$InstallationProgressImplCopyWithImpl<$Res>
    extends _$InstallationProgressCopyWithImpl<$Res, _$InstallationProgressImpl>
    implements _$$InstallationProgressImplCopyWith<$Res> {
  __$$InstallationProgressImplCopyWithImpl(_$InstallationProgressImpl _value,
      $Res Function(_$InstallationProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of InstallationProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phase = null,
    Object? percentage = null,
    Object? currentStep = freezed,
    Object? message = freezed,
    Object? estimatedCompletion = freezed,
  }) {
    return _then(_$InstallationProgressImpl(
      phase: null == phase
          ? _value.phase
          : phase // ignore: cast_nullable_to_non_nullable
              as String,
      percentage: null == percentage
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
      currentStep: freezed == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedCompletion: freezed == estimatedCompletion
          ? _value.estimatedCompletion
          : estimatedCompletion // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InstallationProgressImpl implements _InstallationProgress {
  const _$InstallationProgressImpl(
      {required this.phase,
      required this.percentage,
      this.currentStep,
      this.message,
      this.estimatedCompletion});

  factory _$InstallationProgressImpl.fromJson(Map<String, dynamic> json) =>
      _$$InstallationProgressImplFromJson(json);

  @override
  final String phase;
  @override
  final double percentage;
  @override
  final String? currentStep;
  @override
  final String? message;
  @override
  final DateTime? estimatedCompletion;

  @override
  String toString() {
    return 'InstallationProgress(phase: $phase, percentage: $percentage, currentStep: $currentStep, message: $message, estimatedCompletion: $estimatedCompletion)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InstallationProgressImpl &&
            (identical(other.phase, phase) || other.phase == phase) &&
            (identical(other.percentage, percentage) ||
                other.percentage == percentage) &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.estimatedCompletion, estimatedCompletion) ||
                other.estimatedCompletion == estimatedCompletion));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, phase, percentage, currentStep,
      message, estimatedCompletion);

  /// Create a copy of InstallationProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InstallationProgressImplCopyWith<_$InstallationProgressImpl>
      get copyWith =>
          __$$InstallationProgressImplCopyWithImpl<_$InstallationProgressImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InstallationProgressImplToJson(
      this,
    );
  }
}

abstract class _InstallationProgress implements InstallationProgress {
  const factory _InstallationProgress(
      {required final String phase,
      required final double percentage,
      final String? currentStep,
      final String? message,
      final DateTime? estimatedCompletion}) = _$InstallationProgressImpl;

  factory _InstallationProgress.fromJson(Map<String, dynamic> json) =
      _$InstallationProgressImpl.fromJson;

  @override
  String get phase;
  @override
  double get percentage;
  @override
  String? get currentStep;
  @override
  String? get message;
  @override
  DateTime? get estimatedCompletion;

  /// Create a copy of InstallationProgress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InstallationProgressImplCopyWith<_$InstallationProgressImpl>
      get copyWith => throw _privateConstructorUsedError;
}

FirmwareUpdateRequest _$FirmwareUpdateRequestFromJson(
    Map<String, dynamic> json) {
  return _FirmwareUpdateRequest.fromJson(json);
}

/// @nodoc
mixin _$FirmwareUpdateRequest {
  String get chargerId => throw _privateConstructorUsedError;
  FirmwareInfo get firmwareInfo => throw _privateConstructorUsedError;
  DateTime get requestedAt => throw _privateConstructorUsedError;
  DateTime? get scheduledAt => throw _privateConstructorUsedError;
  bool? get forceUpdate => throw _privateConstructorUsedError;
  int? get retryAttempts => throw _privateConstructorUsedError;
  Map<String, dynamic>? get options => throw _privateConstructorUsedError;

  /// Serializes this FirmwareUpdateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FirmwareUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FirmwareUpdateRequestCopyWith<FirmwareUpdateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FirmwareUpdateRequestCopyWith<$Res> {
  factory $FirmwareUpdateRequestCopyWith(FirmwareUpdateRequest value,
          $Res Function(FirmwareUpdateRequest) then) =
      _$FirmwareUpdateRequestCopyWithImpl<$Res, FirmwareUpdateRequest>;
  @useResult
  $Res call(
      {String chargerId,
      FirmwareInfo firmwareInfo,
      DateTime requestedAt,
      DateTime? scheduledAt,
      bool? forceUpdate,
      int? retryAttempts,
      Map<String, dynamic>? options});

  $FirmwareInfoCopyWith<$Res> get firmwareInfo;
}

/// @nodoc
class _$FirmwareUpdateRequestCopyWithImpl<$Res,
        $Val extends FirmwareUpdateRequest>
    implements $FirmwareUpdateRequestCopyWith<$Res> {
  _$FirmwareUpdateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FirmwareUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chargerId = null,
    Object? firmwareInfo = null,
    Object? requestedAt = null,
    Object? scheduledAt = freezed,
    Object? forceUpdate = freezed,
    Object? retryAttempts = freezed,
    Object? options = freezed,
  }) {
    return _then(_value.copyWith(
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      firmwareInfo: null == firmwareInfo
          ? _value.firmwareInfo
          : firmwareInfo // ignore: cast_nullable_to_non_nullable
              as FirmwareInfo,
      requestedAt: null == requestedAt
          ? _value.requestedAt
          : requestedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      scheduledAt: freezed == scheduledAt
          ? _value.scheduledAt
          : scheduledAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      forceUpdate: freezed == forceUpdate
          ? _value.forceUpdate
          : forceUpdate // ignore: cast_nullable_to_non_nullable
              as bool?,
      retryAttempts: freezed == retryAttempts
          ? _value.retryAttempts
          : retryAttempts // ignore: cast_nullable_to_non_nullable
              as int?,
      options: freezed == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }

  /// Create a copy of FirmwareUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FirmwareInfoCopyWith<$Res> get firmwareInfo {
    return $FirmwareInfoCopyWith<$Res>(_value.firmwareInfo, (value) {
      return _then(_value.copyWith(firmwareInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FirmwareUpdateRequestImplCopyWith<$Res>
    implements $FirmwareUpdateRequestCopyWith<$Res> {
  factory _$$FirmwareUpdateRequestImplCopyWith(
          _$FirmwareUpdateRequestImpl value,
          $Res Function(_$FirmwareUpdateRequestImpl) then) =
      __$$FirmwareUpdateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String chargerId,
      FirmwareInfo firmwareInfo,
      DateTime requestedAt,
      DateTime? scheduledAt,
      bool? forceUpdate,
      int? retryAttempts,
      Map<String, dynamic>? options});

  @override
  $FirmwareInfoCopyWith<$Res> get firmwareInfo;
}

/// @nodoc
class __$$FirmwareUpdateRequestImplCopyWithImpl<$Res>
    extends _$FirmwareUpdateRequestCopyWithImpl<$Res,
        _$FirmwareUpdateRequestImpl>
    implements _$$FirmwareUpdateRequestImplCopyWith<$Res> {
  __$$FirmwareUpdateRequestImplCopyWithImpl(_$FirmwareUpdateRequestImpl _value,
      $Res Function(_$FirmwareUpdateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of FirmwareUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chargerId = null,
    Object? firmwareInfo = null,
    Object? requestedAt = null,
    Object? scheduledAt = freezed,
    Object? forceUpdate = freezed,
    Object? retryAttempts = freezed,
    Object? options = freezed,
  }) {
    return _then(_$FirmwareUpdateRequestImpl(
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      firmwareInfo: null == firmwareInfo
          ? _value.firmwareInfo
          : firmwareInfo // ignore: cast_nullable_to_non_nullable
              as FirmwareInfo,
      requestedAt: null == requestedAt
          ? _value.requestedAt
          : requestedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      scheduledAt: freezed == scheduledAt
          ? _value.scheduledAt
          : scheduledAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      forceUpdate: freezed == forceUpdate
          ? _value.forceUpdate
          : forceUpdate // ignore: cast_nullable_to_non_nullable
              as bool?,
      retryAttempts: freezed == retryAttempts
          ? _value.retryAttempts
          : retryAttempts // ignore: cast_nullable_to_non_nullable
              as int?,
      options: freezed == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FirmwareUpdateRequestImpl implements _FirmwareUpdateRequest {
  const _$FirmwareUpdateRequestImpl(
      {required this.chargerId,
      required this.firmwareInfo,
      required this.requestedAt,
      this.scheduledAt,
      this.forceUpdate,
      this.retryAttempts,
      final Map<String, dynamic>? options})
      : _options = options;

  factory _$FirmwareUpdateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$FirmwareUpdateRequestImplFromJson(json);

  @override
  final String chargerId;
  @override
  final FirmwareInfo firmwareInfo;
  @override
  final DateTime requestedAt;
  @override
  final DateTime? scheduledAt;
  @override
  final bool? forceUpdate;
  @override
  final int? retryAttempts;
  final Map<String, dynamic>? _options;
  @override
  Map<String, dynamic>? get options {
    final value = _options;
    if (value == null) return null;
    if (_options is EqualUnmodifiableMapView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'FirmwareUpdateRequest(chargerId: $chargerId, firmwareInfo: $firmwareInfo, requestedAt: $requestedAt, scheduledAt: $scheduledAt, forceUpdate: $forceUpdate, retryAttempts: $retryAttempts, options: $options)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FirmwareUpdateRequestImpl &&
            (identical(other.chargerId, chargerId) ||
                other.chargerId == chargerId) &&
            (identical(other.firmwareInfo, firmwareInfo) ||
                other.firmwareInfo == firmwareInfo) &&
            (identical(other.requestedAt, requestedAt) ||
                other.requestedAt == requestedAt) &&
            (identical(other.scheduledAt, scheduledAt) ||
                other.scheduledAt == scheduledAt) &&
            (identical(other.forceUpdate, forceUpdate) ||
                other.forceUpdate == forceUpdate) &&
            (identical(other.retryAttempts, retryAttempts) ||
                other.retryAttempts == retryAttempts) &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      chargerId,
      firmwareInfo,
      requestedAt,
      scheduledAt,
      forceUpdate,
      retryAttempts,
      const DeepCollectionEquality().hash(_options));

  /// Create a copy of FirmwareUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FirmwareUpdateRequestImplCopyWith<_$FirmwareUpdateRequestImpl>
      get copyWith => __$$FirmwareUpdateRequestImplCopyWithImpl<
          _$FirmwareUpdateRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FirmwareUpdateRequestImplToJson(
      this,
    );
  }
}

abstract class _FirmwareUpdateRequest implements FirmwareUpdateRequest {
  const factory _FirmwareUpdateRequest(
      {required final String chargerId,
      required final FirmwareInfo firmwareInfo,
      required final DateTime requestedAt,
      final DateTime? scheduledAt,
      final bool? forceUpdate,
      final int? retryAttempts,
      final Map<String, dynamic>? options}) = _$FirmwareUpdateRequestImpl;

  factory _FirmwareUpdateRequest.fromJson(Map<String, dynamic> json) =
      _$FirmwareUpdateRequestImpl.fromJson;

  @override
  String get chargerId;
  @override
  FirmwareInfo get firmwareInfo;
  @override
  DateTime get requestedAt;
  @override
  DateTime? get scheduledAt;
  @override
  bool? get forceUpdate;
  @override
  int? get retryAttempts;
  @override
  Map<String, dynamic>? get options;

  /// Create a copy of FirmwareUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FirmwareUpdateRequestImplCopyWith<_$FirmwareUpdateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

FirmwareUpdateResult _$FirmwareUpdateResultFromJson(Map<String, dynamic> json) {
  return _FirmwareUpdateResult.fromJson(json);
}

/// @nodoc
mixin _$FirmwareUpdateResult {
  String get chargerId => throw _privateConstructorUsedError;
  FirmwareStatus get status => throw _privateConstructorUsedError;
  DateTime get completedAt => throw _privateConstructorUsedError;
  String? get previousVersion => throw _privateConstructorUsedError;
  String? get newVersion => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  String? get errorCode => throw _privateConstructorUsedError;
  Duration? get updateDuration => throw _privateConstructorUsedError;
  Map<String, dynamic>? get diagnostics => throw _privateConstructorUsedError;

  /// Serializes this FirmwareUpdateResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FirmwareUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FirmwareUpdateResultCopyWith<FirmwareUpdateResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FirmwareUpdateResultCopyWith<$Res> {
  factory $FirmwareUpdateResultCopyWith(FirmwareUpdateResult value,
          $Res Function(FirmwareUpdateResult) then) =
      _$FirmwareUpdateResultCopyWithImpl<$Res, FirmwareUpdateResult>;
  @useResult
  $Res call(
      {String chargerId,
      FirmwareStatus status,
      DateTime completedAt,
      String? previousVersion,
      String? newVersion,
      String? errorMessage,
      String? errorCode,
      Duration? updateDuration,
      Map<String, dynamic>? diagnostics});
}

/// @nodoc
class _$FirmwareUpdateResultCopyWithImpl<$Res,
        $Val extends FirmwareUpdateResult>
    implements $FirmwareUpdateResultCopyWith<$Res> {
  _$FirmwareUpdateResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FirmwareUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chargerId = null,
    Object? status = null,
    Object? completedAt = null,
    Object? previousVersion = freezed,
    Object? newVersion = freezed,
    Object? errorMessage = freezed,
    Object? errorCode = freezed,
    Object? updateDuration = freezed,
    Object? diagnostics = freezed,
  }) {
    return _then(_value.copyWith(
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FirmwareStatus,
      completedAt: null == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      previousVersion: freezed == previousVersion
          ? _value.previousVersion
          : previousVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      newVersion: freezed == newVersion
          ? _value.newVersion
          : newVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      updateDuration: freezed == updateDuration
          ? _value.updateDuration
          : updateDuration // ignore: cast_nullable_to_non_nullable
              as Duration?,
      diagnostics: freezed == diagnostics
          ? _value.diagnostics
          : diagnostics // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FirmwareUpdateResultImplCopyWith<$Res>
    implements $FirmwareUpdateResultCopyWith<$Res> {
  factory _$$FirmwareUpdateResultImplCopyWith(_$FirmwareUpdateResultImpl value,
          $Res Function(_$FirmwareUpdateResultImpl) then) =
      __$$FirmwareUpdateResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String chargerId,
      FirmwareStatus status,
      DateTime completedAt,
      String? previousVersion,
      String? newVersion,
      String? errorMessage,
      String? errorCode,
      Duration? updateDuration,
      Map<String, dynamic>? diagnostics});
}

/// @nodoc
class __$$FirmwareUpdateResultImplCopyWithImpl<$Res>
    extends _$FirmwareUpdateResultCopyWithImpl<$Res, _$FirmwareUpdateResultImpl>
    implements _$$FirmwareUpdateResultImplCopyWith<$Res> {
  __$$FirmwareUpdateResultImplCopyWithImpl(_$FirmwareUpdateResultImpl _value,
      $Res Function(_$FirmwareUpdateResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of FirmwareUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chargerId = null,
    Object? status = null,
    Object? completedAt = null,
    Object? previousVersion = freezed,
    Object? newVersion = freezed,
    Object? errorMessage = freezed,
    Object? errorCode = freezed,
    Object? updateDuration = freezed,
    Object? diagnostics = freezed,
  }) {
    return _then(_$FirmwareUpdateResultImpl(
      chargerId: null == chargerId
          ? _value.chargerId
          : chargerId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FirmwareStatus,
      completedAt: null == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      previousVersion: freezed == previousVersion
          ? _value.previousVersion
          : previousVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      newVersion: freezed == newVersion
          ? _value.newVersion
          : newVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      updateDuration: freezed == updateDuration
          ? _value.updateDuration
          : updateDuration // ignore: cast_nullable_to_non_nullable
              as Duration?,
      diagnostics: freezed == diagnostics
          ? _value._diagnostics
          : diagnostics // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FirmwareUpdateResultImpl implements _FirmwareUpdateResult {
  const _$FirmwareUpdateResultImpl(
      {required this.chargerId,
      required this.status,
      required this.completedAt,
      this.previousVersion,
      this.newVersion,
      this.errorMessage,
      this.errorCode,
      this.updateDuration,
      final Map<String, dynamic>? diagnostics})
      : _diagnostics = diagnostics;

  factory _$FirmwareUpdateResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$FirmwareUpdateResultImplFromJson(json);

  @override
  final String chargerId;
  @override
  final FirmwareStatus status;
  @override
  final DateTime completedAt;
  @override
  final String? previousVersion;
  @override
  final String? newVersion;
  @override
  final String? errorMessage;
  @override
  final String? errorCode;
  @override
  final Duration? updateDuration;
  final Map<String, dynamic>? _diagnostics;
  @override
  Map<String, dynamic>? get diagnostics {
    final value = _diagnostics;
    if (value == null) return null;
    if (_diagnostics is EqualUnmodifiableMapView) return _diagnostics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'FirmwareUpdateResult(chargerId: $chargerId, status: $status, completedAt: $completedAt, previousVersion: $previousVersion, newVersion: $newVersion, errorMessage: $errorMessage, errorCode: $errorCode, updateDuration: $updateDuration, diagnostics: $diagnostics)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FirmwareUpdateResultImpl &&
            (identical(other.chargerId, chargerId) ||
                other.chargerId == chargerId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.previousVersion, previousVersion) ||
                other.previousVersion == previousVersion) &&
            (identical(other.newVersion, newVersion) ||
                other.newVersion == newVersion) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.updateDuration, updateDuration) ||
                other.updateDuration == updateDuration) &&
            const DeepCollectionEquality()
                .equals(other._diagnostics, _diagnostics));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      chargerId,
      status,
      completedAt,
      previousVersion,
      newVersion,
      errorMessage,
      errorCode,
      updateDuration,
      const DeepCollectionEquality().hash(_diagnostics));

  /// Create a copy of FirmwareUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FirmwareUpdateResultImplCopyWith<_$FirmwareUpdateResultImpl>
      get copyWith =>
          __$$FirmwareUpdateResultImplCopyWithImpl<_$FirmwareUpdateResultImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FirmwareUpdateResultImplToJson(
      this,
    );
  }
}

abstract class _FirmwareUpdateResult implements FirmwareUpdateResult {
  const factory _FirmwareUpdateResult(
      {required final String chargerId,
      required final FirmwareStatus status,
      required final DateTime completedAt,
      final String? previousVersion,
      final String? newVersion,
      final String? errorMessage,
      final String? errorCode,
      final Duration? updateDuration,
      final Map<String, dynamic>? diagnostics}) = _$FirmwareUpdateResultImpl;

  factory _FirmwareUpdateResult.fromJson(Map<String, dynamic> json) =
      _$FirmwareUpdateResultImpl.fromJson;

  @override
  String get chargerId;
  @override
  FirmwareStatus get status;
  @override
  DateTime get completedAt;
  @override
  String? get previousVersion;
  @override
  String? get newVersion;
  @override
  String? get errorMessage;
  @override
  String? get errorCode;
  @override
  Duration? get updateDuration;
  @override
  Map<String, dynamic>? get diagnostics;

  /// Create a copy of FirmwareUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FirmwareUpdateResultImplCopyWith<_$FirmwareUpdateResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}
