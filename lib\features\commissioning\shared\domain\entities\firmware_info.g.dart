// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firmware_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FirmwareInfoImpl _$$FirmwareInfoImplFromJson(Map<String, dynamic> json) =>
    _$FirmwareInfoImpl(
      version: json['version'] as String,
      downloadUrl: json['downloadUrl'] as String,
      checksum: json['checksum'] as String,
      checksumAlgorithm: json['checksumAlgorithm'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      releaseDate: DateTime.parse(json['releaseDate'] as String),
      description: json['description'] as String?,
      releaseNotes: json['releaseNotes'] as String?,
      isSecurityUpdate: json['isSecurityUpdate'] as bool?,
      isCritical: json['isCritical'] as bool?,
      compatibleModels: (json['compatibleModels'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      minimumVersion: json['minimumVersion'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$FirmwareInfoImplToJson(_$FirmwareInfoImpl instance) =>
    <String, dynamic>{
      'version': instance.version,
      'downloadUrl': instance.downloadUrl,
      'checksum': instance.checksum,
      'checksumAlgorithm': instance.checksumAlgorithm,
      'fileSize': instance.fileSize,
      'releaseDate': instance.releaseDate.toIso8601String(),
      'description': instance.description,
      'releaseNotes': instance.releaseNotes,
      'isSecurityUpdate': instance.isSecurityUpdate,
      'isCritical': instance.isCritical,
      'compatibleModels': instance.compatibleModels,
      'minimumVersion': instance.minimumVersion,
      'metadata': instance.metadata,
    };

_$DownloadProgressImpl _$$DownloadProgressImplFromJson(
        Map<String, dynamic> json) =>
    _$DownloadProgressImpl(
      bytesDownloaded: (json['bytesDownloaded'] as num).toInt(),
      totalBytes: (json['totalBytes'] as num).toInt(),
      percentage: (json['percentage'] as num).toDouble(),
      speed: (json['speed'] as num).toDouble(),
      estimatedCompletion: json['estimatedCompletion'] == null
          ? null
          : DateTime.parse(json['estimatedCompletion'] as String),
      currentPhase: json['currentPhase'] as String?,
    );

Map<String, dynamic> _$$DownloadProgressImplToJson(
        _$DownloadProgressImpl instance) =>
    <String, dynamic>{
      'bytesDownloaded': instance.bytesDownloaded,
      'totalBytes': instance.totalBytes,
      'percentage': instance.percentage,
      'speed': instance.speed,
      'estimatedCompletion': instance.estimatedCompletion?.toIso8601String(),
      'currentPhase': instance.currentPhase,
    };

_$InstallationProgressImpl _$$InstallationProgressImplFromJson(
        Map<String, dynamic> json) =>
    _$InstallationProgressImpl(
      phase: json['phase'] as String,
      percentage: (json['percentage'] as num).toDouble(),
      currentStep: json['currentStep'] as String?,
      message: json['message'] as String?,
      estimatedCompletion: json['estimatedCompletion'] == null
          ? null
          : DateTime.parse(json['estimatedCompletion'] as String),
    );

Map<String, dynamic> _$$InstallationProgressImplToJson(
        _$InstallationProgressImpl instance) =>
    <String, dynamic>{
      'phase': instance.phase,
      'percentage': instance.percentage,
      'currentStep': instance.currentStep,
      'message': instance.message,
      'estimatedCompletion': instance.estimatedCompletion?.toIso8601String(),
    };

_$FirmwareUpdateRequestImpl _$$FirmwareUpdateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$FirmwareUpdateRequestImpl(
      chargerId: json['chargerId'] as String,
      firmwareInfo:
          FirmwareInfo.fromJson(json['firmwareInfo'] as Map<String, dynamic>),
      requestedAt: DateTime.parse(json['requestedAt'] as String),
      scheduledAt: json['scheduledAt'] == null
          ? null
          : DateTime.parse(json['scheduledAt'] as String),
      forceUpdate: json['forceUpdate'] as bool?,
      retryAttempts: (json['retryAttempts'] as num?)?.toInt(),
      options: json['options'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$FirmwareUpdateRequestImplToJson(
        _$FirmwareUpdateRequestImpl instance) =>
    <String, dynamic>{
      'chargerId': instance.chargerId,
      'firmwareInfo': instance.firmwareInfo,
      'requestedAt': instance.requestedAt.toIso8601String(),
      'scheduledAt': instance.scheduledAt?.toIso8601String(),
      'forceUpdate': instance.forceUpdate,
      'retryAttempts': instance.retryAttempts,
      'options': instance.options,
    };

_$FirmwareUpdateResultImpl _$$FirmwareUpdateResultImplFromJson(
        Map<String, dynamic> json) =>
    _$FirmwareUpdateResultImpl(
      chargerId: json['chargerId'] as String,
      status: $enumDecode(_$FirmwareStatusEnumMap, json['status']),
      completedAt: DateTime.parse(json['completedAt'] as String),
      previousVersion: json['previousVersion'] as String?,
      newVersion: json['newVersion'] as String?,
      errorMessage: json['errorMessage'] as String?,
      errorCode: json['errorCode'] as String?,
      updateDuration: json['updateDuration'] == null
          ? null
          : Duration(microseconds: (json['updateDuration'] as num).toInt()),
      diagnostics: json['diagnostics'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$FirmwareUpdateResultImplToJson(
        _$FirmwareUpdateResultImpl instance) =>
    <String, dynamic>{
      'chargerId': instance.chargerId,
      'status': _$FirmwareStatusEnumMap[instance.status]!,
      'completedAt': instance.completedAt.toIso8601String(),
      'previousVersion': instance.previousVersion,
      'newVersion': instance.newVersion,
      'errorMessage': instance.errorMessage,
      'errorCode': instance.errorCode,
      'updateDuration': instance.updateDuration?.inMicroseconds,
      'diagnostics': instance.diagnostics,
    };

const _$FirmwareStatusEnumMap = {
  FirmwareStatus.idle: 'idle',
  FirmwareStatus.downloading: 'downloading',
  FirmwareStatus.downloaded: 'downloaded',
  FirmwareStatus.installing: 'installing',
  FirmwareStatus.installed: 'installed',
  FirmwareStatus.failed: 'failed',
  FirmwareStatus.verificationFailed: 'verification_failed',
  FirmwareStatus.signatureVerified: 'signature_verified',
};
