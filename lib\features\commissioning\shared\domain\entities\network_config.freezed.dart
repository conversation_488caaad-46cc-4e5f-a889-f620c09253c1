// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'network_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WifiNetwork _$WifiNetworkFromJson(Map<String, dynamic> json) {
  return _WifiNetwork.fromJson(json);
}

/// @nodoc
mixin _$WifiNetwork {
  String get ssid => throw _privateConstructorUsedError;
  String get bssid => throw _privateConstructorUsedError;
  int get signalStrength => throw _privateConstructorUsedError;
  SecurityType get securityType => throw _privateConstructorUsedError;
  int get frequency => throw _privateConstructorUsedError;
  bool? get isHidden => throw _privateConstructorUsedError;
  String? get capabilities => throw _privateConstructorUsedError;

  /// Serializes this WifiNetwork to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WifiNetwork
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WifiNetworkCopyWith<WifiNetwork> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WifiNetworkCopyWith<$Res> {
  factory $WifiNetworkCopyWith(
          WifiNetwork value, $Res Function(WifiNetwork) then) =
      _$WifiNetworkCopyWithImpl<$Res, WifiNetwork>;
  @useResult
  $Res call(
      {String ssid,
      String bssid,
      int signalStrength,
      SecurityType securityType,
      int frequency,
      bool? isHidden,
      String? capabilities});
}

/// @nodoc
class _$WifiNetworkCopyWithImpl<$Res, $Val extends WifiNetwork>
    implements $WifiNetworkCopyWith<$Res> {
  _$WifiNetworkCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WifiNetwork
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ssid = null,
    Object? bssid = null,
    Object? signalStrength = null,
    Object? securityType = null,
    Object? frequency = null,
    Object? isHidden = freezed,
    Object? capabilities = freezed,
  }) {
    return _then(_value.copyWith(
      ssid: null == ssid
          ? _value.ssid
          : ssid // ignore: cast_nullable_to_non_nullable
              as String,
      bssid: null == bssid
          ? _value.bssid
          : bssid // ignore: cast_nullable_to_non_nullable
              as String,
      signalStrength: null == signalStrength
          ? _value.signalStrength
          : signalStrength // ignore: cast_nullable_to_non_nullable
              as int,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as SecurityType,
      frequency: null == frequency
          ? _value.frequency
          : frequency // ignore: cast_nullable_to_non_nullable
              as int,
      isHidden: freezed == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool?,
      capabilities: freezed == capabilities
          ? _value.capabilities
          : capabilities // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WifiNetworkImplCopyWith<$Res>
    implements $WifiNetworkCopyWith<$Res> {
  factory _$$WifiNetworkImplCopyWith(
          _$WifiNetworkImpl value, $Res Function(_$WifiNetworkImpl) then) =
      __$$WifiNetworkImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String ssid,
      String bssid,
      int signalStrength,
      SecurityType securityType,
      int frequency,
      bool? isHidden,
      String? capabilities});
}

/// @nodoc
class __$$WifiNetworkImplCopyWithImpl<$Res>
    extends _$WifiNetworkCopyWithImpl<$Res, _$WifiNetworkImpl>
    implements _$$WifiNetworkImplCopyWith<$Res> {
  __$$WifiNetworkImplCopyWithImpl(
      _$WifiNetworkImpl _value, $Res Function(_$WifiNetworkImpl) _then)
      : super(_value, _then);

  /// Create a copy of WifiNetwork
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ssid = null,
    Object? bssid = null,
    Object? signalStrength = null,
    Object? securityType = null,
    Object? frequency = null,
    Object? isHidden = freezed,
    Object? capabilities = freezed,
  }) {
    return _then(_$WifiNetworkImpl(
      ssid: null == ssid
          ? _value.ssid
          : ssid // ignore: cast_nullable_to_non_nullable
              as String,
      bssid: null == bssid
          ? _value.bssid
          : bssid // ignore: cast_nullable_to_non_nullable
              as String,
      signalStrength: null == signalStrength
          ? _value.signalStrength
          : signalStrength // ignore: cast_nullable_to_non_nullable
              as int,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as SecurityType,
      frequency: null == frequency
          ? _value.frequency
          : frequency // ignore: cast_nullable_to_non_nullable
              as int,
      isHidden: freezed == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool?,
      capabilities: freezed == capabilities
          ? _value.capabilities
          : capabilities // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WifiNetworkImpl implements _WifiNetwork {
  const _$WifiNetworkImpl(
      {required this.ssid,
      required this.bssid,
      required this.signalStrength,
      required this.securityType,
      required this.frequency,
      this.isHidden,
      this.capabilities});

  factory _$WifiNetworkImpl.fromJson(Map<String, dynamic> json) =>
      _$$WifiNetworkImplFromJson(json);

  @override
  final String ssid;
  @override
  final String bssid;
  @override
  final int signalStrength;
  @override
  final SecurityType securityType;
  @override
  final int frequency;
  @override
  final bool? isHidden;
  @override
  final String? capabilities;

  @override
  String toString() {
    return 'WifiNetwork(ssid: $ssid, bssid: $bssid, signalStrength: $signalStrength, securityType: $securityType, frequency: $frequency, isHidden: $isHidden, capabilities: $capabilities)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WifiNetworkImpl &&
            (identical(other.ssid, ssid) || other.ssid == ssid) &&
            (identical(other.bssid, bssid) || other.bssid == bssid) &&
            (identical(other.signalStrength, signalStrength) ||
                other.signalStrength == signalStrength) &&
            (identical(other.securityType, securityType) ||
                other.securityType == securityType) &&
            (identical(other.frequency, frequency) ||
                other.frequency == frequency) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.capabilities, capabilities) ||
                other.capabilities == capabilities));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, ssid, bssid, signalStrength,
      securityType, frequency, isHidden, capabilities);

  /// Create a copy of WifiNetwork
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WifiNetworkImplCopyWith<_$WifiNetworkImpl> get copyWith =>
      __$$WifiNetworkImplCopyWithImpl<_$WifiNetworkImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WifiNetworkImplToJson(
      this,
    );
  }
}

abstract class _WifiNetwork implements WifiNetwork {
  const factory _WifiNetwork(
      {required final String ssid,
      required final String bssid,
      required final int signalStrength,
      required final SecurityType securityType,
      required final int frequency,
      final bool? isHidden,
      final String? capabilities}) = _$WifiNetworkImpl;

  factory _WifiNetwork.fromJson(Map<String, dynamic> json) =
      _$WifiNetworkImpl.fromJson;

  @override
  String get ssid;
  @override
  String get bssid;
  @override
  int get signalStrength;
  @override
  SecurityType get securityType;
  @override
  int get frequency;
  @override
  bool? get isHidden;
  @override
  String? get capabilities;

  /// Create a copy of WifiNetwork
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WifiNetworkImplCopyWith<_$WifiNetworkImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NetworkConfig _$NetworkConfigFromJson(Map<String, dynamic> json) {
  return _NetworkConfig.fromJson(json);
}

/// @nodoc
mixin _$NetworkConfig {
  String get ssid => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  SecurityType get securityType => throw _privateConstructorUsedError;
  String? get ipAddress => throw _privateConstructorUsedError;
  String? get gateway => throw _privateConstructorUsedError;
  String? get subnetMask => throw _privateConstructorUsedError;
  String? get primaryDns => throw _privateConstructorUsedError;
  String? get secondaryDns => throw _privateConstructorUsedError;
  bool? get isDhcp => throw _privateConstructorUsedError;
  int? get mtu => throw _privateConstructorUsedError;
  Map<String, dynamic>? get advancedSettings =>
      throw _privateConstructorUsedError;

  /// Serializes this NetworkConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NetworkConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NetworkConfigCopyWith<NetworkConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NetworkConfigCopyWith<$Res> {
  factory $NetworkConfigCopyWith(
          NetworkConfig value, $Res Function(NetworkConfig) then) =
      _$NetworkConfigCopyWithImpl<$Res, NetworkConfig>;
  @useResult
  $Res call(
      {String ssid,
      String password,
      SecurityType securityType,
      String? ipAddress,
      String? gateway,
      String? subnetMask,
      String? primaryDns,
      String? secondaryDns,
      bool? isDhcp,
      int? mtu,
      Map<String, dynamic>? advancedSettings});
}

/// @nodoc
class _$NetworkConfigCopyWithImpl<$Res, $Val extends NetworkConfig>
    implements $NetworkConfigCopyWith<$Res> {
  _$NetworkConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NetworkConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ssid = null,
    Object? password = null,
    Object? securityType = null,
    Object? ipAddress = freezed,
    Object? gateway = freezed,
    Object? subnetMask = freezed,
    Object? primaryDns = freezed,
    Object? secondaryDns = freezed,
    Object? isDhcp = freezed,
    Object? mtu = freezed,
    Object? advancedSettings = freezed,
  }) {
    return _then(_value.copyWith(
      ssid: null == ssid
          ? _value.ssid
          : ssid // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as SecurityType,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      gateway: freezed == gateway
          ? _value.gateway
          : gateway // ignore: cast_nullable_to_non_nullable
              as String?,
      subnetMask: freezed == subnetMask
          ? _value.subnetMask
          : subnetMask // ignore: cast_nullable_to_non_nullable
              as String?,
      primaryDns: freezed == primaryDns
          ? _value.primaryDns
          : primaryDns // ignore: cast_nullable_to_non_nullable
              as String?,
      secondaryDns: freezed == secondaryDns
          ? _value.secondaryDns
          : secondaryDns // ignore: cast_nullable_to_non_nullable
              as String?,
      isDhcp: freezed == isDhcp
          ? _value.isDhcp
          : isDhcp // ignore: cast_nullable_to_non_nullable
              as bool?,
      mtu: freezed == mtu
          ? _value.mtu
          : mtu // ignore: cast_nullable_to_non_nullable
              as int?,
      advancedSettings: freezed == advancedSettings
          ? _value.advancedSettings
          : advancedSettings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NetworkConfigImplCopyWith<$Res>
    implements $NetworkConfigCopyWith<$Res> {
  factory _$$NetworkConfigImplCopyWith(
          _$NetworkConfigImpl value, $Res Function(_$NetworkConfigImpl) then) =
      __$$NetworkConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String ssid,
      String password,
      SecurityType securityType,
      String? ipAddress,
      String? gateway,
      String? subnetMask,
      String? primaryDns,
      String? secondaryDns,
      bool? isDhcp,
      int? mtu,
      Map<String, dynamic>? advancedSettings});
}

/// @nodoc
class __$$NetworkConfigImplCopyWithImpl<$Res>
    extends _$NetworkConfigCopyWithImpl<$Res, _$NetworkConfigImpl>
    implements _$$NetworkConfigImplCopyWith<$Res> {
  __$$NetworkConfigImplCopyWithImpl(
      _$NetworkConfigImpl _value, $Res Function(_$NetworkConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of NetworkConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ssid = null,
    Object? password = null,
    Object? securityType = null,
    Object? ipAddress = freezed,
    Object? gateway = freezed,
    Object? subnetMask = freezed,
    Object? primaryDns = freezed,
    Object? secondaryDns = freezed,
    Object? isDhcp = freezed,
    Object? mtu = freezed,
    Object? advancedSettings = freezed,
  }) {
    return _then(_$NetworkConfigImpl(
      ssid: null == ssid
          ? _value.ssid
          : ssid // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as SecurityType,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      gateway: freezed == gateway
          ? _value.gateway
          : gateway // ignore: cast_nullable_to_non_nullable
              as String?,
      subnetMask: freezed == subnetMask
          ? _value.subnetMask
          : subnetMask // ignore: cast_nullable_to_non_nullable
              as String?,
      primaryDns: freezed == primaryDns
          ? _value.primaryDns
          : primaryDns // ignore: cast_nullable_to_non_nullable
              as String?,
      secondaryDns: freezed == secondaryDns
          ? _value.secondaryDns
          : secondaryDns // ignore: cast_nullable_to_non_nullable
              as String?,
      isDhcp: freezed == isDhcp
          ? _value.isDhcp
          : isDhcp // ignore: cast_nullable_to_non_nullable
              as bool?,
      mtu: freezed == mtu
          ? _value.mtu
          : mtu // ignore: cast_nullable_to_non_nullable
              as int?,
      advancedSettings: freezed == advancedSettings
          ? _value._advancedSettings
          : advancedSettings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NetworkConfigImpl implements _NetworkConfig {
  const _$NetworkConfigImpl(
      {required this.ssid,
      required this.password,
      required this.securityType,
      this.ipAddress,
      this.gateway,
      this.subnetMask,
      this.primaryDns,
      this.secondaryDns,
      this.isDhcp,
      this.mtu,
      final Map<String, dynamic>? advancedSettings})
      : _advancedSettings = advancedSettings;

  factory _$NetworkConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$NetworkConfigImplFromJson(json);

  @override
  final String ssid;
  @override
  final String password;
  @override
  final SecurityType securityType;
  @override
  final String? ipAddress;
  @override
  final String? gateway;
  @override
  final String? subnetMask;
  @override
  final String? primaryDns;
  @override
  final String? secondaryDns;
  @override
  final bool? isDhcp;
  @override
  final int? mtu;
  final Map<String, dynamic>? _advancedSettings;
  @override
  Map<String, dynamic>? get advancedSettings {
    final value = _advancedSettings;
    if (value == null) return null;
    if (_advancedSettings is EqualUnmodifiableMapView) return _advancedSettings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'NetworkConfig(ssid: $ssid, password: $password, securityType: $securityType, ipAddress: $ipAddress, gateway: $gateway, subnetMask: $subnetMask, primaryDns: $primaryDns, secondaryDns: $secondaryDns, isDhcp: $isDhcp, mtu: $mtu, advancedSettings: $advancedSettings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkConfigImpl &&
            (identical(other.ssid, ssid) || other.ssid == ssid) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.securityType, securityType) ||
                other.securityType == securityType) &&
            (identical(other.ipAddress, ipAddress) ||
                other.ipAddress == ipAddress) &&
            (identical(other.gateway, gateway) || other.gateway == gateway) &&
            (identical(other.subnetMask, subnetMask) ||
                other.subnetMask == subnetMask) &&
            (identical(other.primaryDns, primaryDns) ||
                other.primaryDns == primaryDns) &&
            (identical(other.secondaryDns, secondaryDns) ||
                other.secondaryDns == secondaryDns) &&
            (identical(other.isDhcp, isDhcp) || other.isDhcp == isDhcp) &&
            (identical(other.mtu, mtu) || other.mtu == mtu) &&
            const DeepCollectionEquality()
                .equals(other._advancedSettings, _advancedSettings));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      ssid,
      password,
      securityType,
      ipAddress,
      gateway,
      subnetMask,
      primaryDns,
      secondaryDns,
      isDhcp,
      mtu,
      const DeepCollectionEquality().hash(_advancedSettings));

  /// Create a copy of NetworkConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkConfigImplCopyWith<_$NetworkConfigImpl> get copyWith =>
      __$$NetworkConfigImplCopyWithImpl<_$NetworkConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NetworkConfigImplToJson(
      this,
    );
  }
}

abstract class _NetworkConfig implements NetworkConfig {
  const factory _NetworkConfig(
      {required final String ssid,
      required final String password,
      required final SecurityType securityType,
      final String? ipAddress,
      final String? gateway,
      final String? subnetMask,
      final String? primaryDns,
      final String? secondaryDns,
      final bool? isDhcp,
      final int? mtu,
      final Map<String, dynamic>? advancedSettings}) = _$NetworkConfigImpl;

  factory _NetworkConfig.fromJson(Map<String, dynamic> json) =
      _$NetworkConfigImpl.fromJson;

  @override
  String get ssid;
  @override
  String get password;
  @override
  SecurityType get securityType;
  @override
  String? get ipAddress;
  @override
  String? get gateway;
  @override
  String? get subnetMask;
  @override
  String? get primaryDns;
  @override
  String? get secondaryDns;
  @override
  bool? get isDhcp;
  @override
  int? get mtu;
  @override
  Map<String, dynamic>? get advancedSettings;

  /// Create a copy of NetworkConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkConfigImplCopyWith<_$NetworkConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NetworkStatus _$NetworkStatusFromJson(Map<String, dynamic> json) {
  return _NetworkStatus.fromJson(json);
}

/// @nodoc
mixin _$NetworkStatus {
  bool get isConnected => throw _privateConstructorUsedError;
  bool get hasInternet => throw _privateConstructorUsedError;
  String? get connectedSsid => throw _privateConstructorUsedError;
  String? get ipAddress => throw _privateConstructorUsedError;
  String? get gateway => throw _privateConstructorUsedError;
  int? get signalStrength => throw _privateConstructorUsedError;
  double? get linkSpeed => throw _privateConstructorUsedError;
  DateTime? get connectedAt => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this NetworkStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NetworkStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NetworkStatusCopyWith<NetworkStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NetworkStatusCopyWith<$Res> {
  factory $NetworkStatusCopyWith(
          NetworkStatus value, $Res Function(NetworkStatus) then) =
      _$NetworkStatusCopyWithImpl<$Res, NetworkStatus>;
  @useResult
  $Res call(
      {bool isConnected,
      bool hasInternet,
      String? connectedSsid,
      String? ipAddress,
      String? gateway,
      int? signalStrength,
      double? linkSpeed,
      DateTime? connectedAt,
      String? errorMessage});
}

/// @nodoc
class _$NetworkStatusCopyWithImpl<$Res, $Val extends NetworkStatus>
    implements $NetworkStatusCopyWith<$Res> {
  _$NetworkStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NetworkStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isConnected = null,
    Object? hasInternet = null,
    Object? connectedSsid = freezed,
    Object? ipAddress = freezed,
    Object? gateway = freezed,
    Object? signalStrength = freezed,
    Object? linkSpeed = freezed,
    Object? connectedAt = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      hasInternet: null == hasInternet
          ? _value.hasInternet
          : hasInternet // ignore: cast_nullable_to_non_nullable
              as bool,
      connectedSsid: freezed == connectedSsid
          ? _value.connectedSsid
          : connectedSsid // ignore: cast_nullable_to_non_nullable
              as String?,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      gateway: freezed == gateway
          ? _value.gateway
          : gateway // ignore: cast_nullable_to_non_nullable
              as String?,
      signalStrength: freezed == signalStrength
          ? _value.signalStrength
          : signalStrength // ignore: cast_nullable_to_non_nullable
              as int?,
      linkSpeed: freezed == linkSpeed
          ? _value.linkSpeed
          : linkSpeed // ignore: cast_nullable_to_non_nullable
              as double?,
      connectedAt: freezed == connectedAt
          ? _value.connectedAt
          : connectedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NetworkStatusImplCopyWith<$Res>
    implements $NetworkStatusCopyWith<$Res> {
  factory _$$NetworkStatusImplCopyWith(
          _$NetworkStatusImpl value, $Res Function(_$NetworkStatusImpl) then) =
      __$$NetworkStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isConnected,
      bool hasInternet,
      String? connectedSsid,
      String? ipAddress,
      String? gateway,
      int? signalStrength,
      double? linkSpeed,
      DateTime? connectedAt,
      String? errorMessage});
}

/// @nodoc
class __$$NetworkStatusImplCopyWithImpl<$Res>
    extends _$NetworkStatusCopyWithImpl<$Res, _$NetworkStatusImpl>
    implements _$$NetworkStatusImplCopyWith<$Res> {
  __$$NetworkStatusImplCopyWithImpl(
      _$NetworkStatusImpl _value, $Res Function(_$NetworkStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of NetworkStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isConnected = null,
    Object? hasInternet = null,
    Object? connectedSsid = freezed,
    Object? ipAddress = freezed,
    Object? gateway = freezed,
    Object? signalStrength = freezed,
    Object? linkSpeed = freezed,
    Object? connectedAt = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(_$NetworkStatusImpl(
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      hasInternet: null == hasInternet
          ? _value.hasInternet
          : hasInternet // ignore: cast_nullable_to_non_nullable
              as bool,
      connectedSsid: freezed == connectedSsid
          ? _value.connectedSsid
          : connectedSsid // ignore: cast_nullable_to_non_nullable
              as String?,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      gateway: freezed == gateway
          ? _value.gateway
          : gateway // ignore: cast_nullable_to_non_nullable
              as String?,
      signalStrength: freezed == signalStrength
          ? _value.signalStrength
          : signalStrength // ignore: cast_nullable_to_non_nullable
              as int?,
      linkSpeed: freezed == linkSpeed
          ? _value.linkSpeed
          : linkSpeed // ignore: cast_nullable_to_non_nullable
              as double?,
      connectedAt: freezed == connectedAt
          ? _value.connectedAt
          : connectedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NetworkStatusImpl implements _NetworkStatus {
  const _$NetworkStatusImpl(
      {required this.isConnected,
      required this.hasInternet,
      this.connectedSsid,
      this.ipAddress,
      this.gateway,
      this.signalStrength,
      this.linkSpeed,
      this.connectedAt,
      this.errorMessage});

  factory _$NetworkStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$NetworkStatusImplFromJson(json);

  @override
  final bool isConnected;
  @override
  final bool hasInternet;
  @override
  final String? connectedSsid;
  @override
  final String? ipAddress;
  @override
  final String? gateway;
  @override
  final int? signalStrength;
  @override
  final double? linkSpeed;
  @override
  final DateTime? connectedAt;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'NetworkStatus(isConnected: $isConnected, hasInternet: $hasInternet, connectedSsid: $connectedSsid, ipAddress: $ipAddress, gateway: $gateway, signalStrength: $signalStrength, linkSpeed: $linkSpeed, connectedAt: $connectedAt, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkStatusImpl &&
            (identical(other.isConnected, isConnected) ||
                other.isConnected == isConnected) &&
            (identical(other.hasInternet, hasInternet) ||
                other.hasInternet == hasInternet) &&
            (identical(other.connectedSsid, connectedSsid) ||
                other.connectedSsid == connectedSsid) &&
            (identical(other.ipAddress, ipAddress) ||
                other.ipAddress == ipAddress) &&
            (identical(other.gateway, gateway) || other.gateway == gateway) &&
            (identical(other.signalStrength, signalStrength) ||
                other.signalStrength == signalStrength) &&
            (identical(other.linkSpeed, linkSpeed) ||
                other.linkSpeed == linkSpeed) &&
            (identical(other.connectedAt, connectedAt) ||
                other.connectedAt == connectedAt) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isConnected,
      hasInternet,
      connectedSsid,
      ipAddress,
      gateway,
      signalStrength,
      linkSpeed,
      connectedAt,
      errorMessage);

  /// Create a copy of NetworkStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkStatusImplCopyWith<_$NetworkStatusImpl> get copyWith =>
      __$$NetworkStatusImplCopyWithImpl<_$NetworkStatusImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NetworkStatusImplToJson(
      this,
    );
  }
}

abstract class _NetworkStatus implements NetworkStatus {
  const factory _NetworkStatus(
      {required final bool isConnected,
      required final bool hasInternet,
      final String? connectedSsid,
      final String? ipAddress,
      final String? gateway,
      final int? signalStrength,
      final double? linkSpeed,
      final DateTime? connectedAt,
      final String? errorMessage}) = _$NetworkStatusImpl;

  factory _NetworkStatus.fromJson(Map<String, dynamic> json) =
      _$NetworkStatusImpl.fromJson;

  @override
  bool get isConnected;
  @override
  bool get hasInternet;
  @override
  String? get connectedSsid;
  @override
  String? get ipAddress;
  @override
  String? get gateway;
  @override
  int? get signalStrength;
  @override
  double? get linkSpeed;
  @override
  DateTime? get connectedAt;
  @override
  String? get errorMessage;

  /// Create a copy of NetworkStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkStatusImplCopyWith<_$NetworkStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GridConfig _$GridConfigFromJson(Map<String, dynamic> json) {
  return _GridConfig.fromJson(json);
}

/// @nodoc
mixin _$GridConfig {
  double get maxCurrent => throw _privateConstructorUsedError;
  double get maxPower => throw _privateConstructorUsedError;
  double get voltage => throw _privateConstructorUsedError;
  int get phases => throw _privateConstructorUsedError;
  double? get frequency => throw _privateConstructorUsedError;
  double? get powerFactor => throw _privateConstructorUsedError;
  bool? get isThreePhase => throw _privateConstructorUsedError;
  Map<String, dynamic>? get localRegulations =>
      throw _privateConstructorUsedError;

  /// Serializes this GridConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GridConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GridConfigCopyWith<GridConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GridConfigCopyWith<$Res> {
  factory $GridConfigCopyWith(
          GridConfig value, $Res Function(GridConfig) then) =
      _$GridConfigCopyWithImpl<$Res, GridConfig>;
  @useResult
  $Res call(
      {double maxCurrent,
      double maxPower,
      double voltage,
      int phases,
      double? frequency,
      double? powerFactor,
      bool? isThreePhase,
      Map<String, dynamic>? localRegulations});
}

/// @nodoc
class _$GridConfigCopyWithImpl<$Res, $Val extends GridConfig>
    implements $GridConfigCopyWith<$Res> {
  _$GridConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GridConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxCurrent = null,
    Object? maxPower = null,
    Object? voltage = null,
    Object? phases = null,
    Object? frequency = freezed,
    Object? powerFactor = freezed,
    Object? isThreePhase = freezed,
    Object? localRegulations = freezed,
  }) {
    return _then(_value.copyWith(
      maxCurrent: null == maxCurrent
          ? _value.maxCurrent
          : maxCurrent // ignore: cast_nullable_to_non_nullable
              as double,
      maxPower: null == maxPower
          ? _value.maxPower
          : maxPower // ignore: cast_nullable_to_non_nullable
              as double,
      voltage: null == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as double,
      phases: null == phases
          ? _value.phases
          : phases // ignore: cast_nullable_to_non_nullable
              as int,
      frequency: freezed == frequency
          ? _value.frequency
          : frequency // ignore: cast_nullable_to_non_nullable
              as double?,
      powerFactor: freezed == powerFactor
          ? _value.powerFactor
          : powerFactor // ignore: cast_nullable_to_non_nullable
              as double?,
      isThreePhase: freezed == isThreePhase
          ? _value.isThreePhase
          : isThreePhase // ignore: cast_nullable_to_non_nullable
              as bool?,
      localRegulations: freezed == localRegulations
          ? _value.localRegulations
          : localRegulations // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GridConfigImplCopyWith<$Res>
    implements $GridConfigCopyWith<$Res> {
  factory _$$GridConfigImplCopyWith(
          _$GridConfigImpl value, $Res Function(_$GridConfigImpl) then) =
      __$$GridConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double maxCurrent,
      double maxPower,
      double voltage,
      int phases,
      double? frequency,
      double? powerFactor,
      bool? isThreePhase,
      Map<String, dynamic>? localRegulations});
}

/// @nodoc
class __$$GridConfigImplCopyWithImpl<$Res>
    extends _$GridConfigCopyWithImpl<$Res, _$GridConfigImpl>
    implements _$$GridConfigImplCopyWith<$Res> {
  __$$GridConfigImplCopyWithImpl(
      _$GridConfigImpl _value, $Res Function(_$GridConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of GridConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxCurrent = null,
    Object? maxPower = null,
    Object? voltage = null,
    Object? phases = null,
    Object? frequency = freezed,
    Object? powerFactor = freezed,
    Object? isThreePhase = freezed,
    Object? localRegulations = freezed,
  }) {
    return _then(_$GridConfigImpl(
      maxCurrent: null == maxCurrent
          ? _value.maxCurrent
          : maxCurrent // ignore: cast_nullable_to_non_nullable
              as double,
      maxPower: null == maxPower
          ? _value.maxPower
          : maxPower // ignore: cast_nullable_to_non_nullable
              as double,
      voltage: null == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as double,
      phases: null == phases
          ? _value.phases
          : phases // ignore: cast_nullable_to_non_nullable
              as int,
      frequency: freezed == frequency
          ? _value.frequency
          : frequency // ignore: cast_nullable_to_non_nullable
              as double?,
      powerFactor: freezed == powerFactor
          ? _value.powerFactor
          : powerFactor // ignore: cast_nullable_to_non_nullable
              as double?,
      isThreePhase: freezed == isThreePhase
          ? _value.isThreePhase
          : isThreePhase // ignore: cast_nullable_to_non_nullable
              as bool?,
      localRegulations: freezed == localRegulations
          ? _value._localRegulations
          : localRegulations // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GridConfigImpl implements _GridConfig {
  const _$GridConfigImpl(
      {required this.maxCurrent,
      required this.maxPower,
      required this.voltage,
      required this.phases,
      this.frequency,
      this.powerFactor,
      this.isThreePhase,
      final Map<String, dynamic>? localRegulations})
      : _localRegulations = localRegulations;

  factory _$GridConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$GridConfigImplFromJson(json);

  @override
  final double maxCurrent;
  @override
  final double maxPower;
  @override
  final double voltage;
  @override
  final int phases;
  @override
  final double? frequency;
  @override
  final double? powerFactor;
  @override
  final bool? isThreePhase;
  final Map<String, dynamic>? _localRegulations;
  @override
  Map<String, dynamic>? get localRegulations {
    final value = _localRegulations;
    if (value == null) return null;
    if (_localRegulations is EqualUnmodifiableMapView) return _localRegulations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'GridConfig(maxCurrent: $maxCurrent, maxPower: $maxPower, voltage: $voltage, phases: $phases, frequency: $frequency, powerFactor: $powerFactor, isThreePhase: $isThreePhase, localRegulations: $localRegulations)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GridConfigImpl &&
            (identical(other.maxCurrent, maxCurrent) ||
                other.maxCurrent == maxCurrent) &&
            (identical(other.maxPower, maxPower) ||
                other.maxPower == maxPower) &&
            (identical(other.voltage, voltage) || other.voltage == voltage) &&
            (identical(other.phases, phases) || other.phases == phases) &&
            (identical(other.frequency, frequency) ||
                other.frequency == frequency) &&
            (identical(other.powerFactor, powerFactor) ||
                other.powerFactor == powerFactor) &&
            (identical(other.isThreePhase, isThreePhase) ||
                other.isThreePhase == isThreePhase) &&
            const DeepCollectionEquality()
                .equals(other._localRegulations, _localRegulations));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      maxCurrent,
      maxPower,
      voltage,
      phases,
      frequency,
      powerFactor,
      isThreePhase,
      const DeepCollectionEquality().hash(_localRegulations));

  /// Create a copy of GridConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GridConfigImplCopyWith<_$GridConfigImpl> get copyWith =>
      __$$GridConfigImplCopyWithImpl<_$GridConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GridConfigImplToJson(
      this,
    );
  }
}

abstract class _GridConfig implements GridConfig {
  const factory _GridConfig(
      {required final double maxCurrent,
      required final double maxPower,
      required final double voltage,
      required final int phases,
      final double? frequency,
      final double? powerFactor,
      final bool? isThreePhase,
      final Map<String, dynamic>? localRegulations}) = _$GridConfigImpl;

  factory _GridConfig.fromJson(Map<String, dynamic> json) =
      _$GridConfigImpl.fromJson;

  @override
  double get maxCurrent;
  @override
  double get maxPower;
  @override
  double get voltage;
  @override
  int get phases;
  @override
  double? get frequency;
  @override
  double? get powerFactor;
  @override
  bool? get isThreePhase;
  @override
  Map<String, dynamic>? get localRegulations;

  /// Create a copy of GridConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GridConfigImplCopyWith<_$GridConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
