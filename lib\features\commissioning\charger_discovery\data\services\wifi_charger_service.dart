import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:wifi_scan/wifi_scan.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../shared/domain/entities/charger_info.dart';

/// Service for discovering EV chargers over WiFi networks
class WiFiChargerService {
  static const String _ocppServicePort = '8080';
  static const String _httpServicePort = '80';
  static const String _httpsServicePort = '443';
  static const List<String> _commonOcppPorts = ['8080', '9000', '8443', '443', '80'];
  
  final Dio _dio = Dio();
  final NetworkInfo _networkInfo = NetworkInfo();
  
  final StreamController<List<DiscoveredCharger>> _discoveredChargersController =
      StreamController<List<DiscoveredCharger>>.broadcast();
  
  final StreamController<String> _scanStatusController =
      StreamController<String>.broadcast();
  
  final List<DiscoveredCharger> _discoveredChargers = [];
  bool _isScanning = false;
  Timer? _scanTimer;
  
  /// Stream of discovered chargers
  Stream<List<DiscoveredCharger>> get discoveredChargersStream =>
      _discoveredChargersController.stream;
  
  /// Stream of scan status updates
  Stream<String> get scanStatusStream => _scanStatusController.stream;
  
  /// Check if currently scanning
  bool get isScanning => _isScanning;
  
  /// Get list of discovered chargers
  List<DiscoveredCharger> get discoveredChargers => List.unmodifiable(_discoveredChargers);
  
  /// Initialize the WiFi service
  Future<void> initialize() async {
    _dio.options.connectTimeout = const Duration(seconds: 5);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
    _dio.options.sendTimeout = const Duration(seconds: 10);
  }
  
  /// Start scanning for WiFi-connected OCPP chargers
  Future<void> startScanning({Duration timeout = const Duration(seconds: 30)}) async {
    if (_isScanning) {
      return;
    }
    
    _isScanning = true;
    _discoveredChargers.clear();
    _discoveredChargersController.add([]);
    _scanStatusController.add('Starting WiFi scan...');
    
    try {
      // Check permissions
      await _checkPermissions();
      
      // Get current network info
      final networkInfo = await _getCurrentNetworkInfo();
      if (networkInfo == null) {
        throw Exception('Not connected to WiFi network');
      }
      
      _scanStatusController.add('Scanning network: ${networkInfo['ssid']}');
      
      // Start network scanning
      await _scanNetworkDevices(networkInfo);
      
      // Set timeout
      _scanTimer = Timer(timeout, () {
        if (_isScanning) {
          stopScanning();
        }
      });
      
    } catch (e) {
      _scanStatusController.add('Scan failed: $e');
      _isScanning = false;
      rethrow;
    }
  }
  
  /// Stop scanning
  void stopScanning() {
    _isScanning = false;
    _scanTimer?.cancel();
    _scanTimer = null;
    _scanStatusController.add('Scan stopped');
  }
  
  /// Check required permissions
  Future<void> _checkPermissions() async {
    final permissions = [
      Permission.location,
      Permission.locationWhenInUse,
    ];
    
    for (final permission in permissions) {
      final status = await permission.status;
      if (!status.isGranted) {
        final result = await permission.request();
        if (!result.isGranted) {
          throw Exception('Location permission required for WiFi scanning');
        }
      }
    }
  }
  
  /// Get current network information
  Future<Map<String, dynamic>?> _getCurrentNetworkInfo() async {
    try {
      final wifiName = await _networkInfo.getWifiName();
      final wifiIP = await _networkInfo.getWifiIP();
      final wifiBSSID = await _networkInfo.getWifiBSSID();
      final wifiGatewayIP = await _networkInfo.getWifiGatewayIP();
      
      if (wifiName == null || wifiIP == null) {
        return null;
      }
      
      return {
        'ssid': wifiName.replaceAll('"', ''),
        'ip': wifiIP,
        'bssid': wifiBSSID,
        'gateway': wifiGatewayIP,
      };
    } catch (e) {
      return null;
    }
  }
  
  /// Scan network for potential OCPP devices
  Future<void> _scanNetworkDevices(Map<String, dynamic> networkInfo) async {
    final baseIP = _getNetworkBase(networkInfo['ip'] as String);
    if (baseIP == null) {
      throw Exception('Invalid network configuration');
    }
    
    _scanStatusController.add('Scanning IP range: $baseIP.1-254');
    
    // Scan IP range in parallel
    final futures = <Future>[];
    for (int i = 1; i <= 254; i++) {
      final ip = '$baseIP.$i';
      futures.add(_scanDevice(ip));
      
      // Process in batches to avoid overwhelming the network
      if (futures.length >= 20) {
        await Future.wait(futures);
        futures.clear();
        
        if (!_isScanning) break;
        
        // Small delay between batches
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }
    
    // Process remaining futures
    if (futures.isNotEmpty) {
      await Future.wait(futures);
    }
    
    _scanStatusController.add('Network scan completed. Found ${_discoveredChargers.length} devices');
  }
  
  /// Extract network base from IP address
  String? _getNetworkBase(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return null;
    
    return '${parts[0]}.${parts[1]}.${parts[2]}';
  }
  
  /// Scan individual device for OCPP services
  Future<void> _scanDevice(String ip) async {
    if (!_isScanning) return;

    try {
      // Try to detect OCPP services on common ports
      for (final port in _commonOcppPorts) {
        if (!_isScanning) break;

        final charger = await _probeOcppService(ip, port);
        if (charger != null) {
          _discoveredChargers.add(charger);
          _discoveredChargersController.add(List.from(_discoveredChargers));
          break; // Found OCPP service, no need to check other ports
        }
      }
    } catch (e) {
      // Ignore individual device scan errors
    }
  }

  /// Probe device for OCPP service
  Future<DiscoveredCharger?> _probeOcppService(String ip, String port) async {
    try {
      // First try HTTP probe
      final httpCharger = await _probeHttpService(ip, port);
      if (httpCharger != null) return httpCharger;

      // Then try WebSocket probe for OCPP
      final wsCharger = await _probeWebSocketService(ip, port);
      if (wsCharger != null) return wsCharger;

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Probe HTTP service for charger identification
  Future<DiscoveredCharger?> _probeHttpService(String ip, String port) async {
    try {
      final response = await _dio.get(
        'http://$ip:$port',
        options: Options(
          headers: {'User-Agent': 'EV-Commissioning-App/1.0'},
          validateStatus: (status) => status != null && status < 500,
        ),
      );

      // Check response for OCPP/EV charger indicators
      final content = response.data?.toString().toLowerCase() ?? '';
      final headers = response.headers.map;

      if (_isOcppDevice(content, headers)) {
        return _createDiscoveredCharger(ip, port, response);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Probe WebSocket service for OCPP
  Future<DiscoveredCharger?> _probeWebSocketService(String ip, String port) async {
    try {
      final socket = await Socket.connect(ip, int.parse(port), timeout: const Duration(seconds: 3));

      // Send basic WebSocket handshake to check if it's a WebSocket server
      final handshake = 'GET / HTTP/1.1\r\n'
          'Host: $ip:$port\r\n'
          'Upgrade: websocket\r\n'
          'Connection: Upgrade\r\n'
          'Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==\r\n'
          'Sec-WebSocket-Version: 13\r\n'
          'Sec-WebSocket-Protocol: ocpp1.6, ocpp2.0\r\n'
          '\r\n';

      socket.write(handshake);

      final responseBytes = await socket.take(1).first
          .timeout(const Duration(seconds: 2));
      final response = utf8.decode(responseBytes);

      socket.destroy();

      if (response.contains('websocket') || response.contains('ocpp')) {
        return DiscoveredCharger(
          id: 'wifi_${ip.replaceAll('.', '_')}_$port',
          name: 'OCPP Charger',
          serialNumber: 'WIFI_${ip.replaceAll('.', '')}_$port',
          signalStrength: -30, // Good WiFi signal
          connectionType: 'wifi',
          ipAddress: ip,
          discoveredAt: DateTime.now(),
          advertisementData: {
            'ip': ip,
            'port': port,
            'protocol': 'websocket',
            'ocpp_detected': true,
          },
        );
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Check if response indicates OCPP/EV charger device
  bool _isOcppDevice(String content, Map<String, List<String>> headers) {
    // Check content for OCPP/EV charger keywords
    final ocppKeywords = [
      'ocpp', 'chargepoint', 'charging', 'evse', 'electric vehicle',
      'wallbox', 'abb', 'schneider', 'siemens', 'tesla', 'charger',
      'keba', 'phoenix', 'mennekes', 'ccs', 'chademo'
    ];

    for (final keyword in ocppKeywords) {
      if (content.contains(keyword)) {
        return true;
      }
    }

    // Check headers for server information
    final serverHeader = headers['server']?.first?.toLowerCase() ?? '';
    if (serverHeader.contains('charger') ||
        serverHeader.contains('ocpp') ||
        serverHeader.contains('evse')) {
      return true;
    }

    return false;
  }

  /// Create discovered charger from HTTP response
  DiscoveredCharger _createDiscoveredCharger(String ip, String port, Response response) {
    final content = response.data?.toString() ?? '';
    final headers = response.headers.map;

    // Extract device information
    final deviceInfo = _extractDeviceInfo(content, headers);

    return DiscoveredCharger(
      id: 'wifi_${ip.replaceAll('.', '_')}_$port',
      name: deviceInfo['name'] ?? 'WiFi OCPP Charger',
      serialNumber: deviceInfo['serial'] ?? 'WIFI_${ip.replaceAll('.', '')}_$port',
      signalStrength: -25, // Excellent WiFi signal
      connectionType: 'wifi',
      ipAddress: ip,
      discoveredAt: DateTime.now(),
      advertisementData: {
        'ip': ip,
        'port': port,
        'manufacturer': deviceInfo['manufacturer'],
        'model': deviceInfo['model'],
        'firmware': deviceInfo['firmware'],
        'protocol': 'http',
        'response_time': response.extra['response_time'],
      },
    );
  }

  /// Extract device information from response
  Map<String, String?> _extractDeviceInfo(String content, Map<String, List<String>> headers) {
    final info = <String, String?>{};

    // Try to extract from HTML title
    final titleMatch = RegExp(r'<title[^>]*>([^<]+)</title>', caseSensitive: false)
        .firstMatch(content);
    if (titleMatch != null) {
      info['name'] = titleMatch.group(1)?.trim();
    }

    // Try to extract manufacturer from common patterns
    final manufacturers = ['ABB', 'Schneider', 'Siemens', 'Tesla', 'Wallbox', 'KEBA', 'Phoenix'];
    for (final manufacturer in manufacturers) {
      if (content.toLowerCase().contains(manufacturer.toLowerCase())) {
        info['manufacturer'] = manufacturer;
        break;
      }
    }

    // Extract server information
    final serverHeader = headers['server']?.first;
    if (serverHeader != null) {
      info['server'] = serverHeader;
    }

    return info;
  }

  /// Dispose resources
  void dispose() {
    stopScanning();
    _discoveredChargersController.close();
    _scanStatusController.close();
    _dio.close();
  }
}
