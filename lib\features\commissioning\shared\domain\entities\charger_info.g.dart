// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'charger_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ConnectorImpl _$$ConnectorImplFromJson(Map<String, dynamic> json) =>
    _$ConnectorImpl(
      id: json['id'] as String,
      type: $enumDecode(_$ChargerTypeEnumMap, json['type']),
      maxPower: (json['maxPower'] as num).toDouble(),
      maxCurrent: (json['maxCurrent'] as num).toDouble(),
      status: $enumDecode(_$ChargerStatusEnumMap, json['status']),
      currentTransactionId: json['currentTransactionId'] as String?,
      lastUsed: json['lastUsed'] == null
          ? null
          : DateTime.parse(json['lastUsed'] as String),
    );

Map<String, dynamic> _$$ConnectorImplToJson(_$ConnectorImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$ChargerTypeEnumMap[instance.type]!,
      'maxPower': instance.maxPower,
      'maxCurrent': instance.maxCurrent,
      'status': _$ChargerStatusEnumMap[instance.status]!,
      'currentTransactionId': instance.currentTransactionId,
      'lastUsed': instance.lastUsed?.toIso8601String(),
    };

const _$ChargerTypeEnumMap = {
  ChargerType.acType1: 'ac_type1',
  ChargerType.acType2: 'ac_type2',
  ChargerType.dcCcs: 'dc_ccs',
  ChargerType.dcChademo: 'dc_chademo',
  ChargerType.dcCombo: 'dc_combo',
};

const _$ChargerStatusEnumMap = {
  ChargerStatus.available: 'available',
  ChargerStatus.occupied: 'occupied',
  ChargerStatus.reserved: 'reserved',
  ChargerStatus.unavailable: 'unavailable',
  ChargerStatus.faulted: 'faulted',
  ChargerStatus.preparing: 'preparing',
  ChargerStatus.charging: 'charging',
  ChargerStatus.suspendedEvse: 'suspended_evse',
  ChargerStatus.suspendedEv: 'suspended_ev',
  ChargerStatus.finishing: 'finishing',
  ChargerStatus.offline: 'offline',
};

_$ChargerInfoImpl _$$ChargerInfoImplFromJson(Map<String, dynamic> json) =>
    _$ChargerInfoImpl(
      id: json['id'] as String,
      serialNumber: json['serialNumber'] as String,
      model: json['model'] as String,
      manufacturer: json['manufacturer'] as String,
      firmwareVersion: json['firmwareVersion'] as String,
      connectors: (json['connectors'] as List<dynamic>)
          .map((e) => Connector.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: $enumDecode(_$ChargerStatusEnumMap, json['status']),
      lastSeen: DateTime.parse(json['lastSeen'] as String),
      location: json['location'] as String?,
      description: json['description'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$ChargerInfoImplToJson(_$ChargerInfoImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'serialNumber': instance.serialNumber,
      'model': instance.model,
      'manufacturer': instance.manufacturer,
      'firmwareVersion': instance.firmwareVersion,
      'connectors': instance.connectors,
      'status': _$ChargerStatusEnumMap[instance.status]!,
      'lastSeen': instance.lastSeen.toIso8601String(),
      'location': instance.location,
      'description': instance.description,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$DiscoveredChargerImpl _$$DiscoveredChargerImplFromJson(
        Map<String, dynamic> json) =>
    _$DiscoveredChargerImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      serialNumber: json['serialNumber'] as String,
      signalStrength: (json['signalStrength'] as num).toInt(),
      connectionType: json['connectionType'] as String,
      macAddress: json['macAddress'] as String?,
      ipAddress: json['ipAddress'] as String?,
      advertisementData: json['advertisementData'] as Map<String, dynamic>?,
      discoveredAt: json['discoveredAt'] == null
          ? null
          : DateTime.parse(json['discoveredAt'] as String),
    );

Map<String, dynamic> _$$DiscoveredChargerImplToJson(
        _$DiscoveredChargerImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'serialNumber': instance.serialNumber,
      'signalStrength': instance.signalStrength,
      'connectionType': instance.connectionType,
      'macAddress': instance.macAddress,
      'ipAddress': instance.ipAddress,
      'advertisementData': instance.advertisementData,
      'discoveredAt': instance.discoveredAt?.toIso8601String(),
    };

_$ChargerConnectionImpl _$$ChargerConnectionImplFromJson(
        Map<String, dynamic> json) =>
    _$ChargerConnectionImpl(
      chargerId: json['chargerId'] as String,
      connectionType: json['connectionType'] as String,
      isConnected: json['isConnected'] as bool,
      connectedAt: DateTime.parse(json['connectedAt'] as String),
      sessionId: json['sessionId'] as String?,
      connectionParams: json['connectionParams'] as Map<String, dynamic>?,
      lastActivity: json['lastActivity'] == null
          ? null
          : DateTime.parse(json['lastActivity'] as String),
    );

Map<String, dynamic> _$$ChargerConnectionImplToJson(
        _$ChargerConnectionImpl instance) =>
    <String, dynamic>{
      'chargerId': instance.chargerId,
      'connectionType': instance.connectionType,
      'isConnected': instance.isConnected,
      'connectedAt': instance.connectedAt.toIso8601String(),
      'sessionId': instance.sessionId,
      'connectionParams': instance.connectionParams,
      'lastActivity': instance.lastActivity?.toIso8601String(),
    };
