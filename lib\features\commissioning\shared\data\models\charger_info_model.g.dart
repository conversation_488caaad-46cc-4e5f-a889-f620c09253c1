// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'charger_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConnectorModel _$ConnectorModelFromJson(Map<String, dynamic> json) =>
    ConnectorModel(
      id: json['id'] as String,
      type: $enumDecode(_$ChargerTypeEnumMap, json['type']),
      maxPower: (json['max_power'] as num).toDouble(),
      maxCurrent: (json['max_current'] as num).toDouble(),
      status: $enumDecode(_$ChargerStatusEnumMap, json['status']),
      currentTransactionId: json['current_transaction_id'] as String?,
      lastUsed: json['last_used'] == null
          ? null
          : DateTime.parse(json['last_used'] as String),
    );

Map<String, dynamic> _$ConnectorModelToJson(ConnectorModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$ChargerTypeEnumMap[instance.type]!,
      'max_power': instance.maxPower,
      'max_current': instance.maxCurrent,
      'status': _$ChargerStatusEnumMap[instance.status]!,
      'current_transaction_id': instance.currentTransactionId,
      'last_used': instance.lastUsed?.toIso8601String(),
    };

const _$ChargerTypeEnumMap = {
  ChargerType.acType1: 'ac_type1',
  ChargerType.acType2: 'ac_type2',
  ChargerType.dcCcs: 'dc_ccs',
  ChargerType.dcChademo: 'dc_chademo',
  ChargerType.dcCombo: 'dc_combo',
};

const _$ChargerStatusEnumMap = {
  ChargerStatus.available: 'available',
  ChargerStatus.occupied: 'occupied',
  ChargerStatus.reserved: 'reserved',
  ChargerStatus.unavailable: 'unavailable',
  ChargerStatus.faulted: 'faulted',
  ChargerStatus.preparing: 'preparing',
  ChargerStatus.charging: 'charging',
  ChargerStatus.suspendedEvse: 'suspended_evse',
  ChargerStatus.suspendedEv: 'suspended_ev',
  ChargerStatus.finishing: 'finishing',
  ChargerStatus.offline: 'offline',
};

ChargerInfoModel _$ChargerInfoModelFromJson(Map<String, dynamic> json) =>
    ChargerInfoModel(
      id: json['id'] as String,
      serialNumber: json['serial_number'] as String,
      model: json['model'] as String,
      manufacturer: json['manufacturer'] as String,
      firmwareVersion: json['firmware_version'] as String,
      connectors: (json['connectors'] as List<dynamic>)
          .map((e) => ConnectorModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: $enumDecode(_$ChargerStatusEnumMap, json['status']),
      lastSeen: DateTime.parse(json['last_seen'] as String),
      location: json['location'] as String?,
      description: json['description'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ChargerInfoModelToJson(ChargerInfoModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'serial_number': instance.serialNumber,
      'model': instance.model,
      'manufacturer': instance.manufacturer,
      'firmware_version': instance.firmwareVersion,
      'connectors': instance.connectors,
      'status': _$ChargerStatusEnumMap[instance.status]!,
      'last_seen': instance.lastSeen.toIso8601String(),
      'location': instance.location,
      'description': instance.description,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'metadata': instance.metadata,
    };

DiscoveredChargerModel _$DiscoveredChargerModelFromJson(
        Map<String, dynamic> json) =>
    DiscoveredChargerModel(
      id: json['id'] as String,
      name: json['name'] as String,
      serialNumber: json['serial_number'] as String,
      signalStrength: (json['signal_strength'] as num).toInt(),
      connectionType: json['connection_type'] as String,
      macAddress: json['mac_address'] as String?,
      ipAddress: json['ip_address'] as String?,
      advertisementData: json['advertisement_data'] as Map<String, dynamic>?,
      discoveredAt: json['discovered_at'] == null
          ? null
          : DateTime.parse(json['discovered_at'] as String),
    );

Map<String, dynamic> _$DiscoveredChargerModelToJson(
        DiscoveredChargerModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'serial_number': instance.serialNumber,
      'signal_strength': instance.signalStrength,
      'connection_type': instance.connectionType,
      'mac_address': instance.macAddress,
      'ip_address': instance.ipAddress,
      'advertisement_data': instance.advertisementData,
      'discovered_at': instance.discoveredAt?.toIso8601String(),
    };
