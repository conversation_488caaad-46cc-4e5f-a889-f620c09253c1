import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../domain/entities/user.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/repositories/auth_repository.dart';

part 'auth_event.dart';
part 'auth_state.dart';

/// BLoC for authentication management
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc({
    required LoginUseCase loginUseCase,
    required AuthRepository authRepository,
  })  : _loginUseCase = loginUseCase,
        _authRepository = authRepository,
        super(const AuthState.initial()) {
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthStatusChecked>(_onAuthStatusChecked);
    on<AuthTokenRefreshRequested>(_onTokenRefreshRequested);
    on<AuthBiometricLoginRequested>(_onBiometricLoginRequested);
    on<AuthSessionExtended>(_onSessionExtended);
    on<AuthPasswordChangeRequested>(_onPasswordChangeRequested);
  }

  final LoginUseCase _loginUseCase;
  final AuthRepository _authRepository;

  /// Handle login request
  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final result = await _loginUseCase(event.credentials);

    if (result.isSuccess) {
      final loginResult = result.data!;
      emit(state.copyWith(
        status: AuthStatus.authenticated,
        user: loginResult.user,
        tokens: loginResult.tokens,
        isFirstLogin: loginResult.isFirstLogin,
      ));
    } else {
      emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        error: result.failure!.message,
      ));
    }
  }

  /// Handle logout request
  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final result = await _authRepository.logout();

    if (result.isSuccess) {
      await _authRepository.clearAuthData();
      emit(const AuthState.initial());
    } else {
      emit(state.copyWith(
        status: AuthStatus.authenticated, // Keep authenticated on logout failure
        error: result.failure!.message,
      ));
    }
  }

  /// Check authentication status
  Future<void> _onAuthStatusChecked(
    AuthStatusChecked event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final isAuthenticatedResult = await _authRepository.isAuthenticated();
    
    if (isAuthenticatedResult.isSuccess && isAuthenticatedResult.data == true) {
      final userResult = await _authRepository.getCurrentUser();
      final tokensResult = await _authRepository.getStoredTokens();

      if (userResult.isSuccess && tokensResult.isSuccess) {
        emit(state.copyWith(
          status: AuthStatus.authenticated,
          user: userResult.data!,
          tokens: tokensResult.data,
        ));
      } else {
        emit(const AuthState.initial());
      }
    } else {
      emit(const AuthState.initial());
    }
  }

  /// Handle token refresh
  Future<void> _onTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (state.tokens == null) return;

    final result = await _authRepository.refreshTokens(state.tokens!.refreshToken);

    if (result.isSuccess) {
      final newTokens = result.data!;
      await _authRepository.storeTokens(newTokens);
      emit(state.copyWith(tokens: newTokens));
    } else {
      // Token refresh failed, logout user
      add(const AuthLogoutRequested());
    }
  }

  /// Handle biometric login
  Future<void> _onBiometricLoginRequested(
    AuthBiometricLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final biometricResult = await _authRepository.authenticateWithBiometrics();

    if (biometricResult.isSuccess && biometricResult.data == true) {
      // Get stored user data after successful biometric auth
      final userResult = await _authRepository.getCurrentUser();
      final tokensResult = await _authRepository.getStoredTokens();

      if (userResult.isSuccess && tokensResult.isSuccess) {
        emit(state.copyWith(
          status: AuthStatus.authenticated,
          user: userResult.data!,
          tokens: tokensResult.data,
        ));
      } else {
        emit(state.copyWith(
          status: AuthStatus.unauthenticated,
          error: 'Failed to retrieve user data',
        ));
      }
    } else {
      emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        error: 'Biometric authentication failed',
      ));
    }
  }

  /// Handle session extension
  Future<void> _onSessionExtended(
    AuthSessionExtended event,
    Emitter<AuthState> emit,
  ) async {
    await _authRepository.extendSession();
    await _authRepository.updateLastActivity();
  }

  /// Handle password change
  Future<void> _onPasswordChangeRequested(
    AuthPasswordChangeRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final result = await _authRepository.changePassword(
      event.currentPassword,
      event.newPassword,
    );

    if (result.isSuccess) {
      emit(state.copyWith(
        status: AuthStatus.authenticated,
        message: 'Password changed successfully',
      ));
    } else {
      emit(state.copyWith(
        status: AuthStatus.authenticated,
        error: result.failure!.message,
      ));
    }
  }
}
