import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../error/exceptions.dart';

/// Service for secure storage operations
class SecureStorageService {
  SecureStorageService(this._storage);

  final FlutterSecureStorage _storage;

  /// Store a value securely
  Future<void> store(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
    } catch (e) {
      throw CacheException(
        message: 'Failed to store data: $e',
        code: 'STORAGE_WRITE_ERROR',
      );
    }
  }

  /// Retrieve a value from secure storage
  Future<String?> retrieve(String key) async {
    try {
      return await _storage.read(key: key);
    } catch (e) {
      throw CacheException(
        message: 'Failed to retrieve data: $e',
        code: 'STORAGE_READ_ERROR',
      );
    }
  }

  /// Delete a value from secure storage
  Future<void> delete(String key) async {
    try {
      await _storage.delete(key: key);
    } catch (e) {
      throw CacheException(
        message: 'Failed to delete data: $e',
        code: 'STORAGE_DELETE_ERROR',
      );
    }
  }

  /// Clear all stored values
  Future<void> clear() async {
    try {
      await _storage.deleteAll();
    } catch (e) {
      throw CacheException(
        message: 'Failed to clear storage: $e',
        code: 'STORAGE_CLEAR_ERROR',
      );
    }
  }

  /// Check if a key exists
  Future<bool> containsKey(String key) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      throw CacheException(
        message: 'Failed to check key existence: $e',
        code: 'STORAGE_CHECK_ERROR',
      );
    }
  }

  /// Get all keys
  Future<Map<String, String>> getAll() async {
    try {
      return await _storage.readAll();
    } catch (e) {
      throw CacheException(
        message: 'Failed to get all data: $e',
        code: 'STORAGE_READ_ALL_ERROR',
      );
    }
  }

  /// Store JSON data
  Future<void> storeJson(String key, Map<String, dynamic> data) async {
    try {
      final jsonString = data.toString(); // In real app, use json.encode
      await store(key, jsonString);
    } catch (e) {
      throw CacheException(
        message: 'Failed to store JSON data: $e',
        code: 'STORAGE_JSON_WRITE_ERROR',
      );
    }
  }

  /// Retrieve JSON data
  Future<Map<String, dynamic>?> retrieveJson(String key) async {
    try {
      final jsonString = await retrieve(key);
      if (jsonString == null) return null;
      
      // In real app, use json.decode
      // For now, return empty map as placeholder
      return <String, dynamic>{};
    } catch (e) {
      throw CacheException(
        message: 'Failed to retrieve JSON data: $e',
        code: 'STORAGE_JSON_READ_ERROR',
      );
    }
  }
}
