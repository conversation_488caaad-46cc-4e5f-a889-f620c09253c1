import 'dart:async';
import 'dart:convert';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../../../shared/domain/entities/charger_info.dart';

/// Real Bluetooth service for EV charger discovery and configuration
class BluetoothChargerService {
  static const String _chargerServiceUuid =
      '6e400001-b5a3-f393-e0a9-e50e24dcca9e';
  static const String _configCharacteristicUuid =
      '6e400002-b5a3-f393-e0a9-e50e24dcca9e';
  static const String _statusCharacteristicUuid =
      '6e400003-b5a3-f393-e0a9-e50e24dcca9e';

  final StreamController<List<ChargerInfo>> _discoveredChargersController =
      StreamController.broadcast();
  final StreamController<String> _connectionStatusController =
      StreamController.broadcast();

  BluetoothDevice? _connectedDevice;
  BluetoothCharacteristic? _configCharacteristic;
  BluetoothCharacteristic? _statusCharacteristic;
  StreamSubscription? _scanSubscription;
  StreamSubscription? _statusSubscription;

  final List<ChargerInfo> _discoveredChargers = [];
  bool _isScanning = false;
  bool _isConnected = false;

  // Streams
  Stream<List<ChargerInfo>> get discoveredChargersStream =>
      _discoveredChargersController.stream;
  Stream<String> get connectionStatusStream =>
      _connectionStatusController.stream;
  List<ChargerInfo> get discoveredChargers =>
      List.unmodifiable(_discoveredChargers);
  bool get isScanning => _isScanning;
  bool get isConnected => _isConnected;

  /// Start scanning for EV chargers
  Future<void> startScanning({
    Duration timeout = const Duration(seconds: 30),
  }) async {
    if (_isScanning) return;

    try {
      _isScanning = true;
      _discoveredChargers.clear();
      _discoveredChargersController.add([]);

      // Check if Bluetooth is available
      if (await FlutterBluePlus.isSupported == false) {
        throw Exception('Bluetooth not available');
      }

      // Check if Bluetooth is on
      if (await FlutterBluePlus.adapterState.first !=
          BluetoothAdapterState.on) {
        throw Exception('Bluetooth is not turned on');
      }

      // Start scanning
      await FlutterBluePlus.startScan(
        timeout: timeout,
        withServices: [Guid(_chargerServiceUuid)], // Look for charger service
      );

      _scanSubscription = FlutterBluePlus.scanResults.listen((results) {
        for (ScanResult result in results) {
          _processDiscoveredDevice(result);
        }
      });

      // Add timeout handling
      Timer(timeout, () {
        if (_isScanning) {
          stopScanning();
        }
      });
    } catch (e) {
      _isScanning = false;
      throw Exception('Failed to start scanning: $e');
    }
  }

  /// Stop scanning for chargers
  Future<void> stopScanning() async {
    if (!_isScanning) return;

    _isScanning = false;
    await FlutterBluePlus.stopScan();
    await _scanSubscription?.cancel();
    _scanSubscription = null;
  }

  /// Process discovered Bluetooth device
  void _processDiscoveredDevice(ScanResult result) {
    final device = result.device;
    final advertisementData = result.advertisementData;

    // Check if this is an EV charger
    if (!_isEVCharger(advertisementData)) return;

    // Extract charger information
    final chargerInfo = _extractChargerInfo(device, result);

    // Check if already discovered
    final existingIndex = _discoveredChargers.indexWhere(
      (c) => c.id == chargerInfo.id,
    );
    if (existingIndex >= 0) {
      // Update existing charger with new signal strength
      _discoveredChargers[existingIndex] = chargerInfo;
    } else {
      // Add new charger
      _discoveredChargers.add(chargerInfo);
    }

    _discoveredChargersController.add(List.from(_discoveredChargers));
  }

  /// Check if device is an EV charger
  bool _isEVCharger(AdvertisementData advertisementData) {
    // Check for charger service UUID
    if (advertisementData.serviceUuids.contains(Guid(_chargerServiceUuid))) {
      return true;
    }

    // Check for manufacturer data indicating EV charger
    final manufacturerData = advertisementData.manufacturerData;
    for (final entry in manufacturerData.entries) {
      final data = entry.value;
      if (data.length >= 2) {
        // Check for EV charger manufacturer IDs (examples)
        final manufacturerId = entry.key;
        if ([0x02E5, 0x0006, 0x004C].contains(manufacturerId)) {
          // ABB, Microsoft, Apple (examples)
          return true;
        }
      }
    }

    // Check device name patterns
    final deviceName = advertisementData.advName.toLowerCase();
    final chargerPatterns = [
      'evse',
      'charger',
      'ev-',
      'terra',
      'wallbox',
      'fastcharge',
    ];
    return chargerPatterns.any((pattern) => deviceName.contains(pattern));
  }

  /// Extract charger information from scan result
  ChargerInfo _extractChargerInfo(BluetoothDevice device, ScanResult result) {
    final advertisementData = result.advertisementData;
    final deviceName = advertisementData.advName.isNotEmpty
        ? advertisementData.advName
        : device.platformName.isNotEmpty
        ? device.platformName
        : 'Unknown Charger';

    // Extract additional info from manufacturer data
    String? model;
    String? manufacturer;
    String? serialNumber;

    final manufacturerData = advertisementData.manufacturerData;
    for (final entry in manufacturerData.entries) {
      final data = entry.value;
      if (data.length >= 8) {
        // Try to decode manufacturer-specific data
        try {
          final decoded = utf8.decode(data, allowMalformed: true);
          if (decoded.contains('|')) {
            final parts = decoded.split('|');
            if (parts.length >= 3) {
              manufacturer = parts[0];
              model = parts[1];
              serialNumber = parts[2];
            }
          }
        } catch (e) {
          // Ignore decode errors
        }
      }
    }

    return ChargerInfo(
      id: device.remoteId.toString(),
      serialNumber:
          serialNumber ?? _generateSerialFromMac(device.remoteId.toString()),
      manufacturer: manufacturer ?? _guessManufacturer(deviceName),
      model: model ?? _guessModel(deviceName),
      firmwareVersion: '1.0.0', // Default firmware version
      connectors: [
        Connector(
          id: '1',
          type: ChargerType.acType2,
          maxPower: 22.0,
          maxCurrent: 32.0,
          status: ChargerStatus.available,
        ),
      ],
      status: ChargerStatus.available,
      lastSeen: DateTime.now(),
      metadata: {
        'connectionType': 'bluetooth',
        'macAddress': device.remoteId.toString(),
        'signalStrength': result.rssi,
        'discoveredAt': DateTime.now().toIso8601String(),
        'advertisementData': {
          'advName': advertisementData.advName,
          'txPowerLevel': advertisementData.txPowerLevel,
          'serviceUuids': advertisementData.serviceUuids
              .map((uuid) => uuid.toString())
              .toList(),
          'manufacturerData': manufacturerData.map(
            (key, value) => MapEntry(key.toString(), value.toString()),
          ),
        },
      },
    );
  }

  /// Connect to a specific charger
  Future<bool> connectToCharger(String chargerId) async {
    try {
      final charger = _discoveredChargers.firstWhere((c) => c.id == chargerId);
      final macAddress =
          charger.metadata?['macAddress'] as String? ?? chargerId;
      final device = BluetoothDevice.fromId(macAddress);

      _connectionStatusController.add(
        'Connecting to ${charger.manufacturer} ${charger.model}...',
      );

      // Connect to device
      await device.connect(timeout: const Duration(seconds: 15));
      _connectedDevice = device;

      _connectionStatusController.add('Discovering services...');

      // Discover services
      final services = await device.discoverServices();

      // Find charger service
      BluetoothService? chargerService;
      for (final service in services) {
        if (service.uuid.toString().toLowerCase() ==
            _chargerServiceUuid.toLowerCase()) {
          chargerService = service;
          break;
        }
      }

      if (chargerService == null) {
        throw Exception('Charger service not found');
      }

      _connectionStatusController.add('Setting up characteristics...');

      // Find characteristics
      for (final characteristic in chargerService.characteristics) {
        final uuid = characteristic.uuid.toString().toLowerCase();
        if (uuid == _configCharacteristicUuid.toLowerCase()) {
          _configCharacteristic = characteristic;
        } else if (uuid == _statusCharacteristicUuid.toLowerCase()) {
          _statusCharacteristic = characteristic;

          // Subscribe to status notifications
          await characteristic.setNotifyValue(true);
          _statusSubscription = characteristic.lastValueStream.listen(
            _handleStatusUpdate,
          );
        }
      }

      if (_configCharacteristic == null || _statusCharacteristic == null) {
        throw Exception('Required characteristics not found');
      }

      _isConnected = true;
      _connectionStatusController.add('Connected successfully');

      // Request initial status
      await _requestChargerStatus();

      return true;
    } catch (e) {
      _connectionStatusController.add('Connection failed: $e');
      await _disconnect();
      return false;
    }
  }

  /// Disconnect from charger
  Future<void> disconnect() async {
    await _disconnect();
    _connectionStatusController.add('Disconnected');
  }

  /// Internal disconnect method
  Future<void> _disconnect() async {
    await _statusSubscription?.cancel();
    _statusSubscription = null;

    if (_connectedDevice != null) {
      await _connectedDevice!.disconnect();
      _connectedDevice = null;
    }

    _configCharacteristic = null;
    _statusCharacteristic = null;
    _isConnected = false;
  }

  /// Configure charger WiFi settings
  Future<bool> configureWiFi({
    required String ssid,
    required String password,
    String? staticIP,
    String? gateway,
    String? dns,
  }) async {
    if (!_isConnected || _configCharacteristic == null) {
      throw Exception('Not connected to charger');
    }

    try {
      final config = {
        'action': 'configure_wifi',
        'ssid': ssid,
        'password': password,
        if (staticIP != null) 'static_ip': staticIP,
        if (gateway != null) 'gateway': gateway,
        if (dns != null) 'dns': dns,
      };

      final configData = utf8.encode(jsonEncode(config));
      await _configCharacteristic!.write(configData);

      _connectionStatusController.add('WiFi configuration sent');
      return true;
    } catch (e) {
      _connectionStatusController.add('WiFi configuration failed: $e');
      return false;
    }
  }

  /// Configure OCPP settings
  Future<bool> configureOCPP({
    required String centralSystemUrl,
    required String chargePointId,
    String? authKey,
    int heartbeatInterval = 300,
  }) async {
    if (!_isConnected || _configCharacteristic == null) {
      throw Exception('Not connected to charger');
    }

    try {
      final config = {
        'action': 'configure_ocpp',
        'central_system_url': centralSystemUrl,
        'charge_point_id': chargePointId,
        if (authKey != null) 'auth_key': authKey,
        'heartbeat_interval': heartbeatInterval,
      };

      final configData = utf8.encode(jsonEncode(config));
      await _configCharacteristic!.write(configData);

      _connectionStatusController.add('OCPP configuration sent');
      return true;
    } catch (e) {
      _connectionStatusController.add('OCPP configuration failed: $e');
      return false;
    }
  }

  /// Request charger status
  Future<void> _requestChargerStatus() async {
    if (_configCharacteristic == null) return;

    try {
      final request = {'action': 'get_status'};
      final requestData = utf8.encode(jsonEncode(request));
      await _configCharacteristic!.write(requestData);
    } catch (e) {
      _connectionStatusController.add('Status request failed: $e');
    }
  }

  /// Handle status updates from charger
  void _handleStatusUpdate(List<int> data) {
    try {
      final statusJson = utf8.decode(data);
      final status = jsonDecode(statusJson) as Map<String, dynamic>;

      _connectionStatusController.add(
        'Status: ${status['status'] ?? 'Unknown'}',
      );

      // Handle specific status updates
      if (status.containsKey('wifi_connected')) {
        final wifiConnected = status['wifi_connected'] as bool;
        _connectionStatusController.add(
          wifiConnected ? 'WiFi connected' : 'WiFi disconnected',
        );
      }

      if (status.containsKey('ocpp_connected')) {
        final ocppConnected = status['ocpp_connected'] as bool;
        _connectionStatusController.add(
          ocppConnected ? 'OCPP connected' : 'OCPP disconnected',
        );
      }
    } catch (e) {
      _connectionStatusController.add('Failed to parse status: $e');
    }
  }

  /// Generate serial number from MAC address
  String _generateSerialFromMac(String mac) {
    return 'CHG${mac.replaceAll(':', '').substring(0, 6).toUpperCase()}';
  }

  /// Guess manufacturer from device name
  String _guessManufacturer(String deviceName) {
    final name = deviceName.toLowerCase();
    if (name.contains('abb') || name.contains('terra')) return 'ABB';
    if (name.contains('wallbox')) return 'Wallbox';
    if (name.contains('evse')) return 'EVSE Solutions';
    if (name.contains('siemens')) return 'Siemens';
    if (name.contains('schneider')) return 'Schneider Electric';
    return 'Unknown';
  }

  /// Guess model from device name
  String _guessModel(String deviceName) {
    final name = deviceName.toLowerCase();
    if (name.contains('terra')) return 'Terra AC';
    if (name.contains('wallbox')) return 'Pulsar Plus';
    if (name.contains('fastcharge')) return 'FastCharge Pro';
    if (name.contains('ac22')) return 'AC22';
    if (name.contains('dc')) return 'DC Fast Charger';
    return 'Unknown Model';
  }

  /// Dispose resources
  void dispose() {
    stopScanning();
    _disconnect();
    _discoveredChargersController.close();
    _connectionStatusController.close();
  }
}
