import 'package:freezed_annotation/freezed_annotation.dart';

part 'ocpp_credentials.freezed.dart';
part 'ocpp_credentials.g.dart';

/// Represents OCPP protocol versions
enum OCPPVersion {
  @JsonValue('1.6')
  v16,
  @JsonValue('2.0')
  v20,
  @JsonValue('2.0.1')
  v201,
}

/// Represents authentication types for OCPP
enum OCPPAuthType {
  @JsonValue('basic')
  basic,
  @JsonValue('certificate')
  certificate,
  @JsonValue('token')
  token,
}

/// Represents OCPP connection credentials
@freezed
class OCPPCredentials with _$OCPPCredentials {
  const factory OCPPCredentials({
    required String chargePointId,
    required String cmsUrl,
    required OCPPVersion version,
    required OCPPAuthType authType,
    String? username,
    String? password,
    String? certificatePath,
    String? privateKeyPath,
    String? caCertificatePath,
    String? authToken,
    Map<String, String>? headers,
    int? heartbeatInterval,
    int? connectionTimeout,
    bool? useSSL,
  }) = _OCPPCredentials;

  factory OCPPCredentials.fromJson(Map<String, dynamic> json) =>
      _$OCPPCredentialsFromJson(json);
}

/// Represents OCPP message types
enum OCPPMessageType {
  @JsonValue('call')
  call,
  @JsonValue('callresult')
  callResult,
  @JsonValue('callerror')
  callError,
}

/// Represents an OCPP message
@freezed
class OCPPMessage with _$OCPPMessage {
  const factory OCPPMessage({
    required OCPPMessageType messageType,
    required String messageId,
    required String action,
    required Map<String, dynamic> payload,
    DateTime? timestamp,
    String? errorCode,
    String? errorDescription,
  }) = _OCPPMessage;

  factory OCPPMessage.fromJson(Map<String, dynamic> json) =>
      _$OCPPMessageFromJson(json);
}

/// Represents OCPP connection status
@freezed
class OCPPConnectionStatus with _$OCPPConnectionStatus {
  const factory OCPPConnectionStatus({
    required bool isConnected,
    required bool isRegistered,
    DateTime? connectedAt,
    DateTime? lastHeartbeat,
    String? sessionId,
    String? errorMessage,
    int? reconnectAttempts,
    Map<String, dynamic>? connectionInfo,
  }) = _OCPPConnectionStatus;

  factory OCPPConnectionStatus.fromJson(Map<String, dynamic> json) =>
      _$OCPPConnectionStatusFromJson(json);
}

/// Represents a charging transaction
@freezed
class ChargingTransaction with _$ChargingTransaction {
  const factory ChargingTransaction({
    required String transactionId,
    required String connectorId,
    required String idTag,
    required DateTime startTime,
    required double startMeterValue,
    DateTime? stopTime,
    double? stopMeterValue,
    String? stopReason,
    double? energyDelivered,
    double? cost,
    Map<String, dynamic>? metadata,
  }) = _ChargingTransaction;

  factory ChargingTransaction.fromJson(Map<String, dynamic> json) =>
      _$ChargingTransactionFromJson(json);
}
