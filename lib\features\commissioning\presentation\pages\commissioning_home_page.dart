import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../widgets/commissioning_feature_card.dart';
import '../widgets/active_workflows_widget.dart';
import '../widgets/quick_stats_widget.dart';

/// Main home page for EV commissioning operations
class CommissioningHomePage extends StatelessWidget {
  static const String routeName = '/commissioning-home';

  const CommissioningHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('EV Commissioning'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () => _showNotifications(context),
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettings(context),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(context, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: ListTile(
                  leading: Icon(Icons.person),
                  title: Text('Profile'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'help',
                child: ListTile(
                  leading: Icon(Icons.help),
                  title: Text('Help'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'logout',
                child: ListTile(
                  leading: Icon(Icons.logout),
                  title: Text('Logout'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh data
          await Future.delayed(const Duration(seconds: 1));
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeSection(context),
              const SizedBox(height: 24),
              const QuickStatsWidget(),
              const SizedBox(height: 24),
              _buildQuickActionsSection(context),
              const SizedBox(height: 24),
              _buildCommissioningFeaturesSection(context),
              const SizedBox(height: 24),
              const ActiveWorkflowsWidget(),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _startNewCommissioning(context),
        icon: const Icon(Icons.add),
        label: const Text('New Commissioning'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.ev_station,
                size: 32,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome to EV Commissioning',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Streamline your charging station deployment',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                context,
                'Scan QR Code',
                Icons.qr_code_scanner,
                () => _navigateToQRScanner(context),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                context,
                'Discover Chargers',
                Icons.bluetooth_searching,
                () => _navigateToDiscovery(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(icon, size: 32, color: Theme.of(context).primaryColor),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommissioningFeaturesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Commissioning Features',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            CommissioningFeatureCard(
              title: 'Charger Discovery',
              subtitle: 'Find & connect to chargers',
              icon: Icons.search,
              onTap: () => _navigateToDiscovery(context),
            ),
            CommissioningFeatureCard(
              title: 'Network Config',
              subtitle: 'Configure Wi-Fi & connectivity',
              icon: Icons.wifi,
              onTap: () => _navigateToNetworkConfig(context),
            ),
            CommissioningFeatureCard(
              title: 'OCPP Integration',
              subtitle: 'Connect to CMS',
              icon: Icons.cloud_sync,
              onTap: () => _navigateToOCPP(context),
            ),
            CommissioningFeatureCard(
              title: 'Firmware Update',
              subtitle: 'Manage firmware versions',
              icon: Icons.system_update,
              onTap: () => _navigateToFirmware(context),
            ),
            CommissioningFeatureCard(
              title: 'Parameter Config',
              subtitle: 'Set charging parameters',
              icon: Icons.tune,
              onTap: () => _navigateToParameters(context),
            ),
            CommissioningFeatureCard(
              title: 'Diagnostics',
              subtitle: 'Monitor & troubleshoot',
              icon: Icons.analytics,
              onTap: () => _navigateToDiagnostics(context),
            ),
          ],
        ),
      ],
    );
  }

  void _showNotifications(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notifications feature coming soon')),
    );
  }

  void _showSettings(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings feature coming soon')),
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'profile':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile feature coming soon')),
        );
        break;
      case 'help':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Help feature coming soon')),
        );
        break;
      case 'logout':
        context.read<AuthBloc>().add(const AuthLogoutRequested());
        break;
    }
  }

  void _startNewCommissioning(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/workflow/new');
  }

  void _navigateToQRScanner(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/discovery/qr-scanner');
  }

  void _navigateToDiscovery(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/discovery');
  }

  void _navigateToNetworkConfig(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/network-config');
  }

  void _navigateToOCPP(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/ocpp');
  }

  void _navigateToFirmware(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/firmware');
  }

  void _navigateToParameters(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/parameters');
  }

  void _navigateToDiagnostics(BuildContext context) {
    Navigator.pushNamed(context, '/commissioning/diagnostics');
  }
}
