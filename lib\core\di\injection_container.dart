import 'package:get_it/get_it.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';

import '../storage/secure_storage_service.dart';
import '../logging/app_logger.dart';
import '../../features/auth/domain/repositories/auth_repository.dart';
import '../../features/auth/data/repositories/auth_repository_impl.dart';
import '../../features/auth/domain/usecases/login_usecase.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';

// Commissioning imports will be added as implementations are ready

final GetIt getIt = GetIt.instance;

// @InjectableInit() - Commented out since we're using manual DI for now
// Future<void> configureDependencies() async => getIt.init();

/// Manual dependency injection setup
/// This will be replaced by generated code when using injectable
Future<void> setupDependencies() async {
  // Core services
  getIt.registerLazySingleton<Logger>(
    () => Logger(
      printer: Pretty<PERSON>rinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
    ),
  );

  getIt.registerLazySingleton<AppLogger>(() => AppLogger(getIt<Logger>()));

  // Storage
  getIt.registerLazySingleton<FlutterSecureStorage>(
    () => const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    ),
  );

  getIt.registerLazySingleton<SecureStorageService>(
    () => SecureStorageService(getIt<FlutterSecureStorage>()),
  );

  // Network (Mock only - no real API)
  // Removed Dio and DioClient since we're using mock data only

  // Auth Repository
  getIt.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl());

  // Auth Use Cases
  getIt.registerLazySingleton<LoginUseCase>(
    () => LoginUseCase(getIt<AuthRepository>()),
  );

  // Auth BLoC
  getIt.registerFactory<AuthBloc>(
    () => AuthBloc(
      loginUseCase: getIt<LoginUseCase>(),
      authRepository: getIt<AuthRepository>(),
    ),
  );

  // TODO: Register commissioning services when implementations are ready
  // This will be expanded as we implement each commissioning module
  // getIt.registerLazySingleton<CommissioningOrchestrator>(() => ...);
}

/// Clean up dependencies
Future<void> cleanupDependencies() async {
  await getIt.reset();
}
