/// Application-wide constants
class AppConstants {
  // App Information
  static const String appName = 'EV Commissioning App';
  static const String appVersion = '1.0.0';

  // Mock Configuration (No real API endpoints)
  static const bool useMockData = true;
  static const int mockDelay = 1000; // 1 second delay for realistic simulation

  // OCPP Configuration
  static const String ocppVersion16 = '1.6';
  static const String ocppVersion20 = '2.0.1';
  static const int ocppHeartbeatInterval = 300; // 5 minutes

  // Network Configuration
  static const int networkScanTimeout = 10000; // 10 seconds
  static const int pingTimeout = 5000; // 5 seconds
  static const String defaultSubnetMask = '255.255.255.0';

  // Security
  static const int tokenExpiryBuffer = 300; // 5 minutes before expiry
  static const int maxLoginAttempts = 3;
  static const int sessionTimeoutMinutes = 30;

  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;

  // File Limits
  static const int maxImageSizeMB = 10;
  static const int maxReportSizeMB = 50;

  // Charger Configuration
  static const int maxChargersPerBatch = 50;
  static const int defaultMaxCurrent = 32; // Amperes
  static const int minChargingPower = 3; // kW
  static const int maxChargingPower = 350; // kW

  // Database
  static const String databaseName = 'ev_commissioning.db';
  static const int databaseVersion = 1;

  // Storage Keys
  static const String keyAccessToken = 'access_token';
  static const String keyRefreshToken = 'refresh_token';
  static const String keyUserProfile = 'user_profile';
  static const String keyAppSettings = 'app_settings';
  static const String keyOfflineData = 'offline_data';

  // Error Messages
  static const String networkErrorMessage =
      'Network connection error. Please check your internet connection.';
  static const String serverErrorMessage =
      'Server error occurred. Please try again later.';
  static const String authErrorMessage =
      'Authentication failed. Please login again.';
  static const String permissionErrorMessage =
      'Required permissions not granted.';

  // Success Messages
  static const String commissioningSuccessMessage =
      'Charger commissioned successfully!';
  static const String configurationSavedMessage =
      'Configuration saved successfully!';
  static const String reportGeneratedMessage = 'Report generated successfully!';
}
