import 'package:flutter/material.dart';
import '../../domain/entities/commissioning_workflow.dart';

/// Widget to display commissioning workflow progress
class CommissioningProgressWidget extends StatelessWidget {
  final CommissioningWorkflow workflow;
  final VoidCallback? onStepTap;
  final bool showDetails;

  const CommissioningProgressWidget({
    super.key,
    required this.workflow,
    this.onStepTap,
    this.showDetails = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildProgressIndicator(context),
            const SizedBox(height: 16),
            if (showDetails) _buildStepsList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(Icons.ev_station, color: Theme.of(context).primaryColor, size: 24),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Commissioning Progress',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              Text(
                'Charger ID: ${workflow.chargerId}',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        _buildStatusChip(context),
      ],
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    final isCompleted = workflow.isCompleted ?? false;
    final color = isCompleted ? Colors.green : Colors.blue;
    final text = isCompleted ? 'Completed' : 'In Progress';

    return Chip(
      label: Text(
        text,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    final progress = workflow.progressPercentage ?? 0.0;
    final progressText = '${(progress * 100).toInt()}%';

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Overall Progress',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              progressText,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStepsList(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Steps', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        ...workflow.steps.map((step) => _buildStepItem(context, step)),
      ],
    );
  }

  Widget _buildStepItem(BuildContext context, WorkflowStep step) {
    return InkWell(
      onTap: onStepTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            _buildStepIcon(step.status),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    step.name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (step.description.isNotEmpty)
                    Text(
                      step.description,
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                  if (step.errorMessage != null)
                    Text(
                      step.errorMessage!,
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.red),
                    ),
                ],
              ),
            ),
            if (step.estimatedDurationMinutes != null)
              Text(
                '${step.estimatedDurationMinutes}min',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepIcon(StepStatus status) {
    switch (status) {
      case StepStatus.completed:
        return const Icon(Icons.check_circle, color: Colors.green, size: 20);
      case StepStatus.inProgress:
        return const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case StepStatus.failed:
        return const Icon(Icons.error, color: Colors.red, size: 20);
      case StepStatus.skipped:
        return const Icon(Icons.skip_next, color: Colors.orange, size: 20);
      case StepStatus.cancelled:
        return const Icon(Icons.cancel, color: Colors.grey, size: 20);
      case StepStatus.pending:
        return Icon(
          Icons.radio_button_unchecked,
          color: Colors.grey[400],
          size: 20,
        );
    }
  }
}

/// Compact version of the commissioning progress widget
class CompactCommissioningProgressWidget extends StatelessWidget {
  final CommissioningWorkflow workflow;
  final VoidCallback? onTap;

  const CompactCommissioningProgressWidget({
    super.key,
    required this.workflow,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final progress = workflow.progressPercentage ?? 0.0;
    final currentStepName = workflow.steps
        .firstWhere(
          (step) => step.step == workflow.currentStep,
          orElse: () => workflow.steps.first,
        )
        .name;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.ev_station,
                  size: 16,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    workflow.chargerId,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  '${(progress * 100).toInt()}%',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              currentStepName,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
