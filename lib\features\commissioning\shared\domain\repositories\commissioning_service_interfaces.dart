import 'package:dartz/dartz.dart';
import '../../../../../core/error/failures.dart';
import '../entities/charger_info.dart';
import '../entities/network_config.dart';
import '../entities/ocpp_credentials.dart';
import '../entities/firmware_info.dart';

/// Base interface for all commissioning services
abstract class CommissioningService {
  /// Initialize the service
  Future<Either<Failure, bool>> initialize();

  /// Cleanup resources
  Future<void> dispose();

  /// Check if service is available
  Future<bool> isAvailable();

  /// Get service status
  Future<Map<String, dynamic>> getStatus();
}

/// Interface for charger discovery operations
abstract class ChargerDiscoveryService extends CommissioningService {
  /// Scan for available chargers using multiple methods
  Stream<List<DiscoveredCharger>> scanForChargers({
    Duration? timeout,
    List<String>? connectionTypes,
  });

  /// Connect to a specific charger
  Future<Either<Failure, ChargerConnection>> connectToCharger(
    String chargerId, {
    Map<String, dynamic>? connectionParams,
  });

  /// Disconnect from charger
  Future<Either<Failure, bool>> disconnectFromCharger(String chargerId);

  /// Validate charger credentials
  Future<Either<Failure, bool>> validateChargerCredentials(
    ChargerConnection connection,
  );

  /// Get charger information
  Future<Either<Failure, ChargerInfo>> getChargerInfo(
    ChargerConnection connection,
  );

  /// Parse QR code data
  Either<Failure, Map<String, String>> parseQRCode(String qrData);

  /// Validate serial number format
  Either<Failure, bool> validateSerialNumber(String serialNumber);
}

/// Interface for network configuration operations
abstract class NetworkConfigurationService extends CommissioningService {
  /// Scan for available Wi-Fi networks
  Future<Either<Failure, List<WifiNetwork>>> scanWifiNetworks({
    Duration? timeout,
  });

  /// Configure charger network settings
  Future<Either<Failure, bool>> configureChargerNetwork(
    ChargerConnection charger,
    NetworkConfig config,
  );

  /// Test network connectivity
  Future<Either<Failure, NetworkStatus>> testConnectivity(
    ChargerConnection charger,
  );

  /// Synchronize time with NTP servers
  Future<Either<Failure, bool>> synchronizeTime(
    ChargerConnection charger, {
    String? ntpServer,
  });

  /// Configure grid parameters
  Future<Either<Failure, bool>> configureGridParameters(
    ChargerConnection charger,
    GridConfig gridConfig,
  );

  /// Get current network status
  Future<Either<Failure, NetworkStatus>> getNetworkStatus(
    ChargerConnection charger,
  );
}

/// Interface for OCPP integration operations
abstract class OCPPService extends CommissioningService {
  /// Establish connection to CMS
  Future<Either<Failure, bool>> establishConnection(
    String cmsUrl,
    OCPPCredentials credentials,
  );

  /// Register charger with CMS
  Future<Either<Failure, bool>> registerCharger(
    ChargerInfo charger,
    OCPPCredentials credentials,
  );

  /// Listen to OCPP messages
  Stream<OCPPMessage> listenToMessages();

  /// Send OCPP message
  Future<Either<Failure, OCPPMessage>> sendMessage(OCPPMessage message);

  /// Start charging transaction
  Future<Either<Failure, ChargingTransaction>> startTransaction(
    String connectorId,
    String idTag,
  );

  /// Stop charging transaction
  Future<Either<Failure, bool>> stopTransaction(String transactionId);

  /// Get connection status
  Future<Either<Failure, OCPPConnectionStatus>> getConnectionStatus();

  /// Send heartbeat
  Future<Either<Failure, bool>> sendHeartbeat();

  /// Handle boot notification
  Future<Either<Failure, bool>> sendBootNotification(ChargerInfo charger);
}

/// Interface for firmware management operations
abstract class FirmwareService extends CommissioningService {
  /// Check for firmware updates
  Future<Either<Failure, FirmwareInfo?>> checkForUpdates(
    String chargerModel,
    String currentVersion,
  );

  /// Download firmware
  Stream<DownloadProgress> downloadFirmware(String firmwareUrl);

  /// Install firmware on charger
  Future<Either<Failure, bool>> installFirmware(
    ChargerConnection charger,
    String firmwarePath,
  );

  /// Get firmware installation status
  Stream<InstallationProgress> getInstallationProgress(String chargerId);

  /// Verify firmware integrity
  Future<Either<Failure, bool>> verifyFirmware(
    String firmwarePath,
    String expectedChecksum,
  );

  /// Get current firmware status
  Future<Either<Failure, FirmwareStatus>> getFirmwareStatus(
    ChargerConnection charger,
  );

  /// Rollback firmware to previous version
  Future<Either<Failure, bool>> rollbackFirmware(ChargerConnection charger);
}

/// Interface for parameter configuration operations
abstract class ParameterConfigurationService extends CommissioningService {
  /// Get all configurable parameters
  Future<Either<Failure, Map<String, dynamic>>> getParameters(
    ChargerConnection charger,
  );

  /// Set parameter value
  Future<Either<Failure, bool>> setParameter(
    ChargerConnection charger,
    String key,
    dynamic value,
  );

  /// Set multiple parameters
  Future<Either<Failure, Map<String, bool>>> setParameters(
    ChargerConnection charger,
    Map<String, dynamic> parameters,
  );

  /// Validate parameter value
  Either<Failure, bool> validateParameter(
    String key,
    dynamic value,
    Map<String, dynamic>? constraints,
  );

  /// Get parameter constraints
  Future<Either<Failure, Map<String, dynamic>>> getParameterConstraints(
    ChargerConnection charger,
    String key,
  );

  /// Reset parameters to default
  Future<Either<Failure, bool>> resetToDefaults(
    ChargerConnection charger,
    List<String>? parameterKeys,
  );
}

/// Interface for diagnostics operations
abstract class DiagnosticsService extends CommissioningService {
  /// Get real-time charger status
  Stream<ChargerStatus> getRealtimeStatus(ChargerConnection charger);

  /// Get diagnostic logs
  Future<Either<Failure, List<Map<String, dynamic>>>> getDiagnosticLogs(
    ChargerConnection charger, {
    DateTime? startTime,
    DateTime? endTime,
    String? logLevel,
  });

  /// Get usage statistics
  Future<Either<Failure, Map<String, dynamic>>> getUsageStatistics(
    ChargerConnection charger, {
    DateTime? startTime,
    DateTime? endTime,
  });

  /// Run diagnostic tests
  Future<Either<Failure, Map<String, dynamic>>> runDiagnosticTests(
    ChargerConnection charger,
    List<String> testTypes,
  );

  /// Get active alerts
  Future<Either<Failure, List<Map<String, dynamic>>>> getActiveAlerts(
    ChargerConnection charger,
  );

  /// Clear alerts
  Future<Either<Failure, bool>> clearAlerts(
    ChargerConnection charger,
    List<String> alertIds,
  );
}
