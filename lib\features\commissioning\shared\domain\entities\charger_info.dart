import 'package:freezed_annotation/freezed_annotation.dart';

part 'charger_info.freezed.dart';
part 'charger_info.g.dart';

/// Represents the type of EV charging station
enum ChargerType {
  @JsonValue('ac_type1')
  acType1,
  @JsonValue('ac_type2')
  acType2,
  @JsonValue('dc_ccs')
  dcCcs,
  @JsonValue('dc_chademo')
  dcChademo,
  @JsonValue('dc_combo')
  dcCombo,
}

/// Represents the current status of a charging station
enum ChargerStatus {
  @JsonValue('available')
  available,
  @JsonValue('occupied')
  occupied,
  @JsonValue('reserved')
  reserved,
  @JsonValue('unavailable')
  unavailable,
  @JsonValue('faulted')
  faulted,
  @JsonValue('preparing')
  preparing,
  @JsonValue('charging')
  charging,
  @JsonValue('suspended_evse')
  suspendedEvse,
  @JsonValue('suspended_ev')
  suspendedEv,
  @JsonValue('finishing')
  finishing,
  @JsonValue('offline')
  offline,
}

/// Represents a connector on a charging station
@freezed
class Connector with _$Connector {
  const factory Connector({
    required String id,
    required ChargerType type,
    required double maxPower,
    required double maxCurrent,
    required ChargerStatus status,
    String? currentTransactionId,
    DateTime? lastUsed,
  }) = _Connector;

  factory Connector.fromJson(Map<String, dynamic> json) =>
      _$ConnectorFromJson(json);
}

/// Core entity representing an EV charging station
@freezed
class ChargerInfo with _$ChargerInfo {
  const factory ChargerInfo({
    required String id,
    required String serialNumber,
    required String model,
    required String manufacturer,
    required String firmwareVersion,
    required List<Connector> connectors,
    required ChargerStatus status,
    required DateTime lastSeen,
    String? location,
    String? description,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ChargerInfo;

  factory ChargerInfo.fromJson(Map<String, dynamic> json) =>
      _$ChargerInfoFromJson(json);
}

/// Represents a discovered charger during scanning
@freezed
class DiscoveredCharger with _$DiscoveredCharger {
  const factory DiscoveredCharger({
    required String id,
    required String name,
    required String serialNumber,
    required int signalStrength,
    required String connectionType, // 'bluetooth', 'wifi', 'ethernet'
    String? macAddress,
    String? ipAddress,
    Map<String, dynamic>? advertisementData,
    DateTime? discoveredAt,
  }) = _DiscoveredCharger;

  factory DiscoveredCharger.fromJson(Map<String, dynamic> json) =>
      _$DiscoveredChargerFromJson(json);
}

/// Represents an active connection to a charging station
@freezed
class ChargerConnection with _$ChargerConnection {
  const factory ChargerConnection({
    required String chargerId,
    required String connectionType,
    required bool isConnected,
    required DateTime connectedAt,
    String? sessionId,
    Map<String, dynamic>? connectionParams,
    DateTime? lastActivity,
  }) = _ChargerConnection;

  factory ChargerConnection.fromJson(Map<String, dynamic> json) =>
      _$ChargerConnectionFromJson(json);
}
